{"Dashboards": "Tableaux de bord", "auth.login.title": "Bienvenue sur ", "auth.login.description": "Veuillez vous connecter et commencer votre journée de travail.", "auth.login.form.email": "Nom d'utilisateur/Email", "auth.login.form.password": "Mot de passe", "auth.login.forgot_password": "Mot de passe oublié", "auth.login.button": "Se connecter", "auth.logout": "", "Analytics": "Analytique", "CRM": "GRC", "eCommerce": "commerce électronique", "Apps & Pages": "Applications et pages", "Apps": "Apps", "Email": "E-mail", "Chat": "Discuter", "Calendar": "<PERSON><PERSON><PERSON>", "Invoice": "Facture d'achat", "List": "Liste", "Preview": "<PERSON><PERSON><PERSON><PERSON>", "Edit": "É<PERSON>er", "Add": "Ajouter", "User": "Utilisa<PERSON>ur", "View": "Voir", "Security": "Sécurité", "Billing & Plans": "Facturation et forfaits", "Notifications": "Notifications", "Connection": "<PERSON><PERSON>", "Roles & Permissions": "Rôles et autorisations", "Roles": "Les rôles", "Permissions": "Autorisations", "Pages": "Pages", "User Profile": "Profil de l'utilisateur", "Profile": "Profil", "Teams": "Équipes", "Projects": "Projets", "Connections": "Connexions", "Account Settings": "Paramètres du compte", "Account": "<PERSON><PERSON><PERSON>", "Billing": "Facturation", "FAQ": "FAQ", "Help Center": "Centre d'aide", "Pricing": "Prix", "Miscellaneous": "Divers", "Coming Soon": "À venir", "Under Maintenance": "En maintenance", "Page Not Found - 404": "Page non trouvée - 404", "Not Authorized - 401": "Non autorisé - 401", "Server Error - 500": "Erreur de serveur - 500", "Auth Pages": "Pages d'authentification", "Login": "Connexion", "Login v1": "Connexion v1", "Login v2": "Connexion v2", "Login With AppBar": "Connexion avec AppBar", "Register": "S'inscrire", "Register v1": "Enregistrer v1", "Register v2": "Enregistrer v2", "Register Multi-Steps": "Enregistrer plusieurs étapes", "Verify Email": "Vérifier les courriels", "Verify Email v1": "Vérifier l'e-mail v1", "Verify Email v2": "Vérifier l'e-mail v2", "Forgot Password": "Mot de passe oublié", "Forgot Password v1": "Mot de passe oublié v1", "Forgot Password v2": "Mot de passe oublié v2", "Reset Password": "Réinitialiser le mot de passe", "Reset Password v1": "Réinitialiser le mot de passe v1", "Reset Password v2": "Réinitialiser le mot de passe v2", "Two Steps": "Deux étapes", "Two Steps v1": "Deux étapes v1", "Two Steps v2": "Deux étapes v2", "Wizard Examples": "Exemples d'assistants", "Checkout": "Vérifier", "Property Listing": "Liste des biens", "Create Deal": "Créer un accord", "Dialog Examples": "Exemples de dialogue", "User Interface": "Interface utilisateur", "UI": "UI", "Typography": "Typographie", "Icons": "Icônes", "Cards": "<PERSON><PERSON>", "Basic": "De base", "Advanced": "<PERSON><PERSON><PERSON>", "Statistics": "Statistiques", "Widgets": "Widgets", "Actions": "Actions", "Components": "Composants", "Accordion": "Accord<PERSON><PERSON>", "Alerts": "<PERSON><PERSON><PERSON>", "Avatars": "Avatars", "Badges": "Insignes", "Buttons": "Boutons", "Button Group": "Groupe de boutons", "Chips": "<PERSON><PERSON><PERSON>", "Dialogs": "Dialogues", "Menu": "<PERSON><PERSON>", "Pagination": "Pagination", "Progress": "Progrès", "Ratings": "Notes", "Snackbar": "Snack-bar", "Swiper": "<PERSON><PERSON><PERSON>", "Tabs": "Onglets", "Timeline": "Chronologie", "Toasts": "Toasts", "Tree View": "Vue arborescente", "More": "Suite", "Forms & Tables": "Formulaires et tableaux", "Form Elements": "Éléments de formulaire", "Text Field": "Champ de texte", "Select": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Checkbox": "Case à cocher", "Radio": "Radio", "Custom Inputs": "Entrées personnalisées", "Textarea": "Zone de texte", "Autocomplete": "Saisie automatique", "Date Pickers": "Sélecteurs de dates", "Switch": "Changer", "File Uploader": "Té<PERSON>cha<PERSON><PERSON>", "Editor": "<PERSON><PERSON><PERSON>", "Slider": "<PERSON>lis<PERSON><PERSON>", "Input Mask": "Masque de saisie", "Form Layouts": "Dispositions de formulaire", "Form Validation": "Validation du formulaire", "Form Wizard": "Assistant de formulaire", "Table": "Table", "Mui DataGrid": "Grille de données <PERSON>", "Charts & Misc": "Graphiques & Divers", "Charts": "Graphiques", "Apex": "<PERSON><PERSON><PERSON>", "Recharts": "Regraphiques", "ChartJS": "GraphiqueJS", "Access Control": "Contrôle d'accès", "Others": "Les autres", "Menu Levels": "Niveaux de menus", "Menu Level 2.1": "Niveau menu 2.1", "Menu Level 2.2": "Niveau menu 2.2", "Menu Level 3.1": "Niveau menu 3.1", "Menu Level 3.2": "Niveau menu 3.2", "Disabled Menu": "<PERSON><PERSON>", "Raise Support": "Augmenter le soutien", "Documentation": "Documentation", "menu:vehicle": "Gestion des véhicules", "menu:financial": "Gestion Financière", "financial:consumption": "Mission/ Consommation", "financial:services": " Prestation diverses", "financial:invoice": "Facture", "menu:operations": "Gestion des opérations", "operations:articles": "Articles", "operations:requests": "<PERSON><PERSON><PERSON>", "operations:extraction": "Extraction", "operations:articleGroups": "Groupes d'articles", "operations:articleTypes": "Types d'articles", "operations:operations": "Opérations", "operations:products": "Nature des marchandises", "operations:binRates": "Tarifs des bennes/Prix unitaire", "operations:standardRates": "Tarifs standards/Prix unitaire", "operations:trips": "Voyages", "menu:fuelConsumption": "Consommation de carburant", "menu:fuelConsumption:consumptionOperations": "Opérations de consommation", "menu:fuelConsumption:fuelConsumption": "Consommation de carburant", "table": {"search": "Rechercher...", "noData": "<PERSON><PERSON><PERSON> donnée disponible", "error": "Une erreur est survenue lors du chargement des données", "loading": "Chargement...", "create": "<PERSON><PERSON><PERSON>", "edit": "Modifier", "delete": "<PERSON><PERSON><PERSON><PERSON>", "actions": "Actions", "filter": "<PERSON><PERSON><PERSON>", "clearFilters": "Effacer les filtres", "rowsPerPage": "Lignes par page", "of": "sur", "next": "Suivant", "previous": "Précédent", "first": "Premier", "last": "<PERSON><PERSON>"}, "tables": {"routes": {"title": "Trajets", "searchPlaceholder": "Search trajet...", "createButton": " Ajouter un trajet", "deleteSuccess": "Route deleted successfully", "deleteError": "Error deleting route", "confirmDelete": "Are you sure you want to delete this route?", "deleteSelected": "Delete Selected", "deleteDialog": {"title": "Confirm Delete", "message": "Are you sure you want to delete this route?", "cancel": "Cancel", "confirm": "Confirm"}, "columns": {"name": "<PERSON><PERSON><PERSON>", "livraisonVille": "Livraison à la ville", "volumeCarburant": "Volume Carburant", "distance": "Distance", "duree": "<PERSON><PERSON><PERSON>", "fraisRoute": "Frais de Route", "nombreBons": "Nombre de Bons", "activee": "<PERSON>e", "modifiedBy": "Modified By", "modifiedAt": "Modified At"}}, "natureProduct": {"title": "Nature des Articles", "searchPlaceholder": "Rechercher des natures de marchandises...", "createButton": "Ajouter une nature de marchandise", "deleteSuccess": "Nature de marchandise supprimée avec succès", "deleteError": "Erreur lors de la suppression de la nature de marchandise", "confirmDelete": "Êtes-vous sûr de vouloir supprimer cette nature de marchandise?", "deleteSelected": "Supprimer la sélection", "deleteDialog": {"title": "Confirmer la <PERSON>", "message": "Êtes-vous sûr de vouloir supprimer cette nature de marchandise?", "cancel": "Annuler", "confirm": "Confirmer"}, "columns": {"name": "Libellé", "createdAt": "<PERSON><PERSON><PERSON>", "createdBy": "C<PERSON><PERSON> par", "modifiedBy": "Modifié par", "modifiedAt": "<PERSON><PERSON><PERSON><PERSON> le"}}, "articleGroup": {"title": "Groupes d'articles", "searchPlaceholder": "Rechercher des groupes d'articles...", "createButton": "Ajouter un groupe d'article", "deleteSuccess": "Groupe d'article supprimé avec succès", "deleteError": "Erreur lors de la suppression du groupe d'article", "confirmDelete": "Êtes-vous sûr de vouloir supprimer ce groupe d'article?", "deleteSelected": "Supprimer la sélection", "deleteDialog": {"title": "Confirmer la <PERSON>", "message": "Êtes-vous sûr de vouloir supprimer ce groupe d'article?", "cancel": "Annuler", "confirm": "Confirmer"}, "columns": {"code": "Code", "name": "Libellé du groupe", "modifiedBy": "Modifié par", "modifiedAt": "<PERSON><PERSON><PERSON><PERSON> le"}}, "station": {"title": "Stations", "searchPlaceholder": "Rechercher des stations...", "createButton": "Ajouter une station", "deleteSuccess": "Station supprimée avec succès", "deleteError": "Erreur lors de la suppression de la station", "confirmDelete": "Êtes-vous sûr de vouloir supprimer cette station?", "bulkCreateSuccess": "Stations créées avec succès", "bulkCreateError": "Erreur lors de la création des stations", "deleteDialog": {"title": "Confirmer la <PERSON>", "message": "Êtes-vous sûr de vouloir supprimer cette station?", "cancel": "Annuler", "confirm": "Confirmer"}, "columns": {"name": "Nom de la station", "address": "<PERSON><PERSON><PERSON>", "email": "Adresse électronique", "country": "Pays", "active": "Actif", "modifiedBy": "Modifié par", "modifiedAt": "<PERSON><PERSON><PERSON><PERSON> le", "createdAt": "<PERSON><PERSON><PERSON> le"}}, "users": {"title": "Utilisateurs", "searchPlaceholder": "Rechercher des utilisateurs...", "createButton": "Ajouter un utilisateur", "deleteSuccess": "Utilisateur supprimé avec succès", "deleteError": "<PERSON><PERSON><PERSON> lors de la suppression de l'utilisateur", "confirmDelete": "Êtes-vous sûr de vouloir supprimer cet utilisateur?", "deleteSelected": "Supprimer la sélection", "activateSuccess": "Utilisateur activé avec succès", "activateError": "<PERSON><PERSON>ur lors de l'activation de l'utilisateur", "deactivateSuccess": "Utilisateur désactivé avec succès", "deactivateError": "Erreur lors de la désactivation de l'utilisateur", "activateSelected": "Activer la sélection", "deactivateSelected": "Désactiver la sélection", "activateDialog": {"title": "Confirmer l'activation", "message": "Êtes-vous sûr de vouloir activer cet utilisateur?", "cancel": "Annuler", "confirm": "Confirmer"}, "deactivateDialog": {"title": "Confirmer la désactivation", "message": "Êtes-vous sûr de vouloir dés<PERSON>r cet utilisateur?", "cancel": "Annuler", "confirm": "Confirmer"}, "deleteDialog": {"title": "Confirmer la <PERSON>", "message": "Êtes-vous sûr de vouloir supprimer cet utilisateur?", "cancel": "Annuler", "confirm": "Confirmer"}, "columns": {"username": "Nom d'utilisateur", "email": "Adresse électronique", "role": "R<PERSON><PERSON>", "firstName": "Prénom", "lastName": "Nom", "picture": "Photo", "language": "<PERSON><PERSON>", "supervisor": "Superviseur", "isSuperuser": "Statut super-utilisateur", "isActive": "Actif", "actions": "Actions"}}, "validation": {"title": "Validations", "searchPlaceholder": "Rechercher des validations...", "createButton": "Ajouter une validation", "deleteSuccess": "Validation supprimée avec succès", "deleteError": "E<PERSON>ur lors de la suppression de la validation", "confirmDelete": "Êtes-vous sûr de vouloir supprimer cette validation?", "deleteSelected": "Supprimer la sélection", "deleteDialog": {"title": "Confirmer la <PERSON>", "message": "Êtes-vous sûr de vouloir supprimer cette validation?", "cancel": "Annuler", "confirm": "Confirmer"}, "columns": {"dataType": "Type de données", "validationLevels": "Niveaux de validation", "modifiedBy": "Modifié par", "modifiedAt": "<PERSON><PERSON><PERSON><PERSON> le"}}, "agencies": {"client": "Client", "title": "Agences", "searchPlaceholder": "Rechercher des agences...", "createButton": "Ajouter une agence", "deleteSuccess": "Agence supprimée avec succès", "deleteError": "E<PERSON>ur lors de la suppression de l'agence", "confirmDelete": "Êtes-vous sûr de vouloir supprimer cette agence?", "deleteSelected": "Supprimer la sélection", "deleteDialog": {"title": "Confirmer la <PERSON>", "message": "Êtes-vous sûr de vouloir supprimer cette agence?", "cancel": "Annuler", "confirm": "Confirmer"}, "columns": {"client": "Client", "name": "Nom", "address": "<PERSON><PERSON><PERSON>", "active": "Active", "modifiedBy": "Modifié par", "modifiedAt": "<PERSON><PERSON><PERSON><PERSON> le"}}, "customer": {"title": "Clients", "searchPlaceholder": "Rechercher des clients...", "createButton": "Ajouter un client", "deleteSuccess": "Client supprimé avec succès", "deleteError": "<PERSON><PERSON><PERSON> lors de la suppression du client", "confirmDelete": "Êtes-vous sûr de vouloir supprimer ce client?", "deleteSelected": "Supprimer la sélection", "deleteDialog": {"title": "Confirmer la <PERSON>", "message": "Êtes-vous sûr de vouloir supprimer ce client?", "cancel": "Annuler", "confirm": "Confirmer"}, "columns": {"externalId": "Identifiant Externe", "name": "Nom", "address": "<PERSON><PERSON><PERSON>", "phone": "Téléphone", "email": "<PERSON><PERSON><PERSON>", "modifiedBy": "Modifié par", "modifiedAt": "<PERSON><PERSON><PERSON><PERSON> le"}}, "driver": {"title": "Chauffeurs", "searchPlaceholder": "Rechercher des chauffeurs...", "createButton": "Ajouter un chauffeur", "deleteSuccess": "<PERSON><PERSON>eur supprimé avec succès", "deleteError": "<PERSON><PERSON><PERSON> lors de la <PERSON> du chauffeur", "confirmDelete": "Êtes-vous sûr de vouloir supprimer ce chauffeur?", "deleteSelected": "Supprimer la sélection", "deleteDialog": {"title": "Confirmer la <PERSON>", "message": "Êtes-vous sûr de vouloir supprimer ce chauffeur?", "cancel": "Annuler", "confirm": "Confirmer"}, "columns": {"firstName": "Prénom", "lastName": "Nom", "matricule": "Matricule", "fonction": "Fonction", "datedEmbaucheSociete": "Date d'embauche", "sexe": "<PERSON>e", "dateDeNaissance": "Date de naissance", "age": "Age", "address": "<PERSON><PERSON><PERSON>", "phoneNumber": "Numéro de téléphone", "createdAt": "<PERSON><PERSON><PERSON>", "modifiedBy": "Modifié par", "modifiedAt": "<PERSON><PERSON><PERSON><PERSON> le", "actions": "Actions"}}, "userDepartment": {"title": "Départements Utilisateurs", "searchPlaceholder": "Rechercher des départements utilisateurs...", "createButton": "Ajouter un département utilisateur", "deleteSuccess": "Département utilisateur supprimé avec succès", "deleteError": "<PERSON><PERSON>ur lors de la suppression du département utilisateur", "confirmDelete": "Êtes-vous sûr de vouloir supprimer ce département utilisateur?", "deleteDialog": {"title": "Confirmer la <PERSON>", "message": "Êtes-vous sûr de vouloir supprimer ce département utilisateur?", "cancel": "Annuler", "confirm": "Confirmer"}, "columns": {"code": "Code", "name": "Nom", "createdBy": "C<PERSON><PERSON> par", "createdAt": "<PERSON><PERSON><PERSON>", "modifiedBy": "Modifié par", "modifiedAt": "<PERSON><PERSON><PERSON><PERSON> le", "actions": "Actions"}}, "orders": {"title": "<PERSON><PERSON><PERSON>", "searchPlaceholder": "Entrer l'ID de demande...", "columns": {"code": "N° Document", "customer": "Client", "prestation": "Prestation", "modifiedBy": "Modifié par", "validate": "<PERSON><PERSON><PERSON>", "modifiedAt": "<PERSON><PERSON><PERSON><PERSON> le"}, "actions": {"missingTrip": "<PERSON><PERSON>er des voyages manquant"}, "filters": {"prestation": "Prestation", "all": "Tous", "citerne": "Citerne", "plateau": "Plateau", "benne": "<PERSON><PERSON>"}, "createButton": "<PERSON><PERSON><PERSON> une demande", "deleteSuccess": "Commande supprimée avec succès", "deleteError": "E<PERSON>ur lors de la suppression de la commande", "confirmDelete": "Êtes-vous sûr de vouloir supprimer cette commande?", "deleteDialog": {"title": "Confirmer la <PERSON>", "message": "Êtes-vous sûr de vouloir supprimer cette commande?", "cancel": "Annuler", "confirm": "Confirmer"}}, "travel": {"searchPlaceholder": "Rechercher des voyages...", "createButton": "<PERSON><PERSON><PERSON> un voyage", "title": "Voyages", "globalActions:": "Actions globales", "deleteSuccess": "Voyage supprimé avec succès", "deleteError": "<PERSON><PERSON><PERSON> lors de la suppression du voyage", "confirmDelete": "Êtes-vous sûr de vouloir supprimer ce voyage?", "deleteDialog": {"title": "Confirmer la <PERSON>", "message": "Êtes-vous sûr de vouloir supprimer ce voyage?", "cancel": "Annuler", "confirm": "Confirmer"}, "filters": {"title": "Filtres", "dateFrom": "<PERSON>", "dateTo": "Au", "prestation": "Par Prestation", "started": "Par <PERSON>", "ended": "<PERSON><PERSON><PERSON>", "charged": "Par A chargé", "unloaded": "Par A déchargé", "all": "Tous", "yes": "O<PERSON>", "no": "Non"}, "columns": {"number": "N° Voyage", "loading": "Charg.", "loadedAt": "<PERSON><PERSON><PERSON> le", "loadingRecordedAt": "Date Enreg. Charg.", "unloadedAt": "<PERSON><PERSON><PERSON><PERSON><PERSON> le", "unloadingRecordedAt": "Date Enre<PERSON><PERSON>.", "immobilization": "Imm.", "immobilizationDuration": "Durée des Immo.", "travel": "Voyage", "order": "<PERSON><PERSON><PERSON>", "operation": "Opération", "route": "<PERSON><PERSON><PERSON>", "truckDriver": "Camion / Chauffeur", "duration": "<PERSON><PERSON>", "status": "Etat", "startedAt": "<PERSON><PERSON><PERSON><PERSON> le", "startRecordedAt": "Date Enreg. Debut", "estimatedDate": "Date estimee", "delay": "Retard", "endedAt": "<PERSON><PERSON><PERSON><PERSON> le", "endRecordedAt": "Date Enreg. Fin", "createdAt": "<PERSON><PERSON><PERSON> le", "createdBy": "Crée par", "modifiedBy": "Modifié par", "modifiedAt": "<PERSON><PERSON><PERSON><PERSON> le", "actions": "Actions"}}, "missionExpenseOther": {"title": "DA.Prestation Diverses", "searchPlaceholder": "Rechercher des dépenses...", "createButton": "Ajouter DA.Prestation Diverses", "deleteDialog": {"title": "Confirmer la <PERSON>", "message": "Êtes-vous sûr de vouloir supprimer cette dépense ?", "cancel": "Annuler", "confirm": "Confirmer"}, "deleteSuccess": "DA.Prestation Diverses supprimée avec succès", "deleteError": "<PERSON><PERSON>ur lors de la suppression de la dépense", "confirmDelete": "Êtes-vous sûr de vouloir supprimer cette dépense?", "columns": {"code": "N° Document", "validationState": "État de validation", "nextValidator": "Prochain validateur", "validate": "<PERSON><PERSON><PERSON>", "totalAmount": "Montant Total", "modifiedBy": "Modifié par", "modifiedAt": "<PERSON><PERSON><PERSON><PERSON> le"}, "filters": {"filter": "Par Validé.e", "all": "Tous", "pending": "En attente", "approved": "Approu<PERSON><PERSON>", "rejected": "<PERSON><PERSON><PERSON>"}}, "country": {"title": "Pays", "searchPlaceholder": "Rechercher des pays...", "createButton": "Ajouter un pays", "deleteSuccess": "Pays supprimé avec succès", "deleteError": "Erreur lors de la suppression du pays", "confirmDelete": "Êtes-vous sûr de vouloir supprimer ce pays?", "deleteDialog": {"title": "Confirmer la <PERSON>", "message": "Êtes-vous sûr de vouloir supprimer ce pays?", "cancel": "Annuler", "confirm": "Confirmer"}, "columns": {"name": "Nom du pays", "actions": "Actions", "code": "Code ISO", "isActive": "Actif", "modifiedBy": "Modifié par", "modifiedAt": "<PERSON><PERSON><PERSON><PERSON> le"}, "filters": {"status": "Statut", "all": "Tous", "active": "Actif", "inactive": "Inactif"}}, "missionExpenses": {"title": "<PERSON><PERSON><PERSON> de frais de mission", "searchPlaceholder": "Rechercher des dépenses...", "createButton": "Ajouter une dépense", "deleteSuccess": "Dépense supprimée avec succès", "deleteError": "<PERSON><PERSON>ur lors de la suppression de la dépense", "confirmDelete": "Êtes-vous sûr de vouloir supprimer cette dépense?", "deleteDialog": {"title": "Confirmer la <PERSON>", "message": "Êtes-vous sûr de vouloir supprimer cette dépense?", "cancel": "Annuler", "confirm": "Confirmer"}, "actions": {"cancel": "Annuler", "addTrip": "Ajouter un trajet manquant", "validate": "Valider", "delete": "<PERSON><PERSON><PERSON><PERSON>"}, "filters": {"type": "Prestation", "all": "Tous", "validity:": "Validité", "citerne": "Citerne", "plateau": "Plateau", "benne": "<PERSON><PERSON>", "yes": "O<PERSON>", "no": "Non"}, "columns": {"id": "N° Document", "client": "Client", "validate": "<PERSON><PERSON><PERSON>", "validationState": "État de validation", "nextValidator": "Prochain validateur", "prestation": "Prestation", "canceled": "<PERSON><PERSON><PERSON>", "modifiedBy": "Modifié par", "modifiedAt": "<PERSON><PERSON><PERSON><PERSON> le"}}, "product": {"title": "Produits", "searchPlaceholder": "Rechercher des produits...", "createButton": "Ajouter un produit", "columns": {"label": "Libellé", "service": "Prestation", "modifiedBy": "Modifié par", "modifiedAt": "<PERSON><PERSON><PERSON><PERSON> le"}}, "destination": {"title": "Destinations", "searchPlaceholder": "Rechercher des destinations...", "createButton": "Ajouter une destination", "deleteSuccess": "Destination supprimée avec succès", "deleteError": "Erreur lors de la suppression de la destination", "confirmDelete": "Êtes-vous sûr de vouloir supprimer cette destination?", "deleteSelected": "Supprimer la sélection", "deleteDialog": {"title": "Confirmer la <PERSON>", "message": "Êtes-vous sûr de vouloir supprimer cette destination?", "cancel": "Annuler", "confirm": "Confirmer"}, "columns": {"id": "Code", "name": "Nom", "address": "<PERSON><PERSON><PERSON>", "city": "Ville", "country": "Pays", "postalCode": "Code postal", "status": "Statut", "type": "Type de destination", "latitude": "Latitude", "longitude": "Longitude", "modifiedBy": "Modifié par", "modifiedAt": "<PERSON><PERSON><PERSON><PERSON> le"}, "types": {"national": "National", "international": "International"}}, "pricingRoute": {"title": "<PERSON><PERSON><PERSON> de trajet", "searchPlaceholder": "Rechercher des tarifs...", "createButton": "Ajouter un tarif", "deleteSuccess": "<PERSON><PERSON><PERSON> de trajet supprimé avec succès", "deleteError": "<PERSON><PERSON><PERSON> lors de la suppression du tarif de trajet", "confirmDelete": "Êtes-vous sûr de vouloir supprimer ce tarif de trajet?", "deleteDialog": {"title": "Confirmer la <PERSON>", "message": "Êtes-vous sûr de vouloir supprimer ce tarif de trajet?", "cancel": "Annuler", "confirm": "Confirmer"}, "columns": {"id": "Code", "route": "<PERSON><PERSON><PERSON>", "good": "Prestation associée", "unitePrice": "Prix unitaire"}}, "invoiceItems": {"title": "Operations", "searchPlaceholder": "Rechercher des éléments de facture...", "createButton": "Ajouter un élément de facture", "deleteSuccess": "Élément de facture supprimé avec succès", "deleteError": "Erreur lors de la suppression de l'élément de facture", "confirmDelete": "Êtes-vous sûr de vouloir supprimer cet élément de facture?", "deleteDialog": {"title": "Confirmer la <PERSON>", "message": "Êtes-vous sûr de vouloir supprimer cet élément de facture?", "cancel": "Annuler", "confirm": "Confirmer"}, "createInvoive": "<PERSON><PERSON>er une facture", "export": "Exporter", "filter": "Filtres", "filtersTitle": "Filtrer les éléments de facture", "filters": {"status": "Statut", "billed": "<PERSON><PERSON><PERSON><PERSON>", "all": "Tous", "pending": "En attente", "completed": "<PERSON><PERSON><PERSON><PERSON>", "yes": "O<PERSON>", "no": "Non"}, "columns": {"id": "Code", "operation": "Opération", "completed": "<PERSON><PERSON><PERSON><PERSON>", "status": "Statut", "client": "Client", "clientAgency": "Agence client", "billed": "<PERSON><PERSON><PERSON><PERSON>", "driver": "<PERSON><PERSON><PERSON>", "tractor": "<PERSON><PERSON><PERSON><PERSON>", "trailer": "Remorque", "path": "<PERSON><PERSON><PERSON>", "quantity": "Quantité", "unitPrice": "Prix unitaire", "amount": "<PERSON><PERSON>", "totalCharges": "Charges totales", "margin": "Marge", "modifiedBy": "Modifié par", "date": "Date", "actions": "Actions"}}, "extraction": {"title": "Extractions", "searchPlaceholder": "Rechercher des extractions...", "createButton": "Nouvelle extraction", "filters": {"startDate": "Date de début", "endDate": "Date de fin", "client": "Client", "service": "Prestation", "facture": "Facture", "applyFilters": "Appliquer les filtres", "resetFilters": "Réinitialiser"}, "columns": {"id": "Code", "operation": "Opération", "journey": "Voyage", "date": "Date", "customer": "Client", "service": "Prestation", "invoice": "Facture", "pu": "P.U", "amount": "<PERSON><PERSON>", "route": "<PERSON><PERSON><PERSON>", "tractor": "<PERSON><PERSON><PERSON><PERSON>", "quantity": "Qté", "diliveryQuantity": "<PERSON><PERSON>", "trailer": "Remorque", "driver": "<PERSON><PERSON><PERSON>", "lossOnDilivery": "Ecart des Qtés", "tripprice": "Fair de Route", "fuelAmmount": "Vol.carb.(litres)", "products": "Produits", "productType": "Nature du produit", "fuelNeeded": "Charge Carb", "ca": "CA", "fuelCharges": "Charges Consommation", "dafCharges": "Charges D.A.F.S", "grossMargin": "Marge brute", "immobilisation": "Immobilise'e", "immobilisationDuration": "Durée d'immobilisation", "roadFees": "Charge'", "otherCharges": "<PERSON><PERSON><PERSON>", "otherChargesDate": "Date Enreg. Charg", "started": "Démarrage", "ended": "<PERSON><PERSON><PERSON><PERSON>", "createdBy": "C<PERSON><PERSON> par", "unloadedAt": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "unloaded": "Décharg<PERSON>", "createdAt": "Date En<PERSON><PERSON>", "finished": "<PERSON><PERSON><PERSON><PERSON>", "missionExpense": "DA. Mission", "missionExpenseOther": "DA. Frais Suppl", "agent": "Agent", "modifiedBy": "Modifié par", "modifiedAt": "Date de modification"}}, "notification": {"title": "Notifications", "searchPlaceholder": "Rechercher des notifications...", "createButton": "<PERSON><PERSON><PERSON> une notification", "deleteSuccess": "Notification supprimée avec succès", "deleteError": "Erreur lors de la suppression de la notification", "confirmDelete": "Êtes-vous sûr de vouloir supprimer cette notification?", "new": "Nouveau", "update": "Mise à jour", "deleteDialog": {"title": "Confirmer la <PERSON>", "message": "Êtes-vous sûr de vouloir supprimer cette notification?", "cancel": "Annuler", "confirm": "Confirmer"}, "columns": {"user": "Utilisa<PERSON>ur", "title": "Titre", "type": "Type", "detail": "Détail", "modifiedBy": "Modifié par", "modifiedAt": "<PERSON><PERSON><PERSON><PERSON> le"}}, "truck": {"title": "Camions", "searchPlaceholder": "Rechercher des camions...", "createButton": "Ajouter un camion", "columns": {"id": "Code", "registrationNumber": "Numéro d'immatriculation", "model": "<PERSON><PERSON><PERSON><PERSON>", "brand": "Marque", "year": "<PERSON><PERSON>", "status": "Statut", "lastMaintenance": "Dernière maintenance"}}, "pricingBenne": {"title": "Tarifs des bennes", "searchPlaceholder": "Rechercher des tarifs...", "createButton": "Ajouter un tarif", "deleteSuccess": "<PERSON><PERSON><PERSON> de benne supprimé avec succès", "deleteError": "<PERSON><PERSON><PERSON> lors de la suppression du tarif de benne", "confirmDelete": "Êtes-vous sûr de vouloir supprimer ce tarif de benne?", "deleteDialog": {"title": "Confirmer la <PERSON>", "message": "Êtes-vous sûr de vouloir supprimer ce tarif de benne?", "cancel": "Annuler", "confirm": "Confirmer"}, "columns": {"id": "Code", "route": "<PERSON><PERSON><PERSON>", "product": "Nature du produit", "customer": "Client", "unitePrice": "Prix unitaire"}}, "articles": {"title": "Articles", "searchPlaceholder": "Rechercher des articles...", "createButton": "C<PERSON>er un article", "deleteSuccess": "Article supprimé avec succès", "deleteError": "Erreur lors de la suppression de l'article", "confirmDelete": "Êtes-vous sûr de vouloir supprimer cet article?", "deleteDialog": {"title": "Confirmer la <PERSON>", "message": "Êtes-vous sûr de vouloir supprimer cet article?", "cancel": "Annuler", "confirm": "Confirmer"}, "columns": {"id": "Code", "code": "Code SAP", "label": "Libellé", "operationType": "Type d'opération", "group": "Groupe d'articles", "groupCode": "Code du groupe", "measureUnit": "Unité de mesure", "modifiedBy": "Modifié par", "modifiedAt": "<PERSON><PERSON><PERSON><PERSON> le"}, "units": {"liters": "Litres", "kilograms": "Kilogrammes", "units": "Unités", "cubicMeters": "Mètres cubes"}}, "invoice": {"title": "Factures", "searchPlaceholder": "Rechercher des factures...", "createButton": "<PERSON><PERSON>er une facture", "deleteSuccess": "Facture supprimée avec succès", "deleteError": "<PERSON><PERSON>ur lors de la suppression de la facture", "confirmDelete": "Êtes-vous sûr de vouloir supprimer cette facture?", "addToOdooSuccess": "Facture transférée vers Odoo avec succès", "addToOdooError": "Erreur lors de la transfert de la facture vers Odoo", "deleteDialog": {"title": "Confirmer la <PERSON>", "message": "Êtes-vous sûr de vouloir supprimer cette facture?", "cancel": "Annuler", "confirm": "Confirmer"}, "export": "Exporter", "filters": {"status": "Statut", "all": "Tous", "downloaded": "Télécharg<PERSON>", "draft": "Brouillon", "paid": "<PERSON><PERSON>", "partial payment": "Paiement partiel", "past due": "En retard", "sent": "<PERSON><PERSON><PERSON>"}, "filter": "<PERSON><PERSON><PERSON>", "clearFilters": "Effacer les filtres", "rowsPerPage": "Lignes par page", "columns": {"id": "Code", "number": "N° facture", "client": "Client", "dueDate": "Echeance", "amountHT": "Montant HT", "amountTTC": "Montant TTC", "modifiedBy": "Modifié par", "createdAt": "<PERSON><PERSON><PERSON>", "modifiedAt": "<PERSON><PERSON><PERSON><PERSON> le"}}, "trip": {"title": "Voyages", "searchPlaceholder": "Rechercher des voyages...", "createButton": "<PERSON><PERSON><PERSON> un voyage", "columns": {"id": "Code", "reference": "Référence", "origin": "Origine", "destination": "Destination", "driver": "<PERSON><PERSON><PERSON>", "vehicle": "Véhicule", "startDate": "Date de début", "endDate": "Date de fin", "status": "Statut", "distance": "Distance"}}, "fuelConsumption": {"title": "Consommation de carburant", "searchPlaceholder": "Rechercher une consommation...", "createButton": "<PERSON><PERSON><PERSON> une consommation", "deleteSuccess": "Consommation supprimée avec succès", "deleteError": "E<PERSON>ur lors de la suppression de la consommation", "deleteSelected": "Supprimer la sélection", "confirmDelete": "Êtes-vous sûr de vouloir supprimer cette consommation?", "deleteDialog": {"title": "Confirmer la <PERSON>", "message": "Êtes-vous sûr de vouloir supprimer cette consommation?", "cancel": "Annuler", "confirm": "Confirmer"}, "columns": {"code": "Code", "createdAt": "<PERSON><PERSON><PERSON>", "createdBy": "C<PERSON><PERSON> par", "modifiedBy": "Modifié par", "modifiedAt": "<PERSON><PERSON><PERSON><PERSON> le"}}}, "forms": {"routeForm": {"title": "Ajouter un Trajet", "create": "<PERSON><PERSON><PERSON>", "edit": "Modifier", "create_title": "<PERSON><PERSON><PERSON> un Trajet", "edit_title": "Modifier un Trajet", "add_price": "Ajouter un tarif", "deleteDialog": {"delete_title": "Supprimer un tarif", "delete_message": "Êtes-vous sûr de vouloir supprimer ce tarif?", "cancel": "Annuler", "confirm": "Confirmer"}, "description": {"delay": "La durrée du vayage est estimée en nombre d'heures (Ex: 48 pour definir 2 jours, si en minute, alors 0.25 pour 15 min, 0.5 pour 30,0,75 pour 45 min ...)", "distance": "Définir une distance en km", "petrol_volume": "Le volume de carburant est estimé en nombre de litres", "fees": "Les frais de route sont estimés en nombre de DH", "nbr_ligne": "Le nombre de lignes est estimé en nombre de lignes", "special_consumption": "La consommation spéciale est estimée en nombre de litres", "special_value": "valeurs numerique sans separateur décimal, si plusieur valeurs, séparer par un tiret (-)"}, "messages": {"create_success": "<PERSON><PERSON><PERSON> c<PERSON> avec succès", "create_error": "Échec de la création du trajet", "update_success": "Trajet mis à jour avec succès", "update_error": "Échec de la mise à jour du trajet"}, "fields": {"departure": "<PERSON><PERSON><PERSON><PERSON>", "destination": "Destination", "delay": "<PERSON><PERSON><PERSON> du voyage", "distance": "Distance", "petrol_volume": "Volume de carburant", "fees": "Frais de route", "is_active": "Actif", "is_city": "Livraison à la ville", "nbr_ligne": "Nombre de Bons", "special_consumption": "Consommation spéciale", "special_value": "Valeur spéciale", "archive": "Archiver", "pricing_list": "Tarif <PERSON>", "good_type": "Prestation Associée", "price": "Prix unitaire"}}, "natureOfArticleForm": {"title": "Ajouter une Nature d'Article", "create": "<PERSON><PERSON><PERSON>", "edit": "Modifier", "create_title": "Créer une Nature d'Article", "edit_title": "Modifier une Nature d'Article", "messages": {"create_success": "Nature d'article créée avec succès", "create_error": "Échec de la création de la nature d'article", "update_success": "Nature d'article mise à jour avec succès", "update_error": "Échec de la mise à jour de la nature d'article"}, "fields": {"name": "Nom de la nature d'article"}}, "profile": {"title": "Profil", "save": "Enregistrer les modifications", "cancel": "Annuler", "saving": "Enregistrement...", "message": {"update_success": "Profil mis à jour avec succès", "update_error": "Échec de la mise à jour du profil"}, "desc": "Gérer vos informations de compte", "upload": "Télécharger une nouvelle photo", "picDes": "Formats autorisés : PNG ou JPEG. Taille maximale de 800K.", "fields": {"firstName": "Prénom", "lastName": "Nom", "email": "Email", "picture": "Photo de profil"}}, "articleGroupForm": {"title": "Ajouter un Groupe d'Articles", "create": "<PERSON><PERSON><PERSON>", "edit": "Modifier", "create_title": "Créer un Groupe d'Articles", "edit_title": "Modifier un Groupe d'Articles", "messages": {"create_success": "Groupe d'articles créé avec succès", "create_error": "Échec de la création du groupe d'articles", "update_success": "Groupe d'articles mis à jour avec succès", "update_error": "Échec de la mise à jour du groupe d'articles"}, "fields": {"name": "Nom du groupe d'article"}}, "stationForm": {"title": "Ajouter une Station", "create": "<PERSON><PERSON><PERSON>", "edit": "Modifier", "create_title": "<PERSON><PERSON>er une Station", "edit_title": "Modifier une Station", "messages": {"create_success": "Station créée avec succès", "create_error": "Échec de la création de la station", "update_success": "Station mise à jour avec succès", "update_error": "Échec de la mise à jour de la station"}, "fields": {"station_name": "Nom de la station", "station_localisation": "Localisation", "email": "Email de notification", "active": "Active"}}, "changePasswordForm": {"title": "Changement de mot de passe utilisateur", "description": "Entrez un nouveau mot de passe pour l'utilisateur", "reset": {"title": "Veuillez changer votre mot de passe par défaut conformément à la politique de l'entreprise", "description": "Réinitialiser le mot de passe pour l'utilisateur:"}, "back": "Retour aux utilisateurs", "save": "Enregistrer les modifications", "resetButton": "Réinitialiser le mot de passe", "fields": {"password": "Mot de passe", "confirmPassword": "Mot de passe (à nouveau)", "setNewPassword": "Définir un nouveau mot de passe"}}, "usersForm": {"create": "<PERSON><PERSON><PERSON>", "edit": "Modifier", "title": "Ajouter un Utilisateur", "create_title": "<PERSON><PERSON>er un Utilisateur", "edit_title": "Modifier un Utilisateur", "sections": {"basic_info": "Informations de base", "password": "Mot de passe", "permissions": "Permissions", "accountDetails": "<PERSON>é<PERSON> du compte"}, "descriptions": {"title": "Tout d'abord, entrez un nom d'utilisateur et un mot de passe. Ensuite, vous pourrez modifier davantage d'options utilisateur.", "updatePassword": "Les mots de passe ne sont pas enregistrés en clair, ce qui ne permet pas d’afficher le mot de passe de cet utilisateur, mais il est possible de le changer en utilisant", "username": "Requis. 150 caractères maximum. Uniquement des lettres, nombres et les caractères « @ », « . », « + », « - » et « _ ».", "password": {"1": "Votre mot de passe ne peut pas trop ressembler à vos autres informations personnelles.", "2": "Votre mot de passe doit contenir au moins 8 caractères.", "3": "Votre mot de passe ne peut pas être un mot de passe couramment utilisé.", "4": "Votre mot de passe ne peut pas être entièrement numérique."}, "confirmPassword": "Sai<PERSON><PERSON>z le même mot de passe que précédemment, pour vérification.", "active": "Précise si l'utilisateur doit être considéré comme actif. Décochez ceci plutôt que de supprimer le compte.", "groupStatus": "Précise si l'utilisateur peut se connecter à ce site d'administration.", "isSuperuser": "Précise que l'utilisateur possède toutes les permissions sans les assigner explicitement."}, "messages": {"create_success": "Utilisateur c<PERSON>é avec succès", "create_error": "Échec de la création de l'utilisateur", "update_success": "Utilisateur mis à jour avec succès", "update_error": "Échec de la mise à jour de l'utilisateur", "reset_password_success": "Mot de passe réinitialisé avec succès", "reset_password_error": "Échec de la réinitialisation du mot de passe"}, "fields": {"username": "Nom d'utilisateur", "email": "Adresse électronique", "firstName": "Prénom", "password": "Mot de passe", "confirmPassword": "Confirmer le mot de passe", "lastName": "Nom", "groupStatus": "Statut équip", "typeOfOperation": "Type d'operation à gérer", "isSuperuser": "Statut super-utilisateur", "isActive": "Actif", "joinedDate": "Date d’inscription", "lastConnection": "Dernière connexion", "groups": "Groupes", "permissions": "Permissions", "mainDepartment": "Département principal", "annexDepartments": "Départements annexes", "otherPhone": "Autre téléphone", "language": "<PERSON><PERSON>", "supervisor": "Superviseur"}}, "dataValidationForm": {"create_title": "C<PERSON>er une validation de données", "edit_title": "Modifier la validation de données", "fields": {"levelTovalidate": "Niveau à valider", "dataType": "Type de données", "validationLevels": "Niveaux de validation"}, "messages": {"create_success": "Validation de données créée avec succès", "create_error": "Échec de la création de la validation de données", "update_success": "Validation de données mise à jour avec succès", "update_error": "Échec de la mise à jour de la validation de données"}, "dataTypeOptions": {"PURCHAGES_REQUESTS": "<PERSON><PERSON><PERSON>'<PERSON><PERSON><PERSON>", "UPDATE_REQUESTS": "Demandes de mise à jour", "MISSIONS_EXPENSES": "Dépenses de mission", "INVOICES": "Factures"}}, "agencyForm": {"create": "<PERSON><PERSON><PERSON>", "edit": "Modifier", "title": "Ajouter une Agence", "create_title": "<PERSON><PERSON><PERSON> une Agence", "edit_title": "Modifier une Agence", "messages": {"create_success": "Agence créée avec succès", "create_error": "Échec de la création de l'agence", "update_success": "Agence mise à jour avec succès", "update_error": "Échec de la mise à jour de l'agence"}, "fields": {"name": "Nom de l'agence ou station", "address": "Adresse de l'agence ou station", "company": "Client", "is_active": "Active"}}, "customerForm": {"create": "<PERSON><PERSON><PERSON>", "edit": "Modifier", "title": "Ajouter un Client", "create_title": "<PERSON><PERSON>er un Client", "edit_title": "Modifier un Client", "messages": {"create_success": "Client créé avec succès", "create_error": "Échec de la création du client", "update_success": "Client mis à jour avec succès", "update_error": "Échec de la mise à jour du client"}, "fields": {"sap_uid": "Identifiant Externe", "name": "Nom du client", "citerne_price_diff": "Ecart sur tarif Citerne", "other_info": "Informations supplémentaires", "address": "<PERSON><PERSON><PERSON>", "phone1": "Téléphone", "phone2": "Autre Téléphone", "groupcode": "Code du groupe", "zipcode": "Code postal", "mailaddres": "<PERSON><PERSON><PERSON>", "cmpprivate": "Cmp privé", "mailzipcod": "Code postal mail", "addid": "Registre de commerce", "currency": "<PERSON><PERSON>", "cardtype": "Type de carte", "lictradnum": "Lic Trad Num"}}, "driverForm": {"create": "<PERSON><PERSON><PERSON>", "edit": "Modifier", "title": "A<PERSON>ter un Chauffeur", "create_title": "<PERSON><PERSON><PERSON> un Chauffeur", "edit_title": "Modifier un Chauffeur", "driversFunction": {"CHAUFFEUR LIGNE": "CHAUFFEUR LIGNE", "CHAUFFEUR VILLE": "CHAUFFEUR VILLE", "CHAUFFEUR": "CHAUFFEUR", "CHAUFFEUR BUREAU": "CHAUFFEUR BUREAU"}, "driversSexe": {"Masculin": "<PERSON><PERSON><PERSON><PERSON>", "Féminin": "Fé<PERSON>n"}, "messages": {"create_success": "<PERSON><PERSON><PERSON> c<PERSON><PERSON> avec succès", "create_error": "Échec de la création du chauffeur", "update_success": "<PERSON><PERSON>eur mis à jour avec succès", "update_error": "Échec de la mise à jour du chauffeur"}, "fields": {"matricule": "Matricule", "fonction": "Fonction", "dated_embauche_societe": "Date d'embauche", "sexe": "<PERSON>e", "date_de_naissance": "Date de naissance", "first_name": "Prénom", "last_name": "Nom", "address": "<PERSON><PERSON><PERSON>", "phone_number": "Numéro de téléphone"}}, "userDepartment": {"create": "<PERSON><PERSON><PERSON>", "edit": "Modifier", "title": "Ajouter un Département Utilisateur", "create_title": "Créer un Département Utilisateur", "edit_title": "Modifier un Département Utilisateur", "messages": {"create_success": "Département utilisateur créé avec succès", "create_error": "Échec de la création du département utilisateur", "update_success": "Département utilisateur mis à jour avec succès", "update_error": "Échec de la mise à jour du département utilisateur"}, "fields": {"name": "Nom"}}, "orderForm": {"create": "<PERSON><PERSON><PERSON>", "edit": "Modifier", "title": "A<PERSON>ter une Demande", "requestValidation": {"title": "Demander la validation", "message": "Êtes-vous sûr de vouloir demander la validation de cette demande ?"}, "messages": {"create_success": "<PERSON><PERSON><PERSON> c<PERSON> avec succès", "create_error": "Échec de la création de la demande", "update_success": "<PERSON><PERSON><PERSON> mise à jour avec succès", "update_error": "Échec de la mise à jour de la demande", "validation_request_sent": "Demande de validation envoyée avec succès", "validation_request_error": "Échec de l'envoi de la demande de validation", "invoice_generated": "Facture générée avec succès", "invoice_error": "Échec de la génération de la facture"}, "validationStatusOptions": {"EN ATTENTE": "EN ATTENTE", "VALIDE": "VALIDE", "REJETE": "REJETE"}, "prestationOptions": {"CITERNE": "CITERNE", "PLATEAU": "PLATEAU", "BENNE": "BENNE"}, "fields": {"customer": "Client", "prestation": "Prestation", "is_city": "Livraison ville ?", "validated": "<PERSON><PERSON><PERSON>", "operations": "Opérations"}, "operationForm": {"tractor": "Tracteur/Camion", "trailer": "Remorque", "route": "<PERSON><PERSON><PERSON>", "direction": "Direction", "be_number": "N° BE", "bc_number": "N° BC/N° Container", "product": "Services", "product_nature": "Nature du produit", "client_agency": "Agence du client", "quantity": "Quantité(L/T)", "road_fees": "Frais de Route", "fuel_volume": "Volume carburant (L)", "driver": "<PERSON><PERSON><PERSON>", "validation": "Validation", "unit_price": "Prix unitaire", "unloaded_quantity": "Quantité déchargé"}, "addOperation": "Ajouter une Opération", "deleteDialog": {"title": " Confirmer la <PERSON>", "message": "Êtes-vous sûr de vouloir supprimer cette opération ? Cette action est irréversible.", "confirm": "Confirmer", "cancel": "Annuler"}}, "userDepartmentForm": {"title": "Ajouter un Département Utilisateur", "create": "<PERSON><PERSON><PERSON>", "edit": "Modifier", "fields": {"name": "Nom"}}, "invoiceForm": {"title": "Ajouter une Facture", "create": "<PERSON><PERSON><PERSON>", "edit": "Modifier", "fields": {"customer": "Client", "order": "<PERSON><PERSON><PERSON>", "conditions": "Conditions", "echeance": "Echeance", "bank_ref": "Référence ban<PERSON>ire", "account_number": "Numéro de compte", "amount": "<PERSON><PERSON>", "total_amount": "Montant total", "invoice_lines": "Lignes de facture"}}, "invoiceLineForm": {"title": "Ajouter une Ligne de Facture", "create": "<PERSON><PERSON><PERSON>", "edit": "Modifier", "fields": {"truck": "Camion", "be_number": "N° BE", "bc_number": "N° BC", "destination": "Destination", "product": "Produit", "price": "Prix", "qty": "Quantité", "amount_ht": "Montant HT"}}, "missionExpenseOtherForm": {"title": "DA.Prestation Diverses", "create_title": "<PERSON><PERSON><PERSON> une dépense", "edit_title": "Modifier la dépense", "delete_title": "Supp<PERSON>er la dépense", "delete_message": "Êtes-vous sûr de vouloir supprimer cette dépense ?", "lines_title": "<PERSON><PERSON><PERSON>", "fields": {"code": "N° Document", "date": "Date", "priority": "Priorité", "validation": "Validation", "validationReason": "Motif de validation", "expenseLines": "<PERSON><PERSON><PERSON>", "totalAmount": "Montant total"}, "expenseLine": {"designation": "Désignation", "date": "Date", "duration": "<PERSON><PERSON><PERSON>", "unitPrice": "Prix unitaire", "quantity": "Quantité", "amount": "<PERSON><PERSON>", "operations": "Opérations"}, "validation": {"status": {"pending": "En attente", "validated": "Valid<PERSON>", "rejected": "Rejetée"}}, "priority": {"normal": "Normal", "urgent": "<PERSON><PERSON>", "current": "<PERSON><PERSON><PERSON>", "important": "Important"}, "buttons": {"add_line": "Ajouter une ligne", "remove_line": "Supprimer la ligne"}, "messages": {"create_success": "<PERSON>épense créée avec succès", "create_error": "Erreur lors de la création de la dépense", "update_success": "<PERSON><PERSON><PERSON><PERSON> mise à jour avec succès", "update_error": "Erreur lors de la mise à jour de la dépense", "delete_success": "Dépense supprimée avec succès", "delete_error": "<PERSON><PERSON>ur lors de la suppression de la dépense"}}, "missionExpenseForm": {"title": "DA.Prestation", "create_title": "<PERSON><PERSON><PERSON> une dépense", "edit_title": "Modifier la dépense", "delete_title": "Supp<PERSON>er la dépense", "delete_message": "Êtes-vous sûr de vouloir supprimer cette dépense ?", "lines_title": "Lignes de consommation", "fields": {"code": "N° Document", "date": "Date", "order": "<PERSON><PERSON><PERSON>", "canceled": "<PERSON><PERSON><PERSON>", "validation": "Validation", "validationLine": "Ligne de validation", "validationReason": "Motif de validation", "expenseLines": "Lignes de consommation", "submit": "Soumettre la validation"}, "expenseLine": {"product": "Produit", "driver": "<PERSON><PERSON><PERSON>", "beNumber": "N° BE", "bcNumber": "N° BC", "obs": "OBS", "vehicle": "Véhicule", "leave": "<PERSON><PERSON><PERSON><PERSON>", "destination": "Destination", "fuelConsumption": "Conso. Carburant (L)", "quantity": "Qté Trans.", "missionFees": "Frais de Mission", "order": "<PERSON><PERSON><PERSON>", "fuelCost": "<PERSON><PERSON> Conso. Carburant"}, "validation": {"status": {"pending": "En attente", "validated": "Valid<PERSON>", "rejected": "Rejetée"}}, "messages": {"create_success": "<PERSON>épense créée avec succès", "create_error": "Erreur lors de la création de la dépense", "update_success": "<PERSON><PERSON><PERSON><PERSON> mise à jour avec succès", "update_error": "Erreur lors de la mise à jour de la dépense", "delete_success": "Dépense supprimée avec succès", "delete_error": "<PERSON><PERSON>ur lors de la suppression de la dépense"}}, "groupForm": {"title": "Ajouter un Groupe", "create": "<PERSON><PERSON><PERSON>", "edit": "Modifier", "fields": {"name": "Nom", "permissions": "Permissions"}}, "productForm": {"title": "Ajouter un Produit", "create": "<PERSON><PERSON><PERSON>", "edit": "Modifier", "fields": {"name": "Nom", "service": "Service"}}, "vehicleForm": {"title": "Ajouter un véhicule", "create": "<PERSON><PERSON><PERSON>", "edit": "Modifier", "fields": {"code": "Code", "number_plate": "Immatriculation", "consumption_card_number": "N° Carte consommation", "vehicle_type": "Type de véhicule", "container": "Conteneur", "kilometer_old": "Kilomètre ancien", "kilometer_new": "Kilomètre nouveau", "petrol_consumption": "Consommation essence", "long": "Longitude", "lat": "Latitude", "is_active": "Actif"}}, "consumptionOperationsForm": {"title": "<PERSON><PERSON><PERSON> Demande Consommation", "create": "<PERSON><PERSON><PERSON>", "edit": "Modifier", "addDistribution": "Ajouter une répartition", "addDistributionError": "Erreur lors de l'ajout de la répartition", "deleteDistribution": "Supprimer la répartition", "deleteDistributionError": "Erreur lors de la suppression de la répartition", "deleteDistributionDialog": {"title": "Confirmer la <PERSON>", "message": "Êtes-vous sûr de vouloir supprimer cette répartition ?", "cancel": "Annuler", "confirm": "Confirmer"}, "messages": {"create_success": "Consommation créée avec succès", "create_error": "Erreur lors de la création de la consommation", "update_success": "Consommation mise à jour avec succès", "update_error": "Erreur lors de la mise à jour de la consommation", "delete_success": "Consommation supprimée avec succès", "delete_error": "E<PERSON>ur lors de la suppression de la consommation"}, "fields": {"operation": "Opération", "type_conso": "Type de consommation", "validation": "Validation", "volumeTotal": "Volume total", "code": "Code", "distributions": "Répartition", "station": "Station", "volume": "Volume", "cash_card": "Type de paiement", "card_number": "Numéro de carte"}}}, "common": {"apply": "Appliquer", "yes": "O<PERSON>", "no": "Non", "back": "Retour", "hours": "<PERSON><PERSON>", "minutes": "Minutes", "seconds": "Secondes", "waiting": "En attente", "days": "Jours", "months": "<PERSON><PERSON>", "years": "<PERSON><PERSON>", "filter": "<PERSON><PERSON><PERSON>", "clear": "<PERSON><PERSON><PERSON><PERSON>", "valid": "Valide", "reject": "<PERSON><PERSON><PERSON>", "normal": "Normal", "reverse": "Inversé", "cancel": "Annuler", "status": {"canceled": "<PERSON><PERSON><PERSON>", "completed": "<PERSON><PERSON><PERSON><PERSON>", "unloaded": "Décharg<PERSON>", "charged": "<PERSON><PERSON><PERSON>", "started": "<PERSON><PERSON><PERSON><PERSON>", "pending": "En attente"}, "export": "Exporter", "import": "Importer", "print": "<PERSON><PERSON><PERSON><PERSON>", "true": "Vrai", "false": "Faux", "actions": {"delete": "<PERSON><PERSON><PERSON><PERSON>", "edit": "Modifier", "view": "Voir", "validate": "Valider", "reject": "<PERSON><PERSON><PERSON>", "import": "Importer", "deleteSelected": "Supprimer la sélection", "exportCsv": "Exporter en CSV", "cancel": "Annuler", "create": "<PERSON><PERSON><PERSON>", "confirm": "Confirmer", "save": "Enregistrer", "action": "Action"}, "dialog": {"delete": {"title": "Confirmer la <PERSON>", "message": "Êtes-vous sûr de vouloir supprimer cet élément ? Cette action est irréversible.", "confirm": "<PERSON><PERSON><PERSON><PERSON>", "cancel": "Annuler"}}, "messages": {"deleteSuccess": "Élément supprimé avec succès", "deleteError": "Échec de la suppression de l'élément"}}, "Mission Expenses": "Dépenses de mission", "Search expenses...": "Rechercher des dépenses...", "Add Expense": "Ajouter une dépense", "Oui": "O<PERSON>", "Non": "Non", "menu": {"dashboard": "Tableau de bord", "apppages": "Applications & Pages", "analytics": "Analytique", "crm": "CRM", "ecommerce": "Commerce électronique", "email": "Email", "chat": "Cha<PERSON>", "calendar": "<PERSON><PERSON><PERSON>", "invoice": "Facture", "users": "Utilisateurs", "userProfile": "Profil utilisateur", "userDepartments": "Départements", "accountSettings": "Paramètres du compte", "faq": "FAQ", "helpCenter": "Centre d'aide", "pricing": "Tarification", "components": "Composants", "formsTables": "Formulaires & Tableaux", "textField": "Champ de texte", "select": "Sélection", "checkbox": "Case à cocher", "radio": "Bouton radio", "textarea": "Zone de texte", "autocomplete": "Auto-complétion", "datePickers": "Sélecteurs de date", "fileUploader": "Téléchargement de fichiers", "chartsMisc": "Graphiques & Divers", "formElements": "Éléments de formulaire", "customInputs": "Entrées personnalisées", "services": {"main": "Gestion Exploration", "travel": "Voyages", "orders": "<PERSON><PERSON><PERSON>", "products": "Produits", "articles": "Articles", "extractions": "Extractions", "immobilizations": "Immobilisations", "invoiceItems": "Opérations", "pricing": "<PERSON><PERSON><PERSON>", "pricingForBenne": "Tarifs pour benne", "natureOfProducts": "Nature des Articles", "articleGroups": "Groupes d'articles"}, "vehicles": {"main": "Gestion des véhicules", "vehicles": "Véhicules", "trucks": "Camions"}, "userManagement": {"main": "Gestion des utilisateurs", "groups": "Groupes", "userDepartments": "Départements", "permissions": "Permissions", "driver": "Chauffeurs", "customers": "Clients", "agencies": "Agences", "validation": "Validation", "users": "Utilisateurs"}, "notifications": {"main": "Notifications", "notifications": "Notifications"}, "financial": {"main": "Gestion Financière", "invoices": "Factures", "missionExpenses": "DF.Mission/Consommation", "missionExpensesOther": "DA.Prestation diverses"}, "settings": {"main": "Paramètres", "countries": "Pays", "destinations": "Destinations", "route": "Trajets", "drivers": "Chauffeurs"}, "operations": {"main": "Opérations", "articles": "Articles", "requests": "<PERSON><PERSON><PERSON>", "extractions": "Extractions", "articleGroups": "Groupes d'articles", "articleTypes": "Types d'articles", "operations": "Opérations", "products": "Produits", "binRates": "<PERSON><PERSON>", "standardRates": "Taux standard", "trips": "Voyages"}, "fuelCunsumption": {"main": "Consommation de carburant", "operationConsumption": "Consommation d'opération", "fuelConsumption": "Consommation de carburant", "station": "Stations"}, "switch": "Interrupteur", "editor": "<PERSON><PERSON><PERSON>", "slider": "<PERSON><PERSON>", "inputMask": "Masque de saisie", "formLayouts": "Dispositions de formulaire", "formValidation": "Validation de formulaire", "formWizard": "Assistant de formulaire", "table": "<PERSON><PERSON>", "muiDataGrid": "Grille de données <PERSON>", "charts": "Graphiques", "apex": "Apex", "recharts": "Recharts", "chartjs": "ChartJS", "others": "Autres", "accessControl": "Contrôle d'accès", "menuLevels": "Niveaux de menu", "menuLevel21": "Niveau de menu 2.1", "menuLevel22": "Niveau de menu 2.2", "menuLevel31": "Niveau de menu 3.1", "menuLevel32": "Niveau de menu 3.2", "disabledMenu": "<PERSON><PERSON>", "raiseSupport": "<PERSON><PERSON><PERSON><PERSON> <PERSON> l'aide", "documentation": "Documentation"}, "groups": {"form": {"title": {"create": "Créer un nouveau groupe", "edit": "Modifier le groupe"}, "fields": {"name": "Nom du groupe", "name_placeholder": "Entrez le nom du groupe", "permissions": "Permissions", "permissions_placeholder": "Sélectionnez les permissions"}, "buttons": {"back": "Retour aux groupes", "cancel": "Annuler", "save_return": "Enregistrer & Retour", "save_edit": "Enregistrer & Modifier", "save_add": "Enregistrer & Ajouter"}, "messages": {"create_success": "Groupe créé avec succès", "update_success": "Groupe mis à jour avec succès", "create_error": "Échec de la création du groupe", "update_error": "Échec de la mise à jour du groupe"}}}, "product": {"form": {"create_title": "Créer un produit", "edit_title": "Modifier le produit", "name": "Nom", "name_required": "Le nom est requis", "service": "Service", "service_required": "Le service est requis", "loading_services_error": "Erreur lors du chargement des services. Veuillez réessayer plus tard.", "create_success": "Produit créé avec succès", "create_error": "Échec de la création du produit", "update_success": "Produit mis à jour avec succès", "update_error": "Échec de la mise à jour du produit", "saving": "Enregistrement...", "buttons": {"cancel": "Annuler", "save": "Enregistrer"}}}, "article": {"form": {"create_title": "C<PERSON>er un article", "edit_title": "Modifier l'article", "code": "Code SAP", "code_required": "Le code SAP est requis", "label": "Libellé", "label_required": "Le libellé est requis", "group": "Groupe d'articles", "related_documents": "Documents liés", "related_articles": "Article lié", "units": {"cubicMeters": "Mètres cubes", "liters": "Litres", "kilograms": "Kilogrammes", "tonnes": "<PERSON><PERSON>", "units": "Unités"}, "group_required": "Le groupe d'articles est requis", "operation_type": "Type d'opération", "operation_type_required": "Le type d'opération est requis", "measure_unit": "Unité de mesure", "measure_unit_required": "L'unité de mesure est requise", "create_success": "Article créé avec succès", "create_error": "Échec de la création de l'article", "update_success": "Article mis à jour avec succès", "update_error": "Échec de la mise à jour de l'article"}}}