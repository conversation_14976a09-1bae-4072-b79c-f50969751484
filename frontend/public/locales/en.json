{"Dashboards": "Dashboards", "auth.login.title": "Welcome to ", "auth.login.description": "Please sign in and start your workday.", "auth.login.form.email": "Username/Email", "auth.login.form.password": "Password", "auth.login.forgot_password": "Forgot Password", "auth.login.button": "Sign In", "auth.logout": "", "Analytics": "Analytics", "CRM": "CRM", "eCommerce": "eCommerce", "Apps & Pages": "Apps & Pages", "Apps": "Apps", "Email": "Email", "Chat": "Cha<PERSON>", "Calendar": "Calendar", "Invoice": "Invoice", "List": "List", "Preview": "Preview", "Edit": "Edit", "Add": "Add", "User": "User", "View": "View", "Security": "Security", "Billing & Plans": "Billing & Plans", "Notifications": "Notifications", "Connection": "Connection", "Roles & Permissions": "Roles & Permissions", "Roles": "Roles", "Permissions": "Permissions", "Pages": "Pages", "User Profile": "User Profile", "Profile": "Profile", "Teams": "Teams", "Projects": "Projects", "Connections": "Connections", "Account Settings": "Account <PERSON><PERSON>", "Account": "Account", "Billing": "Billing", "FAQ": "FAQ", "Help Center": "Help Center", "Pricing": "Pricing", "Miscellaneous": "Miscellaneous", "Coming Soon": "Coming Soon", "Under Maintenance": "Under Maintenance", "Page Not Found - 404": "Page Not Found - 404", "Not Authorized - 401": "Not Authorized - 401", "Server Error - 500": "Server Error - 500", "Auth Pages": "<PERSON><PERSON>s", "Login": "<PERSON><PERSON>", "Login v1": "Login v1", "Login v2": "Login v2", "Login With AppBar": "Login With AppBar", "Register": "Register", "Register v1": "Register v1", "Register v2": "Register v2", "Register Multi-Steps": "Register Multi-Steps", "Verify Email": "<PERSON><PERSON><PERSON>", "Verify Email v1": "Verify Email v1", "Verify Email v2": "Verify Email v2", "Forgot Password": "Forgot Password", "Forgot Password v1": "Forgot Password v1", "Forgot Password v2": "Forgot Password v2", "Reset Password": "Reset Password", "Reset Password v1": "Reset Password v1", "Reset Password v2": "Reset Password v2", "Two Steps": "Two Steps", "Two Steps v1": "Two Steps v1", "Two Steps v2": "Two Steps v2", "Wizard Examples": "Wizard Examples", "Checkout": "Checkout", "Property Listing": "Property Listing", "Create Deal": "Create Deal", "Dialog Examples": "Dialog Examples", "User Interface": "User Interface", "UI": "UI", "Typography": "Typography", "Icons": "Icons", "Cards": "Cards", "Basic": "Basic", "Advanced": "Advanced", "Statistics": "Statistics", "Widgets": "Widgets", "Actions": "Actions", "Components": "Components", "Accordion": "Accordion", "Alerts": "<PERSON><PERSON><PERSON>", "Avatars": "Avatars", "Badges": "Badges", "Buttons": "Buttons", "Button Group": "Button Group", "Chips": "Chips", "Dialogs": "Dialogs", "Menu": "<PERSON><PERSON>", "Pagination": "Pagination", "Progress": "Progress", "Ratings": "Ratings", "Snackbar": "Snackbar", "Swiper": "Swiper", "Tabs": "Tabs", "Timeline": "Timeline", "Toasts": "Toasts", "Tree View": "Tree View", "More": "More", "Forms & Tables": "Forms & Tables", "Form Elements": "Form Elements", "Text Field": "Text Field", "Select": "Select", "Checkbox": "Checkbox", "Radio": "Radio", "Custom Inputs": "Custom Inputs", "Textarea": "Textarea", "Autocomplete": "Autocomplete", "Date Pickers": "Date Pickers", "Switch": "Switch", "File Uploader": "File Uploader", "Editor": "Editor", "Slider": "Slide<PERSON>", "Input Mask": "Input Mask", "Form Layouts": "Form Layouts", "Form Validation": "Form Validation", "Form Wizard": "Form Wizard", "Table": "Table", "Mui DataGrid": "<PERSON>i <PERSON>Grid", "Charts & Misc": "Charts & Misc", "Charts": "Charts", "Apex": "Apex", "Recharts": "Recharts", "ChartJS": "ChartJS", "Access Control": "Access Control", "Others": "Others", "Menu Levels": "Menu Levels", "Menu Level 2.1": "Menu Level 2.1", "Menu Level 2.2": "Menu Level 2.2", "Menu Level 3.1": "Menu Level 3.1", "Menu Level 3.2": "Menu Level 3.2", "Disabled Menu": "Disabled <PERSON><PERSON>", "Raise Support": "Raise Support", "Documentation": "Documentation", "menu:vehicle": "Vehicle", "menu:financial": "Financial", "financial:consumption": "Mission/Consumption", "financial:services": "Various Services", "financial:invoice": "Invoice", "menu:operations": "Operations Management", "operations:articles": "Articles", "operations:requests": "Requests", "operations:extraction": "Extraction", "operations:articleGroups": "Article Groups", "operations:articleTypes": "Article Types", "operations:operations": "Operations", "operations:products": "Nature of Goods", "operations:binRates": "Bin Rates/Unit Price (L)", "operations:standardRates": "Standard Rates/Unit Price", "operations:trips": "Trips", "menu:fuelConsumption": "Fuel Consumption", "menu:fuelConsumption:consumptionOperations": "Consumption Operations", "menu:fuelConsumption:fuelConsumption": "Fuel Consumption", "table": {"search": "Search...", "noData": "No data available", "error": "An error occurred while loading data", "loading": "Loading...", "create": "Create", "edit": "Edit", "delete": "Delete", "actions": "Actions", "filter": "Filter", "clearFilters": "Clear filters", "rowsPerPage": "Rows per page", "of": "of", "next": "Next", "previous": "Previous", "first": "First", "last": "Last"}, "tables": {"routes": {"title": "Paths", "searchPlaceholder": "Search routes...", "createButton": "Add Route", "deleteSuccess": "Route deleted successfully", "deleteError": "Error deleting route", "confirmDelete": "Are you sure you want to delete this route?", "deleteSelected": "Delete Selected", "deleteDialog": {"title": "Confirm Delete", "message": "Are you sure you want to delete this route?", "cancel": "Cancel", "confirm": "Confirm"}, "columns": {"name": "Label of Route", "livraisonVille": "Is City Delivery", "volumeCarburant": "Petrol Volume", "distance": "Distance", "duree": "Delay", "fraisRoute": "Road Fees", "nombreBons": " Number of Waybills", "activee": "Active", "modifiedBy": "Modified By", "modifiedAt": "Modified At"}}, "natureProduct": {"title": "Nature of Products", "searchPlaceholder": "Search nature of products...", "createButton": "Add Nature of Product", "deleteSuccess": "Nature of product deleted successfully", "deleteError": "Error deleting nature of product", "confirmDelete": "Are you sure you want to delete this nature of product?", "deleteSelected": "Delete Selected", "deleteDialog": {"title": "Confirm Delete", "message": "Are you sure you want to delete this nature of product?", "cancel": "Cancel", "confirm": "Confirm"}, "columns": {"name": "Name", "createdAt": "Created At", "createdBy": "Created By", "modifiedBy": "Modified By", "modifiedAt": "Modified At"}}, "articleGroup": {"title": "Article Groups", "searchPlaceholder": "Search article groups...", "createButton": "Add Article Group", "deleteSuccess": "Article group deleted successfully", "deleteError": "Error deleting article group", "confirmDelete": "Are you sure you want to delete this article group?", "deleteSelected": "Delete Selected", "deleteDialog": {"title": "Confirm Delete", "message": "Are you sure you want to delete this article group?", "cancel": "Cancel", "confirm": "Confirm"}, "columns": {"code": "Id", "name": "Label of Group", "modifiedBy": "Modified By", "modifiedAt": "Modified At"}}, "station": {"title": "Stations", "searchPlaceholder": "Search stations...", "createButton": "Add Station", "deleteSuccess": "Station deleted successfully", "deleteError": "Error deleting station", "confirmDelete": "Are you sure you want to delete this station?", "deleteSelected": "Delete Selected", "bulkCreateSuccess": "Stations created successfully", "bulkCreateError": "Error creating stations", "deleteDialog": {"title": "Confirm Delete", "message": "Are you sure you want to delete this station?", "cancel": "Cancel", "confirm": "Confirm"}, "columns": {"name": "Name", "address": "Localisation", "email": "Email", "active": "Active", "country": "Country", "createdAt": "Created At", "modifiedBy": " Modified By", "modifiedAt": "Modified At"}}, "users": {"title": "Users", "searchPlaceholder": "Search users...", "createButton": "Add User", "activateSuccess": "User activated successfully", "activateError": "Error activating user", "deactivateSuccess": "User deactivated successfully", "deactivateError": "Error deactivating user", "activateSelected": "Activate Selected", "deactivateSelected": "Deactivate Selected", "activateDialog": {"title": "Confirm Activate", "message": "Are you sure you want to activate this user?", "cancel": "Cancel", "confirm": "Confirm"}, "deactivateDialog": {"title": "Confirm Deactivate", "message": "Are you sure you want to deactivate this user?", "cancel": "Cancel", "confirm": "Confirm"}, "deleteSuccess": "User deleted successfully", "deleteError": "Error deleting user", "confirmDelete": "Are you sure you want to delete this user?", "deleteSelected": "Delete Selected", "deleteDialog": {"title": "Confirm Delete", "message": "Are you sure you want to delete this user?", "cancel": "Cancel", "confirm": "Confirm"}, "columns": {"username": "Username", "email": "Email", "role": "Role", "firstName": "First Name", "lastName": "Last Name", "picture": "Picture", "language": "Language", "supervisor": "Supervisor", "isSuperuser": "Is Superuser", "isActive": "Is Active", "actions": "Actions"}}, "validation": {"title": "Validations", "searchPlaceholder": "Search validations...", "createButton": "Add Validation", "deleteSuccess": "Validation deleted successfully", "deleteError": "Error deleting validation", "confirmDelete": "Are you sure you want to delete this validation?", "deleteSelected": "Delete Selected", "deleteDialog": {"title": "Confirm Delete", "message": "Are you sure you want to delete this validation?", "cancel": "Cancel", "confirm": "Confirm"}, "columns": {"dataType": "Data Type", "validationLevels": "Validation Levels", "modifiedBy": "Modified By", "modifiedAt": "Modified At"}}, "agencies": {"title": "Agencies", "client": "Client", "searchPlaceholder": "Search agencies...", "createButton": "Add Agency", "deleteSuccess": "Agency deleted successfully", "deleteError": "Error deleting agency", "confirmDelete": "Are you sure you want to delete this agency?", "deleteSelected": "Delete Selected", "deleteDialog": {"title": "Confirm Delete", "message": "Are you sure you want to delete this agency?", "cancel": "Cancel", "confirm": "Confirm"}, "columns": {"client": "Client", "name": "Name", "address": "Address", "active": "Active", "modifiedBy": "Modified By", "modifiedAt": "Modified At"}}, "customer": {"title": "Customers", "searchPlaceholder": "Search customers...", "createButton": "Add Customer", "deleteSuccess": "Customer deleted successfully", "deleteError": "Error deleting customer", "confirmDelete": "Are you sure you want to delete this customer?", "deleteSelected": "Delete Selected", "deleteDialog": {"title": "Confirm Delete", "message": "Are you sure you want to delete this customer?", "cancel": "Cancel", "confirm": "Confirm"}, "columns": {"externalId": "External ID", "name": "Name", "address": "Address", "phone": "Phone", "email": "Email", "modifiedBy": "Modified By", "modifiedAt": "Modified At"}}, "driver": {"title": "Drivers", "searchPlaceholder": "Search drivers...", "createButton": "Add Driver", "deleteSuccess": "Driver deleted successfully", "deleteError": "Error deleting driver", "confirmDelete": "Are you sure you want to delete this driver?", "deleteSelected": "Delete Selected", "deleteDialog": {"title": "Confirm Delete", "message": "Are you sure you want to delete this driver?", "cancel": "Cancel", "confirm": "Confirm"}, "columns": {"firstName": "First Name", "lastName": "Last Name", "matricule": "Matricule", "age": "Age", "fonction": "Fonction", "datedEmbaucheSociete": "Dated Embauche Societe", "sexe": "<PERSON>e", "dateDeNaissance": "Date De Naissance", "address": "Address", "phoneNumber": "Phone Number", "createdAt": "Created At", "modifiedBy": "Modified By", "modifiedAt": "Modified At", "actions": "Actions"}}, "userDepartment": {"title": "User Departments", "searchPlaceholder": "Search user departments...", "createButton": "Add User Department", "deleteSuccess": "User Department deleted successfully", "deleteError": "Error deleting user department", "confirmDelete": "Are you sure you want to delete this user department?", "deleteDialog": {"title": "Confirm Delete", "message": "Are you sure you want to delete this user department?", "cancel": "Cancel", "confirm": "Confirm"}, "columns": {"code": "Code", "name": "Name", "actions": "Actions", "createdBy": "Created By", "createdAt": "Created At", "modifiedBy": "Modified By", "modifiedAt": "Modified At"}}, "orders": {"title": "Orders", "searchPlaceholder": "Enter order ID...", "columns": {"code": "N° Document", "customer": "Client", "prestation": "Prestation", "modifiedBy": "Modified by", "validate": "Validate", "modifiedAt": "Modified At"}, "actions": {"missingTrip": "Create Missing <PERSON>"}, "filters": {"prestation": "Prestation", "all": "All", "citerne": "Citerne", "plateau": "Plateau", "benne": "<PERSON><PERSON>"}, "createButton": "Create Order", "deleteSuccess": "Order deleted successfully", "deleteError": "Error deleting order", "confirmDelete": "Are you sure you want to delete this order?", "deleteDialog": {"title": "Confirm Delete", "message": "Are you sure you want to delete this order?", "cancel": "Cancel", "confirm": "Confirm"}}, "product": {"title": "Products", "searchPlaceholder": "Search products...", "createButton": "Add Product", "columns": {"label": "Label", "service": "Service", "modifiedBy": " Modified By", "modifiedAt": "Modified At"}}, "destination": {"title": "Destinations", "searchPlaceholder": "Search destinations...", "deleteSelected": "Delete Selected", "createButton": "Add Destination", "deleteSuccess": "Destination deleted successfully", "deleteError": "Error deleting destination", "confirmDelete": "Are you sure you want to delete this destination?", "deleteDialog": {"title": "Confirm Delete", "message": "Are you sure you want to delete this destination?", "cancel": "Cancel", "confirm": "Confirm"}, "columns": {"id": "Code", "name": "Name", "address": "Address", "city": "City", "country": "Country", "postalCode": "Postal Code", "status": "Status", "type": "Destination Type", "latitude": "Latitude", "longitude": "Longitude", "modifiedBy": "Modified By", "modifiedAt": "Modified At"}, "types": {"national": "National", "international": "International"}}, "extraction": {"title": "Extractions", "searchPlaceholder": "Search extractions...", "createButton": "New Extraction", "filters": {"startDate": "Start Date", "endDate": "End Date", "client": "Client", "service": "Service", "facture": "Invoice", "applyFilters": "Apply Filters", "resetFilters": "Reset"}, "columns": {"id": "Code", "operation": "Operation", "journey": "Journey", "date": "Date", "customer": "Customer", "service": "Service", "invoice": "Invoice", "pu": "P.U", "amount": "Amount", "route": "Route", "tractor": "Tractor", "quantity": "Qty", "diliveryQuantity": "Qty Unloaded", "trailer": "Trailer", "driver": "Driver", "lossOnDilivery": "Qty Difference", "tripprice": "Route Fair", "fuelAmmount": "Petrol Vol.(litres)", "products": "Products", "productType": "Product Nature", "fuelNeeded": "Petrol Charge", "ca": "CA", "fuelCharges": "Petrol Charges", "dafCharges": "D.A.F.S Charges", "grossMargin": "<PERSON>", "immobilisation": "Immobilisation", "immobilisationDuration": "Immobilisation Duration", "roadFees": "Road Fees", "otherCharges": "Other Charges", "otherChargesDate": "Other Charges Date", "started": "Started", "ended": "Ended", "createdBy": "Created By", "unloadedAt": "Unloaded At", "unloaded": "Unloaded", "createdAt": "Created At", "finished": "Finished", "missionExpense": "Mission Expense", "missionExpenseOther": "Other Expenses", "agent": "Agent", "modifiedBy": "Modified By", "modifiedAt": "Modified At"}}, "country": {"title": "Countries", "searchPlaceholder": "Search countries...", "createButton": "Add Country", "deleteSuccess": "Country deleted successfully", "deleteError": "Error deleting country", "confirmDelete": "Are you sure you want to delete this country?", "deleteDialog": {"title": "Confirm Delete", "message": "Are you sure you want to delete this country?", "cancel": "Cancel", "confirm": "Confirm"}, "columns": {"name": "Country name", "actions": "Actions", "code": "ISO Code", "isActive": "Active", "modifiedBy": "Modified by", "modifiedAt": "Modified at"}, "filters": {"status": "Status", "all": "All", "active": "Active", "inactive": "Inactive"}}, "invoiceItems": {"title": "Invoice Items", "searchPlaceholder": "Search invoice items...", "createButton": "Add Invoice Item", "export": "Export", "filter": "Filters", "createInvoive": "Create Invoice", "deleteSuccess": "Invoice item deleted successfully", "deleteError": "Error deleting invoice item", "confirmDelete": "Are you sure you want to delete this invoice item?", "deleteDialog": {"title": "Confirm Delete", "message": "Are you sure you want to delete this invoice item?", "cancel": "Cancel", "confirm": "Confirm"}, "filtersTitle": "Filter Invoice Items", "filters": {"status": "Status", "billed": "Billed", "all": "All", "pending": "Pending", "completed": "Completed", "yes": "Yes", "no": "No"}, "columns": {"id": "Code", "operation": "Operation", "completed": "Completed", "status": "Status", "client": "Client", "clientAgency": "Client Agency", "billed": "Billed", "driver": "Driver", "tractor": "Tractor", "trailer": "Trailer", "path": "Path", "quantity": "Quantity", "unitPrice": "Unit Price", "amount": "Amount", "totalCharges": "Total Charges", "margin": "<PERSON><PERSON>", "modifiedBy": "Modified By", "date": "Date", "actions": "Actions"}}, "notification": {"title": "Notifications", "searchPlaceholder": "Search notifications...", "createButton": "Create Notification", "deleteSuccess": "Notification deleted successfully", "deleteError": "Error deleting notification", "confirmDelete": "Are you sure you want to delete this notification?", "new": "New", "update": "Update", "deleteDialog": {"title": "Confirm Delete", "message": "Are you sure you want to delete this notification?", "cancel": "Cancel", "confirm": "Confirm"}, "columns": {"user": "User", "title": "Title", "type": "Type", "detail": "Detail", "modifiedBy": "Modified by", "modifiedAt": "Modified at"}}, "truck": {"title": "Trucks", "searchPlaceholder": "Search trucks...", "createButton": "Add Truck", "columns": {"id": "Code", "registrationNumber": "Registration Number", "model": "Model", "brand": "Brand", "year": "Year", "status": "Status", "lastMaintenance": "Last Maintenance"}}, "pricingBenne": {"title": "Bin Rates", "searchPlaceholder": "Search rates...", "createButton": "Add Rate", "deleteSuccess": "Bin rate deleted successfully", "deleteError": "Error deleting bin rate", "confirmDelete": "Are you sure you want to delete this bin rate?", "deleteDialog": {"title": "Confirm Delete", "message": "Are you sure you want to delete this bin rate?", "cancel": "Cancel", "confirm": "Confirm"}, "export": "Export", "columns": {"id": "Code", "route": "Route", "product": "Product Type", "customer": "Customer", "unitePrice": "Unit Price"}}, "pricingRoute": {"title": "Route Rates", "searchPlaceholder": "Search rates...", "createButton": "Add Rate", "deleteSuccess": "Route rate deleted successfully", "deleteError": "Error deleting route rate", "confirmDelete": "Are you sure you want to delete this route rate?", "deleteDialog": {"title": "Confirm Delete", "message": "Are you sure you want to delete this route rate?", "cancel": "Cancel", "confirm": "Confirm"}, "columns": {"id": "Code", "route": "Route", "good": "Associated Service", "unitePrice": "Unit Price"}}, "invoice": {"title": "Invoices", "searchPlaceholder": "Search invoices...", "createButton": "Create Invoice", "deleteSuccess": "Invoice deleted successfully", "deleteError": "Error deleting invoice", "addToOdooSuccess": "Invoice transferred to Odoo successfully", "addToOdooError": "Error transferring invoice to Odoo", "deleteDialog": {"title": "Confirm Delete", "message": "Are you sure you want to delete this invoice?", "cancel": "Cancel", "confirm": "Confirm"}, "confirmDelete": "Are you sure you want to delete this invoice?", "export": "Export", "filters": {"status": "Status", "all": "All", "downloaded": "Downloaded", "draft": "Draft", "paid": "Paid", "partial payment": "Partial Payment", "past due": "Past Due", "sent": "<PERSON><PERSON>"}, "filter": "Filter", "clearFilters": "Clear Filters", "rowsPerPage": "Rows per page", "columns": {"id": "Code", "number": "Invoice Number", "client": "Client", "dueDate": "Due Date", "amountHT": "Amount HT", "amountTTC": "Amount TTC", "modifiedBy": "Modified By", "createdAt": "Created At", "modifiedAt": "Modified At"}}, "missionExpenses": {"title": "Mission Expenses", "searchPlaceholder": "Search expenses...", "createButton": "Add Expense", "deleteSuccess": "Expense deleted successfully", "deleteError": "Error deleting expense", "confirmDelete": "Are you sure you want to delete this expense?", "deleteDialog": {"title": "Confirm Delete", "message": "Are you sure you want to delete this expense?", "cancel": "Cancel", "confirm": "Confirm"}, "actions": {"cancel": "Cancel", "addTrip": "Add Missing Trip", "delete": "Delete", "validate": "Validate"}, "filters": {"type": "Service", "all": "All", "validity:": "Validity", "citerne": "Citerne", "plateau": "Plateau", "benne": "Bin", "yes": "Yes", "no": "No"}, "columns": {"id": "Document No.", "client": "Client", "validate": "Validate", "validationState": "Validation State", "nextValidator": "Next Validator", "prestation": "Service", "canceled": "Canceled", "modifiedBy": "Modified by", "modifiedAt": "Modified at"}}, "missionExpenseOther": {"title": "Other Expenses", "searchPlaceholder": "Search other expenses...", "createButton": "Add Other Expense", "columns": {"code": "Document No.", "validationState": "Validation State", "nextValidator": "Next Validator", "validate": "Validate", "totalAmount": "Total Amount", "modifiedBy": "Modified By", "modifiedAt": "Modified At"}, "deleteSuccess": "Other expense deleted successfully", "deleteError": "Error deleting other expense", "confirmDelete": "Are you sure you want to delete this other expense?", "deleteDialog": {"title": "Confirm Delete", "message": "Are you sure you want to delete this other expense?", "cancel": "Cancel", "confirm": "Confirm"}, "filters": {"filter": "Validate", "all": "All", "pending": "Pending", "approved": "Approved", "rejected": "Rejected"}}, "articles": {"title": "Articles", "searchPlaceholder": "Search articles...", "createButton": "Create Article", "deleteSuccess": "Article deleted successfully", "deleteError": "Error deleting article", "confirmDelete": "Are you sure you want to delete this article?", "deleteDialog": {"title": "Confirm Delete", "message": "Are you sure you want to delete this article?", "cancel": "Cancel", "confirm": "Confirm"}, "columns": {"id": "Code", "code": "SAP Code", "label": "Label", "operationType": "Operation Type", "group": "Article Group", "groupCode": "Group Code", "measureUnit": "Measure Unit", "modifiedBy": "Modified By", "modifiedAt": "Modified At"}, "units": {"liters": "Liters", "kilograms": "Kilograms", "units": "Units", "cubicMeters": "Cubic Meters"}}, "articleGroups": {"title": "Article Groups", "searchPlaceholder": "Search article groups...", "createButton": "Create Article Group", "columns": {"id": "Code", "code": "Code", "name": "Name", "description": "Description", "status": "Status", "createdAt": "Created At", "updatedAt": "Updated At"}}, "articleTypes": {"title": "Article Types", "searchPlaceholder": "Search article types...", "createButton": "Create Article Type", "columns": {"id": "Code", "code": "Code", "name": "Name", "description": "Description", "status": "Status", "createdAt": "Created At", "updatedAt": "Updated At"}}, "operations": {"title": "Operations", "searchPlaceholder": "Search operations...", "createButton": "Create Operation", "columns": {"id": "Code", "code": "Code", "name": "Name", "description": "Description", "type": "Type", "status": "Status", "startDate": "Start Date", "endDate": "End Date", "client": "Client"}}, "requests": {"title": "Requests", "searchPlaceholder": "Search requests...", "createButton": "Create Request", "columns": {"id": "Code", "reference": "Reference", "type": "Type", "status": "Status", "priority": "Priority", "requestedBy": "Requested By", "assignedTo": "Assigned To", "createdAt": "Created At", "dueDate": "Due Date"}}, "travel": {"searchPlaceholder": "Search travels...", "createButton": "Create Travel", "title": "Travels", "deleteSuccess": "Travel deleted successfully", "deleteError": "Error deleting travel", "globalActions": "Global Actions", "confirmDelete": "Are you sure you want to delete this travel?", "deleteDialog": {"title": "Confirm Delete", "message": "Are you sure you want to delete this travel?", "cancel": "Cancel", "confirm": "Confirm"}, "filters": {"title": "Filters", "dateFrom": "From", "dateTo": "To", "prestation": "By Prestation", "started": "By Started", "ended": "By Ended", "charged": "By Charged", "unloaded": "By Unloaded", "all": "All", "yes": "Yes", "no": "No"}, "columns": {"number": "Travel N°", "loading": "Loading", "loadedAt": "Loaded on", "loadingRecordedAt": "Loading Recorded on", "unloadedAt": "Unloaded on", "unloadingRecordedAt": "Unloading Recorded on", "immobilization": "<PERSON><PERSON><PERSON>.", "immobilizationDuration": "Immob. <PERSON>", "travel": "Travel", "order": "Order", "operation": "Operation", "route": "Route", "truckDriver": "Truck / Driver", "duration": "Duration", "status": "Status", "startedAt": "Started on", "startRecordedAt": "Start Recorded on", "estimatedDate": "Estimated Date", "delay": "Delay", "endedAt": "Ended on", "endRecordedAt": "End Recorded on", "createdAt": "Created on", "createdBy": "Created by", "modifiedBy": "Modified by", "modifiedAt": "Modified on", "actions": "Actions"}}, "trips": {"title": "Trips", "searchPlaceholder": "Search trips...", "createButton": "Create Trip", "columns": {"id": "Code", "reference": "Reference", "origin": "Origin", "destination": "Destination", "driver": "Driver", "vehicle": "Vehicle", "startDate": "Start Date", "endDate": "End Date", "status": "Status", "distance": "Distance"}}, "fuelConsumption": {"deleteSelected": "Delete Selected", "title": "Fuel Consumption", "searchPlaceholder": "Search fuel consumption...", "createButton": "Create Fuel Consumption", "deleteSuccess": "Fuel consumption deleted successfully", "deleteError": "Error deleting fuel consumption", "confirmDelete": "Are you sure you want to delete this fuel consumption?", "deleteDialog": {"title": "Confirm Delete", "message": "Are you sure you want to delete this fuel consumption?", "cancel": "Cancel", "confirm": "Confirm"}, "columns": {"code": "Code", "createdAt": "Created At", "createdBy": "Created By", "modifiedBy": "Modified By", "modifiedAt": "Modified At"}}}, "forms": {"routeForm": {"title": "Add Route", "create": "Create", "edit": "Edit", "create_title": "Create Route", "add_price": "Add Price Tarif", "deleteDialog": {"delete_title": "Delete Price Tarif", "delete_message": "Are you sure you want to delete this price tarif?", "cancel": "Cancel", "confirm": "Confirm"}, "edit_title": "Edit Route", "description": {"delay": "The delay is estimated in number of hours (Ex: 48 for 2 days, if in minutes, then 0.25 for 15 min, 0.5 for 30, 0.75 for 45 min ...)", "distance": "Define a distance in km", "petrol_volume": "The petrol volume is estimated in number of liters", "fees": "The road fees are estimated in number of DH", "nbr_ligne": "The number of lines is estimated in number of lines", "special_consumption": "The special consumption is estimated in number of liters", "special_value": "Numeric values without decimal separator, if multiple values, separate by a dash (-)"}, "messages": {"create_success": "Route created successfully", "create_error": "Failed to create route", "update_success": "Route updated successfully", "update_error": "Failed to update route"}, "fields": {"departure": "Departure", "destination": "Destination", "delay": "Delay", "distance": "Distance", "petrol_volume": "Petrol Volume", "fees": "Road Fees", "is_active": "Is Active", "is_city": "Is City Delivery", "nbr_ligne": "Number of Lines", "special_consumption": "Special Consumption", "special_value": "Special Value", "archive": "Archive", "pricing_list": "Pricing Standard", "good_type": "Good Type", "price": "Price"}}, "natureOfArticleForm": {"title": "Add Nature of Article", "create": "Create", "edit": "Edit", "create_title": "Create Nature of Article", "edit_title": "Edit Nature of Article", "messages": {"create_success": "Nature of article created successfully", "create_error": "Failed to create nature of article", "update_success": "Nature of article updated successfully", "update_error": "Failed to update nature of article"}, "fields": {"name": "Name of the nature of article"}}, "profile": {"title": "Profile", "save": "Save Changes", "desc": " Manage your account information", "upload": "Upload New Photo", "saving": "Saving...", "picDes": "Allowed PNG or JPEG. Max size of 800K.", "message": {"update_success": "Profile updated successfully", "update_error": "Failed to update profile"}, "cancel": "Cancel", "fields": {"firstName": "First Name", "lastName": "Last Name", "email": "Email", "picture": "Profile Picture"}}, "articleGroupForm": {"title": "Add Article Group", "create": "Create", "edit": "Edit", "create_title": "Create Article Group", "edit_title": "Edit Article Group", "messages": {"create_success": "Article group created successfully", "create_error": "Failed to create article group", "update_success": "Article group updated successfully", "update_error": "Failed to update article group"}, "fields": {"name": "Name of the article group"}}, "stationForm": {"title": "Add Station", "create": "Create", "edit": "Edit", "create_title": "Create Station", "edit_title": "Edit Station", "messages": {"create_success": "Station created successfully", "create_error": "Failed to create station", "update_success": "Station updated successfully", "update_error": "Failed to update station"}, "fields": {"station_name": "Station Name", "station_localisation": "Station Location", "email": "Notification Email", "active": "Active"}}, "changePasswordForm": {"title": "Change Users Password", "back": "Back to Users", "reset": {"title": "Please change your default password as per company policy", "description": "Reset the Password for user:"}, "description": "Enter a new password for the user", "save": "Save Changes", "resetButton": "Reset Password", "fields": {"password": "Password", "confirmPassword": "Confirm Password", "setNewPassword": "Set New Password"}}, "usersForm": {"create": "Create", "edit": "Edit", "title": "Add User", "create_title": "Create User", "edit_title": "Edit User", "sections": {"basic_info": "Basic Information", "password": "Password", "permissions": "Permissions", "accountDetails": "Account Details"}, "descriptions": {"title": "First, enter a username and password. Then, you can modify more user options.", "username": "Required. 150 characters or fewer. Letters, digits and @/./+/-/_ only.", "updatePassword": "Passwords are not stored in plain text, so there is no way to see this user's password, but you can change the password using", "password": {"1": "Your password can't be too similar to your other personal information.", "2": "Your password must contain at least 8 characters.", "3": "Your password can't be a commonly used password.", "4": "Your password can't be entirely numeric."}, "confirmPassword": "Enter the same password as before, for verification.", "active": "Designates whether this user should be treated as active. Uncheck this instead of deleting accounts.", "groupStatus": "Designates whether the user can log into this admin site.", "isSuperuser": "Designates that this user has all permissions without explicitly assigning them."}, "messages": {"create_success": "User created successfully", "create_error": "Failed to create user", "update_success": "User updated successfully", "update_error": "Failed to update user", "reset_password_success": "Password reset successfully", "reset_password_error": "Failed to reset password"}, "fields": {"username": "Username", "email": "Email", "firstName": "First Name", "password": "Password", "confirmPassword": "Confirm Password", "lastName": "Last Name", "groupStatus": "Group Status", "isSuperuser": "Is Superuser", "isActive": "Is Active", "groups": "Groups", "joinedDate": "Joinned Data", "typeOfOperation": "Type of Operation", "lastConnection": "Last Login", "permissions": "Permissions", "mainDepartment": "Main Department", "annexDepartments": "Annex Departments", "otherPhone": "Other Phone", "language": "Language", "supervisor": "Supervisor"}}, "dataValidationForm": {"create_title": "Create Data Validation", "edit_title": "Edit Data Validation", "fields": {"levelTovalidate": "Level to Validate", "dataType": "Data Type", "validationLevels": "Validation Levels"}, "messages": {"create_success": "Data validation created successfully", "create_error": "Failed to create data validation", "update_success": "Data validation updated successfully", "update_error": "Failed to update data validation"}, "dataTypeOptions": {"PURCHAGES_REQUESTS": "Purchase Requests", "UPDATE_REQUESTS": "Update Requests", "MISSIONS_EXPENSES": "Mission Expenses", "INVOICES": "Invoices"}}, "agencyForm": {"title": "Add Agency", "create": "Create", "edit": "Edit", "create_title": "Create Agency", "edit_title": "Edit Agency", "messages": {"create_success": "Agency created successfully", "create_error": "Failed to create agency", "update_success": "Agency updated successfully", "update_error": "Failed to update agency"}, "fields": {"name": "Name of the agency or station", "address": "Address of the agency or station", "company": "Customer", "is_active": "Active"}}, "customerForm": {"title": "Add Customer", "create": "Create", "edit": "Edit", "create_title": "Create Customer", "edit_title": "Edit Customer", "messages": {"create_success": "Customer created successfully", "create_error": "Failed to create customer", "update_success": "Customer updated successfully", "update_error": "Failed to update customer"}, "fields": {"sap_uid": "External ID", "name": "Name of the customer", "citerne_price_diff": "Price Difference on Citerne", "other_info": "Other Informations", "address": "Address", "phone1": "Phone", "phone2": "Another Phone", "groupcode": "Group Code", "zipcode": "Zip Code", "mailaddres": "Mail Address", "cmpprivate": "Cmp Private", "mailzipcod": "Mail Zip Code", "addid": "Register of Commerce", "currency": "<PERSON><PERSON><PERSON><PERSON>", "cardtype": "Card Type", "lictradnum": "Lic Trad Num"}}, "driverForm": {"title": "Add Driver", "create": "Create", "edit": "Edit", "create_title": "Create Driver", "edit_title": "Edit Driver", "driversFunction": {"CHAUFFEUR LIGNE": "LINE DRIVER", "CHAUFFEUR VILLE": "CITY DRIVER", "CHAUFFEUR": "DRIVER", "CHAUFFEUR BUREAU": "OFFICE DRIVER"}, "driversSexe": {"Masculin": "Male", "Féminin": "Female"}, "messages": {"create_success": "Driver created successfully", "create_error": "Failed to create driver", "update_success": "Driver updated successfully", "update_error": "Failed to update driver"}, "fields": {"matricule": "Matricule", "fonction": "Function", "dated_embauche_societe": "Employment Date", "sexe": "Sex", "date_de_naissance": "Birth Date", "first_name": "First Name", "last_name": "Last Name", "address": "Address", "phone_number": "Phone Number"}}, "userDepartmentForm": {"title": "Add User Department", "create": "Create", "edit": "Edit", "create_title": "Create User Department", "edit_title": "Edit User Department", "messages": {"create_success": "User department created successfully", "create_error": "Failed to create user department", "update_success": "User department updated successfully", "update_error": "Failed to update user department"}, "fields": {"name": "Name"}}, "orderForm": {"title": "Add Order", "create": "Create", "edit": "Edit", "requestValidation": {"title": "Request Validation", "message": "Are you sure you want to request validation for this order?"}, "messages": {"create_success": "Order created successfully", "create_error": "Failed to create order", "update_success": "Order updated successfully", "update_error": "Failed to update order", "validation_request_sent": "Validation request sent successfully", "validation_request_error": "Failed to send validation request", "invoice_generated": "Invoice generated successfully", "invoice_error": "Failed to generate invoice"}, "validationStatusOptions": {"EN ATTENTE": "PENDING", "VALIDE": "VALIDATED", "REJETE": "REJECTED"}, "prestationOptions": {"CITERNE": "TANK", "PLATEAU": "FLATBED", "BENNE": "DUMP TRUCK"}, "fields": {"customer": "Customer", "prestation": "Prestation", "is_city": "City", "validated": "Validated", "operations": "Operations"}, "userDepartmentForm": {"title": "Add User Department", "create": "Create", "edit": "Edit", "fields": {"name": "Name"}}, "missionExpenseForm": {"title": "Mission Expenses", "create_title": "Create Expense", "edit_title": "Edit Expense", "delete_title": "Delete Expense", "delete_message": "Are you sure you want to delete this expense?", "lines_title": "Consumption Lines", "fields": {"code": "Document Number", "date": "Date", "order": "Order", "canceled": "Canceled", "validation": "Validation", "validationReason": "Validation Reason", "expenseLines": "Consumption Lines", "validationLine": "Validation Line", "submit": "Submit Validation"}, "expenseLine": {"driver": "Driver", "obs": "Observations", "product": "Product", "beNumber": "BE Number", "bcNumber": "BC Number/Container Number", "vehicle": "Vehicle", "leave": "Leave", "destination": "Destination", "fuelConsumption": "Fuel Consumption (L)", "quantity": "Quantity (L/T)", "missionFees": "Mission Fees", "fuelCost": "Fuel Cost", "order": "Order"}, "validation": {"status": {"pending": "Pending", "validated": "Validated", "rejected": "Rejected"}}, "messages": {"create_success": "Expense created successfully", "create_error": "Error creating expense", "update_success": "Expense updated successfully", "update_error": "Error updating expense", "delete_success": "Expense deleted successfully", "delete_error": "Error deleting expense"}}, "operationForm": {"tractor": "Tractor/Truck", "trailer": "Trailer", "route": "Route", "direction": "Direction", "be_number": "BE Number", "bc_number": "BC Number/Container Number", "product": "Services", "product_nature": "Product Nature", "client_agency": "Client Agency", "quantity": "Quantity(L/T)", "road_fees": "Road Fees", "fuel_volume": "Fuel Volume (L)", "driver": "Driver", "validation": "Validation", "unit_price": "Unit Price", "unloaded_quantity": "Unloaded Quantity"}, "addOperation": "Add Additional Operation", "deleteDialog": {"title": "Confirm Delete", "message": "Are you sure you want to delete this operation? This action is irreversible.", "confirm": "Confirm", "cancel": "Cancel"}}, "invoiceForm": {"title": "Add Invoice", "create": "Create", "edit": "Edit", "fields": {"customer": "Customer", "order": "Order", "conditions": "Conditions", "echeance": "Echeance", "bank_ref": "Bank Ref", "account_number": "Account Number", "amount": "Amount", "total_amount": "Total Amount", "invoice_lines": "Invoice Lines"}}, "invoiceLineForm": {"title": "Add Invoice Line", "create": "Create", "edit": "Edit", "fields": {"truck": "Truck", "be_number": "BE Number", "bc_number": "BC Number", "destination": "Destination", "product": "Product", "price": "Price", "qty": "Quantity", "amount_ht": "Amount HT"}}, "missionExpenseOtherForm": {"title": "Other Mission Expenses", "create_title": "Create Expense", "edit_title": "Edit Expense", "delete_title": "Delete Expense", "delete_message": "Are you sure you want to delete this expense?", "lines_title": "Expense Lines", "fields": {"code": "Code", "code_placeholder": "Code", "date": "Date", "priority": "Priority", "validation": "Validation", "validationReason": "Validation Reason", "expenseLines": "Expense Lines", "totalAmount": "Total Amount"}, "expenseLine": {"designation": "Designation", "date": "Date", "duration": "Duration", "unitPrice": "Unit Price", "quantity": "Quantity", "amount": "Amount", "operations": "Operations"}, "validation": {"status": {"pending": "Pending", "validated": "Validated", "rejected": "Rejected"}}, "priority": {"normal": "Normal", "urgent": "<PERSON><PERSON>", "current": "Current", "important": "Important"}, "buttons": {"add_line": "Add Line", "remove_line": "Remove Line"}, "messages": {"create_success": "Expense created successfully", "create_error": "Error creating expense", "update_success": "Expense updated successfully", "update_error": "Error updating expense", "delete_success": "Expense deleted successfully", "delete_error": "Error deleting expense"}}, "groupForm": {"title": "Add Group", "create": "Create", "edit": "Edit", "fields": {"name": "Name", "permissions": "Permissions"}}, "productForm": {"title": "Add Product", "create": "Create", "edit": "Edit", "fields": {"name": "Name", "service": "Service"}}, "vehicleForm": {"title": "Add Vehicle", "create": "Create", "edit": "Edit", "fields": {"code": "Code", "number_plate": "Number Plate", "consumption_card_number": "Consumption Card Number", "vehicle_type": "Vehicle Type", "container": "Container", "kilometer_old": "Kilometer Old", "kilometer_new": "Kilometer New", "petrol_consumption": "Petrol Consumption", "long": "Longitude", "lat": "Latitude", "is_active": "Is Active"}}, "consumptionOperationsForm": {"title": "Add Consumption Operations", "create": "Create", "edit": "Edit", "addDistribution": "Add Distribution", "addDistributionError": "Error adding distribution", "deleteDistribution": "Delete Distribution", "deleteDistributionError": "Error deleting distribution", "deleteDistributionDialog": {"title": "Confirm Delete", "message": "Are you sure you want to delete this distribution?", "cancel": "Cancel", "confirm": "Confirm"}, "messages": {"create_success": "Consumption created successfully", "create_error": "Error creating consumption", "update_success": "Consumption updated successfully", "update_error": "Error updating consumption", "delete_success": "Consumption deleted successfully", "delete_error": "Error deleting consumption"}, "fields": {"operation": "Operation", "type_conso": "Type Conso", "validation": "Validation", "volumeTotal": "Volume Total", "code": "Code", "distributions": "Distributions", "station": "Station", "volume": "Volume", "cash_card": "Payment Type", "card_number": "Card Number"}}}, "common": {"apply": "Apply", "hours": "Hours", "minutes": "Minutes", "seconds": "Seconds", "waiting": "Waiting", "days": "Days", "months": "Months", "years": "Years", "filter": "Filter", "clear": "Clear", "normal": "Normal", "reverse": "Reverse", "cancel": "Cancel", "valid": "<PERSON><PERSON>", "reject": "Rejected", "back": "Back", "yes": "Yes", "no": "No", "export": "Export", "import": "Import", "print": "Print", "true": "True", "false": "False", "actions": {"delete": "Delete", "edit": "Edit", "view": "View", "deleteSelected": "Delete Selected", "exportCSV": "Export to CSV", "cancel": "Cancel", "import": "Import", "create": "Create", "confirm": "Confirm", "save": "Save", "action": "Action"}, "status": {"canceled": "Canceled", "completed": "Completed", "unloaded": "Unloaded", "charged": "Charged", "started": "Started", "pending": "Pending"}, "dialog": {"delete": {"title": "Confirm Delete", "message": "Are you sure you want to delete this item? This action cannot be undone.", "confirm": "Delete", "cancel": "Cancel"}}, "messages": {"deleteSuccess": "Item deleted successfully", "deleteError": "Failed to delete item"}}, "Mission Expenses": "Mission Expenses", "Search expenses...": "Search expenses...", "Add Expense": "Add Expense", "Oui": "Yes", "Non": "No", "menu": {"dashboard": "Dashboard", "apppages": "Apps & Pages", "analytics": "Analytics", "crm": "CRM", "ecommerce": "Ecommerce", "email": "Email", "chat": "Cha<PERSON>", "calendar": "Calendar", "invoice": "Invoice", "users": "Users", "userProfile": "User Profile", "accountSettings": "Account <PERSON><PERSON>", "faq": "FAQ", "helpCenter": "Help Center", "pricing": "Pricing", "financial": {"main": "Financial Management", "invoices": "Invoices", "missionExpenses": "Mission Expenses", "missionExpensesOther": "Mission Expenses Other"}, "settings": {"main": "Settings", "countries": "Countries", "destinations": "Destinations"}, "services": {"main": "Services", "travel": "Travel", "orders": "Orders", "products": "Products", "articles": "Articles", "extractions": "Extractions", "immobilizations": "Immobilizations", "invoiceItems": "Invoice Items", "pricing": "Pricing", "pricingForBenne": "Pricing for <PERSON>ne", "natureOfProducts": "Nature of Products", "articleGroups": "Article Groups"}, "vehicles": {"main": "Vehicles Management", "vehicles": "Vehicles", "trucks": "Trucks"}, "userManagement": {"main": "User Management", "groups": "Groups", "userDepartments": "Departments", "permissions": "Permissions", "driver": "Driver", "customers": "Customers", "agencies": "Agencies", "validation": "Validations", "users": "User"}, "notifications": {"main": "Notifications", "notifications": "Notifications"}, "operations": {"main": "Operations", "articles": "Articles", "requests": "Requests", "extractions": "Extractions", "articleGroups": "Article Groups", "articleTypes": "Article Types", "operations": "Operations", "products": "Products", "binRates": "Bin Rates", "standardRates": "Standard Rates", "trips": "Trips"}, "fuelCunsumption": {"main": "Fuel Cunsumption", "operationConsumption": "Operation Consumption", "fuelConsumption": "Fuel Consumption", "station": "Stations"}, "formsTables": "Forms & Tables", "formElements": "Form Elements", "textField": "Text Field", "select": "Select", "checkbox": "Checkbox", "radio": "Radio", "customInputs": "Custom Inputs", "textarea": "Textarea", "autocomplete": "Autocomplete", "datePickers": "Date Pickers", "switch": "Switch", "fileUploader": "File Uploader", "editor": "Editor", "slider": "Slide<PERSON>", "inputMask": "Input Mask", "formLayouts": "Form Layouts", "formValidation": "Form Validation", "formWizard": "Form Wizard", "table": "Table", "muiDataGrid": "<PERSON>i <PERSON>Grid", "charts": "Charts", "apex": "Apex", "recharts": "Recharts", "chartjs": "ChartJS", "others": "Others", "accessControl": "Access Control", "menuLevels": "Menu Levels", "menuLevel21": "Menu Level 2.1", "menuLevel22": "Menu Level 2.2", "menuLevel31": "Menu Level 3.1", "menuLevel32": "Menu Level 3.2", "disabledMenu": "Disabled <PERSON><PERSON>", "raiseSupport": "Raise Support", "documentation": "Documentation"}, "groups": {"form": {"title": {"create": "Create New Group", "edit": "Edit Group"}, "fields": {"name": "Group Name", "name_placeholder": "Enter group name", "permissions": "Permissions", "permissions_placeholder": "Select permissions"}, "buttons": {"back": "Back to Groups", "cancel": "Cancel", "save_return": "Save & Return", "save_edit": "Save & Edit", "save_add": "Save & Add New"}, "messages": {"create_success": "Group created successfully", "update_success": "Group updated successfully", "create_error": "Failed to create group", "update_error": "Failed to update group"}}}, "product": {"form": {"create_title": "Create Product", "edit_title": "Edit Product", "name": "Name", "name_required": "Name is required", "service": "Service", "service_required": "Service is required", "loading_services_error": "Error loading services. Please try again later.", "create_success": "Product created successfully", "create_error": "Failed to create product", "update_success": "Product updated successfully", "update_error": "Failed to update product", "saving": "Saving...", "buttons": {"cancel": "Cancel", "save": "Save"}}}, "article": {"form": {"create_title": "Create Article", "edit_title": "Edit Article", "code": "SAP Code", "code_required": "SAP Code is required", "label": "Label", "label_required": "Label is required", "group": "Article Group", "related_documents": "Related Documents", "related_document": "Related Document", "related_articles": "Related Article", "units": {"cubicMeters": "Cubic Meters", "liters": "Liters", "kilograms": "Kilograms", "tonnes": "<PERSON><PERSON>", "units": "Units"}, "group_required": "Article group is required", "operation_type": "Operation Type", "operation_type_required": "Operation type is required", "measure_unit": "Measure Unit", "measure_unit_required": "Measure unit is required", "create_success": "Article created successfully", "create_error": "Failed to create article", "update_success": "Article updated successfully", "update_error": "Failed to update article"}}}