import { api } from 'src/lib/api'
import { InfiniteFetchParams } from 'src/types/models'
import { Agencies, AgenciesResponse, FetchAgencyParam } from 'src/types/models/agencies'

export const fetchAgencies = async (params: Partial<FetchAgencyParam>) => {
  const res = await api.get<AgenciesResponse>('/agencies/', { params })

  return res.data
}

export const fetchInfiniteAgency = async ({
  pageParam,
  searchQuery,
  company
}: InfiniteFetchParams & Partial<FetchAgencyParam>) => {
  const response = await fetchAgencies({
    limit: 100,
    offset: pageParam * 100,
    search: searchQuery,
    company
  })

  const agency = response.results.map(item => ({
    label: item.name,
    value: item.id,

    ...item
  }))
  const filteredProduct = searchQuery
    ? agency.filter(p => p.name.toLowerCase().includes(searchQuery.toLowerCase()))
    : agency

  return {
    items: filteredProduct,
    nextPage: response.next ? pageParam + 1 : null,
    totalItems: response.count
  }
}

export const getAgencyById = async (id: string) => {
  const res = await api.get<Agencies>(`/agencies/${id}`)

  return res.data
}

export const createAgency = async (agencyData: any) => {
  const res = await api.post('/agencies/', agencyData)

  return res.data
}

export const updateAgency = async (id: string, agencyData: any) => {
  const res = await api.patch(`/agencies/${id}/`, agencyData)

  return res.data
}

export const deleteAgency = async (ids: string[]) => {
  const res = await api.post('/agencies/delete_selected/', { ids })

  return res.data
}
