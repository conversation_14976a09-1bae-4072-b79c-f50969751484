import { api } from 'src/lib/api'
import { CreateRoutePayload, FetchRoutesParams, Route, RoutesResponse } from 'src/types/models/routes'

export const fetchRoutes = async (params: Partial<FetchRoutesParams>) => {
  const response = await api.get<RoutesResponse>('/trajet/', { params })

  return response.data
}

export const getRouteById = async (id: string) => {
  const response = await api.get<Route>(`/trajet/${id}/`)

  return response.data
}

export const createRoute = async (data: CreateRoutePayload) => {
  const response = await api.post('/trajet/', data)

  return response.data
}

export const updateRoute = async (id: string, data: Partial<CreateRoutePayload>) => {
  const response = await api.patch(`/trajet/${id}/`, data)

  return response.data
}

export const deleteRoute = async (id: string) => {
  const response = await api.delete(`/trajet/${id}/`)

  return response.data
}

export const deleteSelectedRoutes = async (ids: string[]) => {
  const response = await api.post('/trajet/delete_selected/', { ids })

  return response.data
}
