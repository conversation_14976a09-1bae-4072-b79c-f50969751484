import { api } from '../../lib/api'

export const fetchDocumentType = async () => {
  const res = await api.get('/document-type/')

  return res.data
}

export const fetchDocumentTypeById = async (id: string) => {
  const res = await api.get(`/document-type/${id}`)

  return res.data
}

export const createDocumentType = async (data: any) => {
  const res = await api.post('/document-type/', data)

  return res.data
}

export const updateDocumentType = async (id: string, data: any) => {
  const res = await api.patch(`/document-type/${id}/`, data)

  return res.data
}

export const deleteDocumentType = async (id: string) => {
  const res = await api.delete(`/document-type/${id}/`)

  return res.data
}

export const deleteSelectedDocumentType = async (ids: string[]) => {
  const res = await api.post('/document-type/delete_selected/', { ids })

  return res.data
}
