import { api } from 'src/lib/api'
import {
  FetchUserDepartmentParams,
  UserDepartmentRequestPayload,
  UserDepartmentResponse
} from 'src/types/models/user-department'
import { InfiniteFetchParams } from 'src/types/models'

export const fetchInfiniteUserDepartments = async ({ pageParam, searchQuery }: InfiniteFetchParams) => {
  const response = await fetchUserDepartments({
    limit: 100,
    offset: pageParam * 100
  })

  const userDepartments = response.results.map(item => ({
    label: item.name,
    value: item.id,

    ...item
  }))

  const filteredUserDepartments = searchQuery
    ? userDepartments.filter(ud => ud.name.toLowerCase().includes(searchQuery.toLowerCase()))
    : userDepartments

  return {
    items: filteredUserDepartments,
    nextPage: response.next ? pageParam + 1 : null,
    totalItems: response.count
  }
}

export const fetchUserDepartments = async (params: Partial<FetchUserDepartmentParams>) => {
  const response = await api.get<UserDepartmentResponse>('/departments/', { params })

  return response.data
}

export const getUserDepartmentById = async (id: string) => {
  const response = await api.get(`/departments/${id}/`)

  return response.data
}

export const deleteSelectedUserDepartments = async (ids: string[]) => {
  const res = await api.post('/departments/delete_selected/', { ids })

  return res.data
}

export const createUserDepartment = async (userDepartmentData: UserDepartmentRequestPayload) => {
  const response = await api.post('/departments/', userDepartmentData)

  return response.data
}

export const updateUserDepartment = async (id: string, userDepartmentData: Partial<UserDepartmentRequestPayload>) => {
  const response = await api.patch(`/departments/${id}/`, userDepartmentData)

  return response.data
}

export const deleteUserDepartment = async (id: string) => {
  const response = await api.delete(`/departments/${id}/`)

  return response.data
}
