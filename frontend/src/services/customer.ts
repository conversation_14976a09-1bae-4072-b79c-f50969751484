import { api } from 'src/lib/api'
import { InfiniteFetchParams, PaginatedResponse } from 'src/types/models'
import { Customer, CustomerRequestPayload, CustomerParams } from 'src/types/models/customer'

export const fetchCustomers = async (params: Partial<CustomerParams>): Promise<PaginatedResponse<Customer>> => {
  const response = await api.get('/customers/', { params })

  return response.data
}

export const fetchInfiniteCustomers = async ({ pageParam, searchQuery }: InfiniteFetchParams) => {
  const response = await fetchCustomers({
    limit: 100,
    offset: pageParam * 100
  })

  const customers = response.results.map(item => ({
    label: item.name,
    value: item.id,

    ...item
  }))

  const filteredUsers = searchQuery
    ? customers.filter(c => c.name.toLowerCase().includes(searchQuery.toLowerCase()))
    : customers

  return {
    items: filteredUsers,
    nextPage: response.next ? pageParam + 1 : null,
    totalItems: response.count
  }
}

export const getCustomerById = async (id: string): Promise<Customer> => {
  const response = await api.get(`/customers/${id}/`)

  return response.data
}

export const createCustomer = async (customerData: CustomerRequestPayload): Promise<Customer> => {
  const response = await api.post('/customers/', customerData)

  return response.data
}

export const updateCustomer = async (id: string, customerData: Partial<CustomerRequestPayload>): Promise<Customer> => {
  const response = await api.patch(`/customers/${id}/`, customerData)

  return response.data
}

export const deleteSelectedCustomers = async (ids: string[]) => {
  const res = await api.post('/customers/delete_selected/', { ids })

  return res.data
}

export const deleteCustomer = async (id: string): Promise<void> => {
  await api.delete(`/customers/${id}/`)
}
