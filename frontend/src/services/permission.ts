import { api } from 'src/lib/api'
import { BaseFetchParams, InfiniteFetchParams } from 'src/types/models'
import { PermissionData } from 'src/types/models/permission'

export const fetchInfinitePermissions = async ({ pageParam, searchQuery }: InfiniteFetchParams) => {
  const response = await fetchPermissions({
    limit: 100,
    offset: pageParam * 100
  })

  const permissions = response.results.map(item => ({
    label: item.name,
    value: item.id,

    ...item
  }))

  const filteredPermissions = searchQuery
    ? permissions.filter(p => p.name.toLowerCase().includes(searchQuery.toLowerCase()))
    : permissions

  return {
    items: filteredPermissions,
    nextPage: response.next ? pageParam + 1 : null,
    totalItems: response.count
  }
}

export const fetchPermissions = async (params: Partial<BaseFetchParams>) => {
  const res = await api.get<PermissionData>('/permissions/', { params })

  return res.data
}

export const getPermissionById = async (id: string) => {
  const res = await api.get(`/permissions/${id}/`)

  return res.data
}

// export const createPermission = async (data: any) => {
//   const res = await api.post('/permissions/', data)

//   return res.data
// }

// export const updatePermission = async (id: string, data: any) => {
//   const res = await api.patch(`/permissions/${id}/`, data)

//   return res.data
// }

// export const deletePermission = async (id: string) => {
//   const res = await api.delete(`/permissions/${id}/`)

//   return res.data
// }
