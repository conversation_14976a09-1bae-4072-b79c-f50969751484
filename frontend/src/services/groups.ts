import { api } from 'src/lib/api'
import { BaseFetchParams, InfiniteFetchParams } from 'src/types/models'
import { GroupsResponse } from 'src/types/models/user-management/groups'

export const fetchInfiniteGroups = async ({ pageParam, searchQuery }: InfiniteFetchParams) => {
  const response = await fetchGroups({
    limit: 100,
    offset: pageParam * 100
  })

  const groups = response.results.map(item => ({
    label: item.name,
    value: item.id,

    ...item
  }))

  const filteredGroups = searchQuery
    ? groups.filter(g => g.name.toLowerCase().includes(searchQuery.toLowerCase()))
    : groups

  return {
    items: filteredGroups,
    nextPage: response.next ? pageParam + 1 : null,
    totalItems: response.count
  }
}

export const fetchGroups = async (params: Partial<BaseFetchParams>) => {
  const res = await api.get<GroupsResponse>('/groups/', { params })

  return res.data
}

export const createGroup = async (data: any) => {
  const res = await api.post('/groups/', data)

  return res.data
}

export const updateGroup = async (id: string, data: any) => {
  const res = await api.patch(`/groups/${id}/`, data)

  return res.data
}

export const getGroupById = async (id: string) => {
  const res = await api.get(`/groups/${id}`)

  return res.data
}

export const deleteGroup = async (id: string) => {
  const response = await api.delete(`/groups/${id}/`)

  return response.data
}
