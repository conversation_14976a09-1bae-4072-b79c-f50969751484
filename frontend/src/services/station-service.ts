import { api } from 'src/lib/api'
import { fetchStationsServiceParams } from 'src/types/models/station-service'

export const fetchStations = async (params: Partial<fetchStationsServiceParams>) => {
  const response = await api.get('/stations/', { params })

  return response.data
}

export const getStationById = async (id: string) => {
  const response = await api.get(`/stations/${id}/`)

  return response.data
}

export const createStation = async (stationData: any) => {
  const response = await api.post('/stations/', stationData)

  return response.data
}

export const updateStation = async (id: string, stationData: any) => {
  const response = await api.patch(`/stations/${id}/`, stationData)

  return response.data
}

export const deleteStation = async (id: string) => {
  const response = await api.delete(`/stations/${id}/`)

  return response.data
}

export const deleteSelectedStations = async (ids: string[]) => {
  const response = await api.post('/stations/delete_selected/', { ids })

  return response.data
}
