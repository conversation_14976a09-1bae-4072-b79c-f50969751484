import { api } from 'src/lib/api'
import { MissionExpenseLine, MissionExpenseLineFormData, QueryParams } from 'src/types/models/me-lines-other'

interface PaginatedResponse<T> {
  count: number
  next: string | null
  previous: string | null
  results: T[]
}

export const fetchMissionExpenseLines = async (
  params?: QueryParams
): Promise<PaginatedResponse<MissionExpenseLine>> => {
  const response = await api.get('/me-lines-other/', { params })

  return response.data
}

export const getMissionExpenseLineById = async (id: string): Promise<MissionExpenseLine> => {
  const response = await api.get(`/me-lines-other/${id}/`)

  return response.data
}

export const createMissionExpenseLine = async (data: MissionExpenseLineFormData): Promise<MissionExpenseLine> => {
  const response = await api.post('/me-lines-other/', data)

  return response.data
}

export const updateMissionExpenseLine = async (
  id: string,
  data: Partial<MissionExpenseLineFormData>
): Promise<MissionExpenseLine> => {
  const response = await api.patch(`/me-lines-other/${id}/`, data)

  return response.data
}

export const deleteMissionExpenseLine = async (id: string): Promise<void> => {
  await api.delete(`/me-lines-other/${id}/`)
}
