import { api } from 'src/lib/api'
import { MissionExpenseOtherFormData, MissionExpenseOtherParams } from 'src/types/models/mission-expense-other'

export const fetchMissionExpensesOthers = async (params: Partial<MissionExpenseOtherParams>) => {
  const response = await api.get('/mission-expenses-other/', { params })

  return response.data
}

export const getMissionExpenseOtherById = async (id: string) => {
  const response = await api.get(`/mission-expenses-other/${id}/`)

  return response.data
}

export const createMissionExpenseOther = async (missionExpenseOtherData: MissionExpenseOtherFormData) => {
  const payload = {
    priority: missionExpenseOtherData.priority,
    lines: missionExpenseOtherData.expenseLines?.map(line => {
      const operation = line.operations?.map(v => v.id)

      return {
        description: line.designation,
        date: line.date,
        duration: line.duration,
        fees: line.price,
        qty: line.quantity,
        amount: line.amount,
        operation_ids: operation
      }
    })
  }
  const response = await api.post('/mission-expenses-other/', payload)

  return response.data
}

export const updateMissionExpenseOther = async (id: string, missionExpenseOtherData: MissionExpenseOtherFormData) => {
  const data = {
    priority: missionExpenseOtherData.priority,
    lines: missionExpenseOtherData.expenseLines?.map(line => {
      const operation = line.operations?.map(v => v.id)

      return {
        description: line.designation,
        date: line.date,
        duration: line.duration,
        fees: line.price,
        qty: line.quantity,
        amount: line.amount,
        operation_ids: operation
      }
    })
  }
  const response = await api.patch(`/mission-expenses-other/${id}/`, data)

  return response.data
}

export const deleteMissionExpenseOther = async (id: string) => {
  const response = await api.delete(`/mission-expenses-other/${id}/`)

  return response.data
}

export const validateMissionExpensesOthers = async (id: string) => {
  const response = await api.post(`/mission-expenses-other/${id}/validate/`)

  return response.data
}

export const rejectMissionExpensesOthers = async (id: string, raison: string) => {
  const response = await api.post(`/mission-expenses-other/${id}/reject/`, { raison })

  return response.data
}

export const addMissionExpenseLine = async (data: any) => {
  const response = await api.post(`/me-lines-other/`, data)

  return response.data
}

export const updateMissionExpenseLine = async (id: string, data: any) => {
  const response = await api.patch(`/me-lines-other/${id}/`, data)

  return response.data
}

export const deleteMissionExpenseOtherLine = async (id: string) => {
  const response = await api.delete(`/me-lines-other/${id}/`)

  return response.data
}
