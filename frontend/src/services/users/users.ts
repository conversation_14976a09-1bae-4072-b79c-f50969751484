import { File } from 'buffer'
import { api } from 'src/lib/api'
import { BaseFetchParams, InfiniteFetchParams } from 'src/types/models'
import { PasswordPayload, Supervisor, UserRequestPayload } from 'src/types/models/user-management/users'

export const fetchUsers = async (params: Partial<BaseFetchParams>) => {
  const response = await api.get('/users/', { params })

  return response.data
}

export const getUserById = async (id: string) => {
  const response = await api.get(`/users/${id}/`)

  return response.data
}

export const createUser = async (userData: UserRequestPayload) => {
  delete userData.confirm_password
  const response = await api.post('/users/', userData)

  return response.data
}

export const updateUser = async (id: string, userData: Partial<UserRequestPayload>) => {
  delete userData.confirm_password

  const data: Partial<UserRequestPayload> = {
    ...userData,
    groups: userData.groups.map((g: any) => g.id),
    user_permissions: userData.user_permissions.map((p: any) => p.id),
    annex_departments_ids: userData.annex_departments_ids.map((an: any) => an.id)
  }

  const response = await api.patch(`/users/${id}/`, data)

  return response.data
}
export const changePassword = async (id: string, passwordData: PasswordPayload) => {
  const response = await api.patch(`/users/${id}/change-password`, passwordData)

  return response.data
}

export const changeAdminPassword = async (id: string, passwordData: PasswordPayload) => {
  const response = await api.patch(`/users/${id}/admin-change-password`, passwordData)

  return response.data
}

export const deleteUser = async (id: string) => {
  const response = await api.delete(`/users/${id}/`)

  return response.data
}

export const deleteSelectedUsers = async (ids: string[]) => {
  const response = await api.post('/users/delete_selected/', { ids })

  return response.data
}

export const activateSelected = async (ids: string[]) => {
  const response = await api.post('/users/activate_selected/', { ids })

  return response.data
}

export const deactivateSelected = async (ids: string[]) => {
  const response = await api.post('/users/deactivate_selected/', { ids })

  return response.data
}

export const userSupervisors = async (params: Partial<BaseFetchParams>) => {
  const response = await api.get<Supervisor[]>('/users/supervisors/', { params })

  return response.data
}

export const updateProfileImage = async (image: any, id: string) => {
  const response = await api.put(`/users/${id}/update_profile_photo/`, { picture: image })

  return response.data
}
