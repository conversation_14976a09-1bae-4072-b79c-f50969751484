import { api } from 'src/lib/api'
import { BaseFetchParams } from 'src/types/models'

export const fetchValidation = async (params: Partial<BaseFetchParams>) => {
  const response = await api.get('/data-validations/', { params })

  return response.data
}

export const getValidationById = async (id: string) => {
  const response = await api.get(`/data-validations/${id}/`)

  return response.data
}

export const updateValidation = async (id: string, validationData: any) => {
  const response = await api.patch(`/data-validations/${id}/`, validationData)

  return response.data
}

export const deleteSelectedValidations = async (ids: string[]) => {
  const response = await api.post('/data-validations/delete_selected/', { ids })

  return response.data
}

export const createValidation = async (validationData: any) => {
  const response = await api.post('/data-validations/', validationData)

  return response.data
}
