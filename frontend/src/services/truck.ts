import { api } from 'src/lib/api'
import { TruckParam } from 'src/types/models/truck'

export const fetchTrucks = async (params: TruckParam) => {
  const response = await api.get('/truck/', { params })

  return response.data
}

export const getTruckById = async (id: string) => {
  const response = await api.get(`/truck/${id}/`)

  return response.data
}

export const createTruck = async (truckData: any) => {
  const response = await api.post('/truck/', truckData)

  return response.data
}

export const updateTruck = async (id: string, truckData: any) => {
  const response = await api.patch(`/truck/${id}/`, truckData)

  return response.data
}

export const deleteTruck = async (id: string) => {
  const response = await api.delete(`/truck/${id}/`)

  return response.data
}
