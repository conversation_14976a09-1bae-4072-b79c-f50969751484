import { api } from 'src/lib/api'
import { CreateInvoiceData, FetchInvoiceParams } from 'src/types/models/invoice'

export const fetchInvoices = async (params: Partial<FetchInvoiceParams>) => {
  const response = await api.get('/invoices/', { params })

  return response.data
}

export const getInvoiceById = async (id: string) => {
  const response = await api.get(`/invoices/${id}/`)

  return response.data
}

export const createInvoice = async (invoiceData: CreateInvoiceData) => {
  const response = await api.post('/invoices/', invoiceData)

  return response.data
}

export const updateInvoice = async (id: string, invoiceData: Partial<CreateInvoiceData>) => {
  const response = await api.patch(`/invoices/${id}/`, invoiceData)

  return response.data
}

export const deleteInvoice = async (id: string) => {
  const response = await api.delete(`/invoices/${id}/`)

  return response.data
}

export const addToOdoo = async ({ invoice_ids }: { invoice_ids: string[] }) => {
  const response = await api.post(`/invoices/transfer_to_odoo/`, { invoice_ids })

  return response.data
}

export const getInvoiceStats = async () => {
  const response = await api.get('/invoices/stats/')

  return response.data
}
