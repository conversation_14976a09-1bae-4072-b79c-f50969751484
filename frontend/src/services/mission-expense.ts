import { api } from 'src/lib/api'
import { MissionExpenseParams } from 'src/types/models/mission-expense'

export const fetchMissionExpenses = async (params: Partial<MissionExpenseParams>) => {
  const response = await api.get('/mission-expenses/', { params })

  return response.data
}

export const getMissionExpenseById = async (id: string) => {
  const response = await api.get(`/mission-expenses/${id}/`)

  return response.data
}

export const createMissionExpense = async (missionExpenseData: any) => {
  const response = await api.post('/mission-expenses/', missionExpenseData)

  return response.data
}

export const cancelMissionExpeses = async (ids: string[]) => {
  const res = await api.post('mission-expenses/cancel/', { mission_expense_ids: ids })

  return res.data
}

export const validateMissionExpenses = async (id: string) => {
  const response = await api.post(`/mission-expenses/${id}/validate/`)

  return response.data
}

export const rejectMissionExpenses = async (id: string, raison: string) => {
  const response = await api.post(`/mission-expenses/${id}/reject/`, { raison })

  return response.data
}

export const deleteSelectedMissionExpense = async (ids: string[]) => {
  const res = await api.post('mission-expenses/delete_selected/', { ids })

  return res.data
}

export const updateMissionExpense = async (id: string, missionExpenseData: any) => {
  const response = await api.patch(`/mission-expenses/${id}/`, missionExpenseData)

  return response.data
}

export const deleteMissionExpense = async (id: string) => {
  const response = await api.delete(`/mission-expenses/${id}/`)

  return response.data
}

export const getMissionExpensesStats = async () => {
  const response = await api.get('/mission-expenses/stats/')

  return response.data
}

export const getMissionExpensesDashboardStats = async () => {
  const response = await api.get('/mission-expenses/dashboard_stats/')

  return response.data
}
