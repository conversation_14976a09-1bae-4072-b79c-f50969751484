import { api } from 'src/lib/api'
import { FetchNotificationParams } from 'src/types/models/notification'

export const fetchNotifications = async (params: Partial<FetchNotificationParams>) => {
  const response = await api.get('/notification/', { params })

  return response.data
}

export const getNotificationById = async (id: string) => {
  const response = await api.get(`/notification/${id}`)

  return response.data
}

// Create a new notification
export const createNotification = async (data: any) => {
  const response = await api.post('/notification/', data)

  return response.data
}

// Update an existing notification
export const updateNotification = async (id: string, data: any) => {
  const response = await api.put(`/notification/${id}`, data)

  return response.data
}

// Delete a notification
export const deleteNotification = async (id: string) => {
  const response = await api.delete(`/notification/${id}`)

  return response.data
}

export const deleteSelectedNotifications = async (ids: string[]) => {
  const response = await api.post('/notification/delete_selected/', { ids })

  return response.data
}
