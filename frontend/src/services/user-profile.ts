import { api } from 'src/lib/api'

interface UpdateProfileImageResponse {
  avatar: string
  message: string
}

export const updateProfileImage = async (file: File): Promise<UpdateProfileImageResponse> => {
  const formData = new FormData()
  formData.append('avatar', file)

  const response = await api.patch('/users/profile/avatar/', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })

  return response.data
}

export const deleteProfileImage = async (): Promise<void> => {
  await api.delete('/users/profile/avatar/')
}