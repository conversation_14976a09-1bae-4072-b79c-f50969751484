import moment from 'moment'
import { api } from 'src/lib/api'
import { InfiniteFetchParams } from 'src/types/models'
import { FetchDriverParams, Driver, DriverRequest, DriverResponse, DriverFormData } from 'src/types/models/drivers'

export const fetchDriver = async (params: Partial<FetchDriverParams>) => {
  const response = await api.get<DriverResponse>('/drivers/', { params })

  return response.data
}

export const fetchInfiniteDriver = async ({ pageParam, searchQuery }: InfiniteFetchParams) => {
  const response = await fetchDriver({
    limit: 100,
    offset: pageParam * 100
  })

  const driver = response.results.map(item => ({
    label: `${item.first_name} ${item.last_name}`.trim(),
    value: item.id,

    ...item
  }))
  const filteredDriver = searchQuery
    ? driver.filter(
        p =>
          p.first_name.toLowerCase().includes(searchQuery.toLowerCase()) ||
          p.last_name.toLowerCase().includes(searchQuery.toLowerCase())
      )
    : driver

  return {
    items: filteredDriver,
    nextPage: response.next ? pageParam + 1 : null,
    totalItems: response.count
  }
}

export const getDriverById = async (id: string) => {
  const response = await api.get<Driver>(`/drivers/${id}/`)

  return response.data
}

export const createDriver = async (driverData: DriverFormData) => {
  const data = {
    ...driverData,
    dated_embauche_societe: moment(driverData.dated_embauche_societe).format('YYYY-MM-DD'),
    date_de_naissance: moment(driverData.date_de_naissance).format('YYYY-MM-DD')
  }

  const response = await api.post('/drivers/', data)

  return response.data
}

export const updateDriver = async (driverData: Partial<DriverFormData>, id?: string) => {
  const data = {
    ...driverData,
    dated_embauche_societe: moment(driverData.dated_embauche_societe).format('YYYY-MM-DD'),
    date_de_naissance: moment(driverData.date_de_naissance).format('YYYY-MM-DD')
  }
  const response = await api.patch(`/drivers/${id}/`, data)

  return response.data
}

export const deleteSelectedDrivers = async (ids: string[]) => {
  const res = await api.post('/drivers/delete_selected/', { ids })

  return res.data
}

export const deleteDriver = async (id: string) => {
  const response = await api.delete(`/drivers/${id}/`)

  return response.data
}
