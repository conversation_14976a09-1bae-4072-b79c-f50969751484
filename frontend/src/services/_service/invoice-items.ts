import { api } from 'src/lib/api'
import { InfiniteFetchParams } from 'src/types/models'
import {
  CreateInvoicePayload,
  FetchInvoiceItemsParams,
  InvoiceItemRequestPayload,
  InvoiceItemResponse
} from 'src/types/models/_services/invoice-items'

export const fetchInvoiceItems = async (params: Partial<FetchInvoiceItemsParams>) => {
  const response = await api.get<InvoiceItemResponse>('/invoice-items/', { params })

  return response.data
}

export const infiniteInvoiceItem = async ({ pageParam, searchQuery }: InfiniteFetchParams) => {
  const response = await fetchInvoiceItems({
    limit: 100,
    offset: pageParam * 100
  })

  const invoiceItem = response.results.map(item => ({
    value: String(item.id),
    label: item.order_code,

    ...item
  }))

  const filteredinvoiceItem = searchQuery
    ? invoiceItem.filter(
        v =>
          v.order_code.toLowerCase().includes(searchQuery.toLowerCase()) ||
          v.be_number.toLowerCase().includes(searchQuery.toLowerCase())
      )
    : invoiceItem

  return {
    items: filteredinvoiceItem,
    nextPage: response.next ? pageParam + 1 : null,
    totalItems: response.count
  }
}

export const getInvoiceItemById = async (id: string) => {
  const response = await api.get(`/invoice-items/${id}/`)

  return response.data
}

export const createInvoiceItem = async (invoiceItemData: InvoiceItemRequestPayload) => {
  const response = await api.post('/invoice-items/', invoiceItemData)

  return response.data
}

export const updateInvoiceItem = async (id: string, invoiceItemData: Partial<any>) => {
  const response = await api.patch(`/invoice-items/${id}/`, invoiceItemData)

  return response.data
}

export const deleteInvoiceItem = async (id: string) => {
  const response = await api.delete(`/invoice-items/${id}/`)

  return response.data
}

export const createInvoice = async (invoiceItemData: CreateInvoicePayload) => {
  const response = await api.post('/invoice-items/create_invoice/', invoiceItemData)

  return response.data
}
