import { api } from 'src/lib/api'
import { InfiniteFetchParams } from 'src/types/models'
import {
  FetchNatureOfArticlesParams,
  NatureOfArticle,
  NatureOfArticleRequestPayload,
  NatureOfArticleResponse
} from 'src/types/models/_services/nature-of-articles'

export const fetchNatureOfArticles = async (params: Partial<FetchNatureOfArticlesParams>) => {
  const response = await api.get<NatureOfArticleResponse>('/nature_of_articles/', { params })

  return response.data
}

export const fetchInfiniteNatureOfArticles = async ({ pageParam, searchQuery }: InfiniteFetchParams) => {
  const response = await fetchNatureOfArticles({
    limit: 100,
    offset: pageParam * 100
  })

  const product = response.results.map(item => ({
    label: item.name,
    value: item.id,
    ...item
  }))

  const filteredProduct = searchQuery
    ? product.filter(p => p.name.toLowerCase().includes(searchQuery.toLowerCase()))
    : product

  return {
    items: filteredProduct,
    nextPage: response.next ? pageParam + 1 : null,
    totalItems: response.count
  }
}
export const getNatureOfArticleById = async (id: string) => {
  const response = await api.get<NatureOfArticle>(`/nature_of_articles/${id}/`)

  return response.data
}

export const createNatureOfArticle = async (natureOfArticleData: NatureOfArticleRequestPayload) => {
  const response = await api.post('/nature_of_articles/', natureOfArticleData)

  return response.data
}

export const updateNatureOfArticle = async (
  id: string,
  natureOfArticleData: Partial<NatureOfArticleRequestPayload>
) => {
  const response = await api.patch(`/nature_of_articles/${id}/`, natureOfArticleData)

  return response.data
}

export const deleteNatureOfArticle = async (id: string) => {
  const response = await api.delete(`/nature_of_articles/${id}/`)

  return response.data
}

export const deleteSelectedNatureOfArticles = async (ids: string[]) => {
  const response = await api.post('/nature_of_articles/delete_selected/', { ids })

  return response.data
}
