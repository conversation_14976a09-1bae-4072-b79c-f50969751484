import { api } from 'src/lib/api'
import { InfiniteFetchParams } from 'src/types/models'
import {
  DestinationParams,
  DestinationRequestPayload,
  DestinationsResponse,
  Destination
} from 'src/types/models/_services/destinations'

export const fetchDestinations = async (params: Partial<DestinationParams>) => {
  const response = await api.get<DestinationsResponse>('/destinations/', { params })

  return response.data
}

export const fetchInfiniteDestination = async ({ pageParam, searchQuery }: InfiniteFetchParams) => {
  const response = await fetchDestinations({
    limit: 100,
    offset: pageParam * 100
  })

  const path = response.results.map(item => ({
    label: item.name,
    value: item.id,
    ...item
  }))

  const filteredPath = searchQuery ? path.filter(p => p.name.toLowerCase().includes(searchQuery.toLowerCase())) : path

  return {
    items: filteredPath,
    nextPage: response.next ? pageParam + 1 : null,
    totalItems: response.count
  }
}

export const getDestinationById = async (id: string) => {
  const response = await api.get<Destination>(`/destinations/${id}/`)

  return response.data
}

export const createDestination = async (destinationData: DestinationRequestPayload) => {
  const response = await api.post<Destination>('/destinations/', destinationData)

  return response.data
}

export const updateDestination = async (id: string, destinationData: Partial<DestinationRequestPayload>) => {
  const response = await api.patch<Destination>(`/destinations/${id}/`, destinationData)

  return response.data
}

export const deleteDestination = async (id: string) => {
  const response = await api.delete(`/destinations/${id}/`)

  return response.data
}

export const deleteSelectedDestinations = async (ids: string[]) => {
  const response = await api.post('/destinations/delete_selected/', { ids })

  return response.data
}
