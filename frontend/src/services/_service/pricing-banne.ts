import { api } from 'src/lib/api'
import { FetchPricingBenneParams } from 'src/types/models/_services/price-banne'

export const fetchPricingBenne = async (params: Partial<FetchPricingBenneParams>) => {
  const response = await api.get('/pricing_benne_per_route/', { params })

  return response.data
}

export const getPricingBenneById = async (id: string) => {
  const response = await api.get(`/pricing_benne_per_route/${id}/`)

  return response.data
}

export const createPricingBenne = async (pricingBenneData: any) => {
  const response = await api.post('/pricing_benne_per_route/', pricingBenneData)

  return response.data
}

export const updatePricingBenne = async (id: string, pricingBenneData: any) => {
  const response = await api.patch(`/pricing_benne_per_route/${id}/`, pricingBenneData)

  return response.data
}

export const deletePricingBenne = async (id: string) => {
  const response = await api.delete(`/pricing_benne_per_route/${id}/`)

  return response.data
}
