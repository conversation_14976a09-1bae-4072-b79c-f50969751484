import { api } from 'src/lib/api'
import { InfiniteFetchParams } from 'src/types/models'
import {
  Station,
  StationRequestPayload,
  FetchStationsParams,
  StationResponse
} from 'src/types/models/_services/stations'

export const fetchStations = async (params: Partial<FetchStationsParams>) => {
  const response = await api.get<StationResponse>('/stations/', { params })

  return response.data
}

export const infiniteFuelStattion = async ({ pageParam, searchQuery }: InfiniteFetchParams) => {
  const response = await fetchStations({
    limit: 100,
    offset: pageParam * 100
  })
  const fuelStation = response.results.map(station => ({
    label: station.station_name,
    value: station.id,
    ...station
  }))

  const filteredfuelStation = searchQuery
    ? fuelStation.filter(v => v.station_name.toLowerCase().includes(searchQuery.toLowerCase()))
    : fuelStation

  return {
    items: filteredfuelStation,
    nextPage: response.next ? pageParam + 1 : null,
    totalItems: response.count
  }
}
export const getStationById = async (id: string) => {
  const response = await api.get<Station>(`/stations/${id}/`)

  return response.data
}

export const createStation = async (stationData: StationRequestPayload) => {
  const response = await api.post<Station>('/stations/', stationData)

  return response.data
}

export const updateStation = async (id: string, stationData: Partial<StationRequestPayload>) => {
  const response = await api.patch<Station>(`/stations/${id}/`, stationData)

  return response.data
}

export const deleteStation = async (id: string) => {
  const response = await api.delete(`/stations/${id}/`)

  return response.data
}

export const bulkStationCreate = async (data: any) => {
  const response = await api.post('/stations/import_stations/', data)

  return response.data
}

export const deleteSelectedStations = async (ids: string[]) => {
  const response = await api.post('/stations/delete_selected/', { ids })

  return response.data
}
