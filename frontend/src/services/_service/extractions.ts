import { api } from 'src/lib/api'

export const fetchExtractions = async (params: any) => {
  const response = await api.get('/extractions/', { params })

  return response.data
}

export const getExtractionById = async (id: string) => {
  const response = await api.get(`/extractions/${id}/`)

  return response.data
}

export const createExtraction = async (extractionData: any) => {
  const response = await api.post('/extractions/', extractionData)

  return response.data
}

export const updateExtraction = async (id: string, extractionData: any) => {
  const response = await api.patch(`/extractions/${id}/`, extractionData)

  return response.data
}

export const deleteExtraction = async (id: string) => {
  const response = await api.delete(`/extractions/${id}/`)

  return response.data
}
