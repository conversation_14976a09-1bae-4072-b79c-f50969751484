import { api } from 'src/lib/api'
import { FetchImmobilizationParams } from 'src/types/models/_services/imobilization'

export const fetchImmobilizations = async (params: Partial<FetchImmobilizationParams>) => {
  const response = await api.get('/immobilizations/', { params })

  return response.data
}

export const getImmobilizationById = async (id: string) => {
  const response = await api.get(`/immobilizations/${id}/`)

  return response.data
}

export const createImmobilization = async (immobilizationData: any) => {
  const response = await api.post('/immobilizations/', immobilizationData)

  return response.data
}

export const updateImmobilization = async (id: string, immobilizationData: any) => {
  const response = await api.patch(`/immobilizations/${id}/`, immobilizationData)

  return response.data
}

export const deleteImmobilization = async (id: string) => {
  const response = await api.delete(`/immobilizations/${id}/`)

  return response.data
}

export const deleteSelectedImmobilizations = async (ids: string[]) => {
  const response = await api.post('/immobilizations/delete_selected/', { ids })

  return response.data
}
