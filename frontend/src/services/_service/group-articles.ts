import { api } from 'src/lib/api'
import { FetchGroupArticlesParams, GroupArticleRequestPayload } from 'src/types/models/_services/group-article'

export const fetchGroupArticles = async (params: Partial<FetchGroupArticlesParams>) => {
  const response = await api.get('/group_articles/', { params })

  return response.data
}

export const getGroupArticleById = async (id: string) => {
  const response = await api.get(`/group_articles/${id}/`)

  return response.data
}

export const createGroupArticle = async (groupArticleData: GroupArticleRequestPayload) => {
  const response = await api.post('/group_articles/', groupArticleData)

  return response.data
}

export const updateGroupArticle = async (id: string, groupArticleData: Partial<GroupArticleRequestPayload>) => {
  const response = await api.patch(`/group_articles/${id}/`, groupArticleData)

  return response.data
}

export const deleteGroupArticle = async (id: string) => {
  const response = await api.delete(`/group_articles/${id}/`)

  return response.data
}

export const deleteSelectedGroupArticles = async (ids: string[]) => {
  const response = await api.post('/group_articles/delete_selected/', { ids })

  return response.data
}
