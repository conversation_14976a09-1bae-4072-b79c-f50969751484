import { api } from 'src/lib/api'
import { FetchPricingRouteParams } from 'src/types/models/_services/pricing-route'

export const fetchPricingRoute = async (params: Partial<FetchPricingRouteParams>) => {
  const response = await api.get('/pricing_routes/', { params })

  return response.data
}

export const getPricingRouteById = async (id: string) => {
  const response = await api.get(`/pricing_routes/${id}/`)

  return response.data
}

export const createPricingRoute = async (pricingRouteData: any) => {
  const response = await api.post('/pricing_routes/', pricingRouteData)

  return response.data
}

export const updatePricingRoute = async (id: string, pricingRouteData: any) => {
  const response = await api.patch(`/pricing_routes/${id}/`, pricingRouteData)

  return response.data
}

export const deletePricingRoute = async (id: string) => {
  const response = await api.delete(`/pricing_route/${id}/`)

  return response.data
}
