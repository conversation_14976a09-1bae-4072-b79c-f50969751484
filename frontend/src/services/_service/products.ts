import { api } from 'src/lib/api'
import { InfiniteFetchParams } from 'src/types/models'
import {
  Product,
  ProductParams,
  ProductResponse,
  ProuductRequestPayload,
  SingleProductResponse
} from 'src/types/models/_services/products'
import { fetchArticles } from './article'

export const fetchProducts = async (params: Partial<ProductParams>) => {
  const response = await api.get<ProductResponse>('/products/', { params })

  return response.data
}

export const fetchInfiniteProduct = async ({ pageParam, searchQuery }: InfiniteFetchParams) => {
  const response = await fetchArticles({
    limit: 100,
    offset: pageParam * 100
  })

  console.log(response)

  const product = response.results.map(item => ({
    value: item.id,
    ...item
  }))

  const filteredProduct = searchQuery
    ? product.filter(p => p.label.toLowerCase().includes(searchQuery.toLowerCase()))
    : product

  return {
    items: filteredProduct,
    nextPage: response.next ? pageParam + 1 : null,
    totalItems: response.count
  }
}
export const getProductById = async (id: string) => {
  const response = await api.get<SingleProductResponse>(`/products/${id}/`)

  return response.data
}

export const createProduct = async (productData: ProuductRequestPayload) => {
  const response = await api.post('/products/', productData)

  return response.data
}

export const updateProduct = async (id: string, productData: Partial<ProuductRequestPayload>) => {
  const response = await api.patch(`/products/${id}/`, productData)

  return response.data
}

export const deleteProduct = async (id: string) => {
  const response = await api.delete(`/products/${id}/`)

  return response.data
}
