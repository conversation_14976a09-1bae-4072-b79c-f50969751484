import { api } from 'src/lib/api'
import { TravelParams } from 'src/types/models/_services/travel'

export const fetchTravels = async (params: Partial<TravelParams>) => {
  const response = await api.get('/travels/', { params })

  return response.data
}

export const getTravelById = async (id: string) => {
  const response = await api.get(`/travels/${id}/`)

  return response.data
}

export const createTravel = async (travelData: any) => {
  const response = await api.post('/travels/', travelData)

  return response.data
}

export const updateTravel = async (id: string, travelData: any) => {
  const response = await api.patch(`/travels/${id}/`, travelData)

  return response.data
}

export const deleteTravel = async (id: string) => {
  const response = await api.delete(`/travels/${id}/`)

  return response.data
}

export const startTravel = async (id: string, startData: any) => {
  const response = await api.post(`/travels/${id}/start/`, startData)

  return response.data
}

export const endTravel = async (id: string, endData: any) => {
  const response = await api.post(`/travels/${id}/end/`, endData)

  return response.data
}

export const cancelTravel = async (id: string, cancelData: any) => {
  const response = await api.post(`/travels/${id}/cancel/`, cancelData)

  return response.data
}

export const loadTravel = async (id: string, loadData: any) => {
  const response = await api.post(`/travels/${id}/load_truck/`, loadData)

  return response.data
}

export const unloadTravel = async (id: string, unloadData: any) => {
  const response = await api.post(`/travels/${id}/unload_truck/`, unloadData)

  return response.data
}

export const endBlocking = async (id: string, endBlockingData: any) => {
  const response = await api.post(`/travels/${id}/end_blocking/`, endBlockingData)

  return response.data
}

export const reportBlocking = async (id: string, reportBlockingData: any) => {
  const response = await api.post(`/travels/${id}/report_blocking/`, reportBlockingData)

  return response.data
}

export const setOnWay = async (id: string, setOnwayData: any) => {
  const response = await api.post(`/travels/${id}/set_on_way/`, setOnwayData)

  return response.data
}

export const getTravelsStats = async () => {
  const response = await api.get('/travels/stats/')

  return response.data
}

export const getTravelsDashboardStats = async () => {
  const response = await api.get('/travels/dashboard_stats/')

  return response.data
}

export const deleteSelectedTravels = async (ids: string[]) => {
  const response = await api.post('/travels/delete_selected/', { ids })

  return response.data
}
