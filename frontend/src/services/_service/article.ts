import { api } from 'src/lib/api'
import { Article, ArticleFormData, ArticlesResponse, FetchArticlesParams } from 'src/types/models/_services/article'

export const fetchArticles = async (params: Partial<FetchArticlesParams>) => {
  const response = await api.get<ArticlesResponse>('/articles/', { params })

  return response.data
}

export const getArticleById = async (id: string) => {
  const response = await api.get<Article>(`/articles/${id}/`)

  return response.data
}

export const createArticle = async (articleData: ArticleFormData) => {
  const response = await api.post('/articles/', articleData)

  return response.data
}

export const updateArticle = async (id: string, articleData: Partial<ArticleFormData>) => {
  const response = await api.patch(`/articles/${id}/`, articleData)

  return response.data
}

export const deleteArticle = async (id: string) => {
  const response = await api.delete(`/articles/${id}/`)

  return response.data
}

export const deleteSelectedArticles = async (ids: string[]) => {
  const response = await api.post('/articles/delete_selected/', { ids })

  return response.data
}
