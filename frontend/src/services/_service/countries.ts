import { api } from 'src/lib/api'
import { CountryParams, CountryRequestPayload } from 'src/types/models/_services/countries'

export const fetchCountries = async (params: Partial<CountryParams>) => {
  const response = await api.get('/countries/', { params })

  return response.data
}

export const getCountryById = async (id: string) => {
  const response = await api.get(`/countries/${id}/`)

  return response.data
}

export const createCountry = async (countryData: any) => {
  const response = await api.post('/countries/', countryData)

  return response.data
}

export const updateCountry = async (id: string, countryData: any) => {
  const response = await api.patch(`/countries/${id}/`, countryData)

  return response.data
}

export const deleteCountry = async (id: string) => {
  const response = await api.delete(`/countries/${id}/`)

  return response.data
}
