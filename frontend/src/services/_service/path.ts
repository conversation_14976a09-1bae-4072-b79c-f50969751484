import { api } from 'src/lib/api'
import { InfiniteFetchParams } from 'src/types/models'
import { FetchPathParams, Path, PathResponse } from 'src/types/models/_services/path'

export const fetchPaths = async (params: Partial<FetchPathParams>) => {
  const response = await api.get<PathResponse>('/trajet/', { params })

  return response.data
}

export const fetchInfinitePath = async ({ pageParam, searchQuery }: InfiniteFetchParams) => {
  const response = await fetchPaths({
    limit: 100,
    offset: pageParam * 100,
    search: searchQuery
  })

  const path = response.results.map(item => ({
    label: item.name,
    value: item.id,
    ...item
  }))

  const filteredPath = searchQuery ? path.filter(p => p.name.toLowerCase().includes(searchQuery.toLowerCase())) : path

  return {
    items: filteredPath,
    nextPage: response.next ? pageParam + 1 : null,
    totalItems: response.count
  }
}

export const deletePath = async (id: string) => {
  const response = await api.delete(`/trajet/${id}/`)

  return response.data
}

export const getPathById = async (id: string) => {
  const response = await api.get<Path>(`/trajet/${id}/`)

  return response.data
}

export const createPath = async (pathData: any) => {
  const response = await api.post('/trajet/', pathData)

  return response.data
}

export const updatePath = async (id: string, pathData: any) => {
  const response = await api.patch(`/trajet/${id}/`, pathData)

  return response.data
}
