import { api } from 'src/lib/api'
import { PaginatedResponse } from 'src/types/models'
import { InvoiceItemRequestPayload } from 'src/types/models/_services/invoice-items'
import { Order, OrderFormValues, OrderParams, OrderRequestPayload, OrderStats } from 'src/types/models/_services/orders'

export const fetchOrders = async (params: Partial<OrderParams>) => {
  const response = await api.get<PaginatedResponse<Order>>('/orders/', { params })

  return response.data
}

export const addMissingTrips = async (ids: string[]) => {
  const response = await api.post(`/orders/strart_missing_travel/`, { order_ids: ids })

  return response.data
}

export const requestValidation = async (id: string) => {
  const response = await api.post(`/orders/${id}/validation_request/`)

  return response.data
}

export const updateOrder = async (id: string, orderData: Partial<OrderRequestPayload>) => {
  const data = {
    prestation: orderData.prestation,
    is_city: orderData.is_city,
    customer_id: orderData.customer_id,
    operation_list: orderData?.operations?.map(operation => {
      const mappedOperation: any = {
        tractor_id: operation.tractor_id,
        path_id: operation.path_id,
        direction: operation.direction,
        driver_id: operation.driver_id,
        be_number: operation.be_number,
        bc_number: operation.bc_number,
        agence_id: operation.agence_id,
        qty: operation.qty,
        road_fees: operation.road_fees,
        product_id: Number(operation.product_id),
        petrol_volume: Number(operation.petrol_volume),
        validation: 'EN ATTENTE'
      }

      if (operation.nature_product_id !== null) {
        mappedOperation.nature_product_id = operation.nature_product_id
      }

      if (operation.trailer_id !== null) {
        mappedOperation.trailer_id = operation.trailer_id
      }

      return mappedOperation
    })
  }

  const response = await api.patch(`/orders/${id}/`, data)

  return response.data
}

export const getOrderById = async (id: string) => {
  const response = await api.get<Order>(`/orders/${id}/`)

  return response.data
}

export const createOrder = async (orderData: OrderFormValues) => {
  const data = {
    customer_id: orderData.customer,
    prestation: orderData.prestation,
    is_city: orderData.is_city,
    operation_list: orderData.operations.map(operation => {
      const mappedOperation = {
        ...operation,
        product_id: Number(operation.product_id),
        petrol_volume: Number(operation.petrol_volume)
      }

      if (operation.nature_product_id) {
        mappedOperation.nature_product_id = operation.nature_product_id
      }

      return mappedOperation
    })
  }
  const response = await api.post('/orders/', data)

  return response.data
}

export const getOrderOperations = async (id: string) => {
  const response = await api.get(`/orders/${id}/lines/`)

  return response.data
}

export const deleteOrder = async (id: string) => {
  const response = await api.delete(`/orders/${id}/`)

  return response.data
}

export const validateOrder = async (id: string) => {
  const response = await api.post(`/orders/${id}/validate/`)

  return response.data
}

export const rejectOrder = async (id: string, reason: string) => {
  const response = await api.post(`/orders/${id}/reject/`, { reason })

  return response.data
}

export const createOrderLines = async (payload: Partial<InvoiceItemRequestPayload>) => {
  const response = await api.post('/invoice-items/', payload)

  return response.data
}

export const updateOrderOperationLine = async (id: string, payload: Partial<InvoiceItemRequestPayload>) => {
  const response = await api.patch(`/invoice-items/${id}/`, payload)

  return response.data
}

export const getOrdersStats = async () => {
  const response = await api.get('/orders/stats/')

  return response.data
}

export const ordersDashboardStats = async () => {
  const response = await api.get<OrderStats>('/orders/dashboard_stats/')

  return response.data
}
