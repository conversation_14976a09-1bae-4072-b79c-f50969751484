import { api } from 'src/lib/api'
import { InfiniteFetchParams } from 'src/types/models'
import { FetchVehiclesParams, Vehicle, VehicleRequest, VehicleResponse } from 'src/types/models/vehicles'

export const fetchVehicles = async (params: Partial<FetchVehiclesParams>) => {
  const response = await api.get<VehicleResponse>('/vehicle/', { params })

  return response.data
}

export const fetchInfiniteTractors = async ({ pageParam, searchQuery }: InfiniteFetchParams) => {
  const response = await fetchVehicles({
    limit: 100,
    offset: pageParam * 100,
    vehicle_type_combo: 'TRACTEUR+CAMION'
  })

  const vehicle = response.results.map(item => ({
    label: item.number_plate,
    value: item.id,

    ...item
  }))
  const filteredVehicle = searchQuery
    ? vehicle.filter(
        v =>
          v.number_plate.toLowerCase().includes(searchQuery.toLowerCase()) ||
          v.code.toLowerCase().includes(searchQuery.toLowerCase())
      )
    : vehicle

  return {
    items: filteredVehicle,
    nextPage: response.next ? pageParam + 1 : null,
    totalItems: response.count
  }
}

export const fetchInfiniteTrailers = async ({ pageParam, searchQuery }: InfiniteFetchParams) => {
  const response = await fetchVehicles({
    limit: 100,
    offset: pageParam * 100,
    vehicle_type: 'REMORQUE'
  })

  const vehicle = response.results.map(item => ({
    label: item.number_plate,
    value: item.id,

    ...item
  }))
  const filteredVehicle = searchQuery
    ? vehicle.filter(
        v =>
          v.number_plate.toLowerCase().includes(searchQuery.toLowerCase()) ||
          v.code.toLowerCase().includes(searchQuery.toLowerCase())
      )
    : vehicle

  return {
    items: filteredVehicle,
    nextPage: response.next ? pageParam + 1 : null,
    totalItems: response.count
  }
}

export const getVehicleById = async (id: string) => {
  const response = await api.get<Vehicle>(`/vehicle/${id}/`)

  return response.data
}

export const createVehicle = async (vehicleData: VehicleRequest) => {
  const response = await api.post('/vehicle/', vehicleData)

  return response.data
}

export const updateVehicle = async (vehicleData: VehicleRequest, id?: string) => {
  const response = await api.patch(`/vehicle/${id}/`, vehicleData)

  return response.data
}

export const deleteVehicle = async (id: string) => {
  const response = await api.delete(`/vehicle/${id}/`)

  return response.data
}

export const fetchTractorOptions = async (
  search: string,
  page: number
): Promise<{
  options: Array<{
    value: string
    label: string
    id: number | string
    code: string
  }>
  hasMore: boolean
  additional: {
    page: number
  }
}> => {
  const response = await fetchVehicles({
    limit: 100,
    offset: (page - 1) * 100
  })

  const vehicles = response.results.map(item => ({
    value: String(item.id),
    label: item.code,
    id: item.id,
    code: item.code
  }))

  const filteredVehicles = search
    ? vehicles.filter(
        v => v.code.toLowerCase().includes(search.toLowerCase()) || v.label.toLowerCase().includes(search.toLowerCase())
      )
    : vehicles

  return {
    options: filteredVehicles,
    hasMore: !!response.next,
    additional: {
      page: page + 1
    }
  }
}
