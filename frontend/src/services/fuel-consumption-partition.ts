import { api } from 'src/lib/api'
import { FetchFuelConsumptionPartitionParams } from 'src/types/models/fuel-consumption-partition'

export const fetchFuelConsumptionPartitions = async (params: Partial<FetchFuelConsumptionPartitionParams>) => {
  const response = await api.get('/fuel-consumption-repartitions/', { params })

  return response.data
}

export const getFuelConsumptionPartitionById = async (id: string) => {
  const response = await api.get(`/fuel-consumption-repartitions/${id}/`)

  return response.data
}

export const createFuelConsumptionPartition = async (payload: any) => {
  const response = await api.post('/fuel-consumption-repartitions/', payload)

  return response.data
}

export const updateFuelConsumptionPartition = async (id: string, payload: any) => {
  const response = await api.patch(`/fuel-consumption-repartitions/${id}/`, payload)

  return response.data
}

export const deleteFuelConsumptionPartition = async (id: string) => {
  const response = await api.delete(`/fuel-consumption-repartitions/${id}/`)

  return response.data
}
