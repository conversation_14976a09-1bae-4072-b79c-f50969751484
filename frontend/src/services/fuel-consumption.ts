import { api } from 'src/lib/api'
import { FetchFuelConsumptionParams } from 'src/types/models/fuel-consumption'

export const fetchFuelConsumptions = async (params: Partial<FetchFuelConsumptionParams>) => {
  const response = await api.get('/fuel-consumptions/', { params })

  return response.data
}

export const getFuelConsumptionById = async (id: string) => {
  const response = await api.get(`/fuel-consumptions/${id}/`)

  return response.data
}

export const createFuelConsumption = async (fuelConsumptionData: any) => {
  const response = await api.post('/fuel-consumptions/', fuelConsumptionData)

  console.log(response.data)

  return response.data
}

export const updateFuelConsumption = async (id: string, fuelConsumptionData: any) => {
  const response = await api.patch(`/fuel-consumptions/${id}/`, fuelConsumptionData)

  return response.data
}

export const deleteFuelConsumption = async (id: string) => {
  const response = await api.delete(`/fuel-consumptions/${id}/`)

  return response.data
}

export const deleteSelectedFuelConsumption = async (ids: string[]) => {
  const response = await api.post('/fuel-consumptions/delete_selected/', { ids })

  return response.data
}
