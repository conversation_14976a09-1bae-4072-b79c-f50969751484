import { useState, useEffect } from 'react'
import { <PERSON>ton, <PERSON><PERSON>ield, Typo<PERSON>, Stack } from '@mui/material'
import { DynamicFields, DynamicPreviewProps } from 'src/types/email-template'

export default function DynamicPreview({ templateHtml }: DynamicPreviewProps): JSX.Element {
  const [dynamicFields, setDynamicFields] = useState<DynamicFields>({})
  const [previewHtml, setPreviewHtml] = useState(templateHtml)

  useEffect(() => {
    const regex = /{{([^{}]+)}}/g
    const matches = [...templateHtml.matchAll(regex)]
    const fields: DynamicFields = {}

    matches.forEach(match => {
      const fieldName = match[1].trim()
      if (!fields[fieldName]) {
        fields[fieldName] = ''
      }
    })

    setDynamicFields(fields)
    setPreviewHtml(templateHtml)
  }, [templateHtml])

  const handleFieldChange = (field: string, value: string) => {
    setDynamicFields(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const updatePreview = () => {
    let updatedHtml = templateHtml

    Object.entries(dynamicFields).forEach(([field, value]) => {
      const regex = new RegExp(`{{${field}}}`, 'g')
      updatedHtml = updatedHtml.replace(regex, value || `{{${field}}}`)
    })

    setPreviewHtml(updatedHtml)
    const iframe = document.getElementById('preview-iframe') as HTMLIFrameElement | null
    if (iframe) {
      const iframeDoc = iframe.contentDocument || iframe.contentWindow?.document
      if (iframeDoc) {
        iframeDoc.open()
        iframeDoc.write(updatedHtml)
        iframeDoc.close()
      }
    }
  }

  return (
    <Stack spacing={3}>
      {Object.keys(dynamicFields).length === 0 ? (
        <Typography color='text.secondary'>
          No dynamic fields found in this template. Add fields like in the editor.
        </Typography>
      ) : (
        <>
          {Object.entries(dynamicFields).map(([field, value]) => (
            <TextField
              key={field}
              label={field}
              value={value}
              onChange={e => handleFieldChange(field, e.target.value)}
              placeholder={`Enter ${field}`}
              fullWidth
              variant='outlined'
              size='small'
            />
          ))}

          <Button onClick={updatePreview} variant='contained' fullWidth>
            Update Preview
          </Button>
        </>
      )}
    </Stack>
  )
}
