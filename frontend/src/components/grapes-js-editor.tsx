import { useEffect, useImperativeHandle, forwardRef, useRef } from 'react'
import 'grapesjs/dist/css/grapes.min.css'
import { GrapeJSEditorProps, GrapeJSEditorRef } from 'src/types/email-template'

const GrapeJSEditor = forwardRef<GrapeJSEditorRef, GrapeJSEditorProps>(({ initialData }, ref) => {
  const editorRef = useRef<any>(null)

  useImperativeHandle(ref, () => ({
    getHtml: () => editorRef.current?.getHtml() || '',
    getCss: () => editorRef.current?.getCss() || '',
    getComponents: () => editorRef.current?.getComponents() || []
  }))

  useEffect(() => {
    const loadGrapesJS = async () => {
      try {
        let grapesjs
        let newsletter
        let basicBlocks

        if (typeof window !== 'undefined') {
          grapesjs = require('grapesjs')
          newsletter = require('grapesjs-preset-newsletter')
          basicBlocks = require('grapesjs-blocks-basic')

          grapesjs = grapesjs.default || grapesjs
          newsletter = newsletter.default || newsletter
          basicBlocks = basicBlocks.default || basicBlocks
        }

        if (!grapesjs) {
          throw new Error('Failed to load GrapesJS library')
        }

        editorRef.current = grapesjs.init({
          container: '#gjs',
          height: '100%',
          width: '100%',
          storageManager: false,
          plugins: [newsletter, basicBlocks],
          pluginsOpts: {
            newsletter: {},
            basicBlocks: {}
          },

          panels: {
            defaults: [
              {
                id: 'basic-actions',
                buttons: [
                  {
                    id: 'visibility',
                    active: true,
                    className: 'btn-toggle-borders',
                    label: '<i class="fa fa-eye"></i>',
                    command: 'sw-visibility'
                  }
                ]
              }
            ]
          }
        })

        const panelManager = editorRef.current.Panels

        panelManager.addPanel({
          id: 'dynamic-content-panel',
          visible: true,
          buttons: []
        })

        if (initialData) {
          if (initialData.html) {
            editorRef.current.setComponents(initialData.html)
          }

          if (initialData.css) {
            editorRef.current.setStyle(initialData.css)
          }

          if (initialData.components) {
            try {
              const components =
                typeof initialData.components === 'string' ? JSON.parse(initialData.components) : initialData.components

              editorRef.current.setComponents(components)
            } catch (e) {
              console.error('Failed to parse components:', e)
            }
          }
        }
      } catch (error) {
        console.error('Failed to load GrapesJS:', error)
      }
    }

    loadGrapesJS()

    return () => {
      if (editorRef.current) {
        editorRef.current.destroy()
      }
    }
  }, [initialData])

  return <div id='gjs' />
})

GrapeJSEditor.displayName = 'GrapeJSEditor'

export default GrapeJSEditor
