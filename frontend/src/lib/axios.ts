import axios from 'axios'
import { encodeBase64 } from 'src/utils'

const basic = encodeBase64(
  process.env.NEXT_PUBLIC_CLIENT_ID as string,
  process.env.NEXT_PUBLIC_CLIENT_SERCRET as string
)

const httpAuthClient = axios.create({
  baseURL: process.env.NEXT_PUBLIC_API_URL

  // baseURL: 'https://dory-thorough-commonly.ngrok-free.app/api'
  // baseURL: 'http://192.168.1.105:8089/api'
})

httpAuthClient.interceptors.request.use(
  config => {
    //Loic basic
    // const token =
    //   'SjNhTmNRR1d1TmVVdk11Y1Zldk5GdjVicFYzWW5DeTg4STM1V0hxNjoySGdaWmdhRFcxRXJwTjZlaGVXNjVubk1mbXJhUUN3SVNjODJ1QnJmdktMR2ltT1hJQXNUamk2Nk9EM0ZnUGlTSlNWaTVCSXFUOXo2SVptQnpIdGxNMG95eUtwOXNnR083Zzl2ckplMVhtYUN0RHpuUGQyN0gxZm95TnFBalNPNw=='

    if (basic) config.headers.Authorization = `Basic ${basic}`

    return config
  },
  error => {
    return Promise.reject(error)
  }
)

httpAuthClient.interceptors.response.use(
  response => response,
  error => {
    console.error('API Error:', error.response?.data || error.message)

    if (error.response?.status === 401) {
      console.log('Unauthorized! Redirecting...')
    }

    return Promise.reject(error)
  }
)

export { httpAuthClient }
