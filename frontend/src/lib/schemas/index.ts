import * as yup from 'yup'

export const documentTypeSchema = yup.object().shape({
  name: yup.string().required('Name is required'),
  is_chargeable: yup.boolean().required(),
  is_default: yup.boolean().required(),
  amount: yup.string().required()
})

export const routeSchema = yup.object().shape({
  departure_id: yup.string().required('Departure is required'),
  destination_id: yup.string().required('Destination is required'),
  delay: yup.number().required('Delay is required'),
  distance: yup.number().integer().max(2147483647).required('Distance is required'),
  petrol_volume: yup.number().integer().max(2147483647).required('Petrol volume is required'),
  fees: yup.string().required('Fees is required'),
  is_active: yup.boolean().optional(),
  is_city: yup.boolean().optional(),
  nbr_ligne: yup.number().integer().max(2147483647).required('Number of lines is required'),
  special_consumption: yup.boolean().optional(),
  special_value: yup.string().optional(),
  archive: yup.boolean().optional(),
  pricing_list: yup
    .array()
    .of(
      yup.object().shape({
        good_type_id: yup.string().required('Good type is required'),
        unite_price: yup.string().required('Unit price is required')
      })
    )
    .optional()
})

export const passwordSchema = yup.object().shape({
  new_password: yup.string().min(8, 'Password must be at least 8 characters').required('Password is required'),
  confirm_new_password: yup
    .string()
    .required('Confirm password is required')
    .oneOf([yup.ref('new_password'), ''], 'Passwords must match')
})

export const orderSchema = yup.object().shape({
  customer: yup.string().required('Client is required'),
  prestation: yup.string().required('Prestation is required'),
  is_city: yup.boolean().optional(),
  operations: yup
    .array()
    .of(
      yup.object().shape({
        tractor_id: yup.string().required('Tracteur is required'),
        trailer_id: yup.string().nullable().optional(),
        path_id: yup.string().required('Trajet is required'),
        direction: yup.string().required('Direction is required'),
        be_number: yup.string().optional(),
        bc_number: yup.string().optional(),
        product_id: yup.string().required('Product is required'),
        nature_product_id: yup.string().optional().nullable(),
        agence_id: yup.string().required('Agence du client is required'),
        qty: yup.number().min(0, 'Quantity must be positive'),

        // .max(1000, 'Quantity must be less than 1000')
        // .required('Quantity is required'),
        petrol_volume: yup.string().required('Fuel volume is required'),
        driver_id: yup.string().required('Driver is required'),
        price: yup
          .number()
          .min(0, 'Price must be positive')
          .max(1000000, 'Price must be less than 1000000000')
          .optional(),
        road_fees: yup.string().optional(),
        validation: yup.string().optional()
      })
    )
    .min(1, 'At least one operation is required'),
  rejection_reason: yup.string().when('validation_status', {
    is: 'REJETEE',
    then: schema => schema.required('Rejection reason is required when status is rejected'),
    otherwise: schema => schema.optional()
  }),
  validation_status: yup.string().oneOf(['EN ATTENTE', 'VALIDEE', 'REJETEE']).optional()
})

export const pricingBenneSchema = yup.object().shape({
  route: yup.string().required('Le trajet est requis'),
  product: yup.string().required('La nature du produit est requise'),
  customer: yup.string().required('Le client est requis'),
  unite_price: yup
    .number()
    .typeError('Le prix doit être un nombre')
    .required('Le prix unitaire est requis')
    .min(0, 'Le prix ne peut pas être négatif')
})

export const destinationSchema = yup.object().shape({
  name: yup.string().required('Le nom est requis'),
  country: yup.string(),
  lat: yup.string().required('La latitude est requise'),
  long: yup.string().required('La longitude est requise'),
  type: yup.string().oneOf(['NATIONAL', 'INTERNATIONAL']).required('Le type de destination est requis'),
  is_active: yup.boolean().default(true),
  is_archived: yup.boolean().default(false)
})

export const travelSchema = yup.object().shape({
  process: yup.string().required('Le numéro de commande est requis'),
  duration: yup.number().required('La durée est requise').min(0, 'La durée doit être positive'),
  blocking_duration: yup
    .number()
    .required("La durée d'immobilisation est requise")
    .min(0, 'La durée doit être positive'),
  started_date: yup.date().required('La date de début est requise'),
  ended_date: yup.date().required('La date de fin est requise'),
  charged_date: yup.date().required('La date de chargement est requise'),
  unloaded_date: yup.date().required('La date de déchargement est requise')
})

export const productSchema = yup.object().shape({
  name: yup.string().required('Product is required'),
  service_id: yup.string().required('product is required')
})

export const missionExpenseOthersSchema = yup.object().shape({
  priority: yup.string().required('La priorité est requise'),
  expenseLines: yup.array().of(
    yup
      .object()
      .shape({
        designation: yup.string().optional(),
        date: yup.date().nullable().optional(),
        duration: yup.string().nullable().optional(),
        price: yup.number().nullable().optional(),
        quantity: yup.number().nullable().optional(),
        operations: yup.array().of(yup.object()).nullable()
      })
      .optional()
  ),
  validation: yup.string().optional(),
  validationReason: yup.string().optional()
})

export const countrySchema = yup.object().shape({
  name: yup.string().required('Le nom est requis'),
  iso_2: yup.string().required('Le code est requis').max(2, 'Le code doit contenir 2 caractères maximum'),
  code: yup.string(),
  is_active: yup.boolean().default(true)
})

export const articleSchema = yup.object().shape({
  code: yup.string().required(),
  label: yup.string().required(),
  related_documents: yup.array().of(yup.string()).optional(),
  related_articles: yup.array().of(yup.string()).optional(),
  operation_type: yup.string().required(),
  measure_unit: yup.string().required(),
  group: yup.string().optional() // New optional field
})

export const consumptionSchema = yup.object().shape({
  operation: yup.string().required('Opération est requise'),
  type_conso: yup.string().required('Type de consommation est requis'),
  validation: yup.string().required('Validation est requise'),
  code: yup.string().optional(),
  distributions: yup.array().of(
    yup
      .object()
      .shape({
        station: yup.string().required('Station est requise'),
        volume: yup.number().required('Volume est requis'),
        cash_card: yup.string().required('Type de paiement est requis'),
        card_number: yup.string().when('cash_card', {
          is: 'Carte',
          then: schema => schema.required('Numéro de carte requis'),
          otherwise: schema => schema.notRequired()
        })
      })
      .optional()
  )
})

export const vehicleSchema = yup.object().shape({
  code: yup.string().required('Code is required'),
  number_plate: yup.string().required('Number plate is required'),
  consumption_card_number: yup.string().required('Consumption card number is required'),
  vehicle_type: yup
    .string()
    .oneOf(['TRACTEUR', 'REMORQUE', 'CAMION', 'PICKUP', 'HEAD_OFFICE'])
    .required('Vehicle type is required'),
  container: yup.string().required('Container is required'),
  petrol_consumption: yup.string().required('Petrol consumption is required'),
  long: yup.string().required('Longitude is required'),
  lat: yup.string().required('Latitude is required'),
  is_active: yup.boolean().required('Status is required')
})

export const missionExpenseSchema = yup.object().shape({
  client: yup.string().required('Client is required'),
  for_order: yup.string().required('Order is required'),
  mission_expense_lines: yup.array().of(
    yup.object().shape({
      operation: yup.string().required('Operation is required'),
      driver: yup.string().required('Driver is required'),
      departure: yup.string().required('Departure is required'),
      destination: yup.string().required('Destination is required'),
      vehicle: yup.string().required('Vehicle is required'),
      be_number: yup.string().required('BE number is required'),
      bc_number: yup.string().required('BC number is required'),
      fees: yup.string().required('Fees is required'),
      petrol_volume: yup.string().required('Petrol volume is required'),
      petrol_amount: yup.string().required('Petrol amount is required'),
      product_qty: yup.string().required('Product quantity is required'),
      product: yup.string().required('Product is required'),
      obs: yup.string().optional()
    })
  )
})

export const driverSchema = yup.object().shape({
  matricule: yup.string().required('Matricule is required'),
  fonction: yup
    .string()
    .oneOf(['CHAUFFEUR LIGNE', 'CHAUFFEUR VILLE', 'CHAUFFEUR', 'CHAUFFEUR BUREAU'])
    .required('Fonction is required'),
  dated_embauche_societe: yup.date().required("Date d'embauche is required"),
  sexe: yup.string().oneOf(['Masculin', 'Féminin']).required('Sexe is required'),
  date_de_naissance: yup.date().required('Date de naissance is required'),
  first_name: yup.string().required('First name is required'),
  last_name: yup.string().required('Last name is required'),
  address: yup.string().required('Address is required'),
  phone_number: yup.string().required('Phone number is required')
})

export const customerSchema = yup.object().shape({
  sap_uid: yup.string().required('SAP UID is required'),
  name: yup.string().required('Name is required'),
  citerne_price_diff: yup.string(),
  other_info: yup.string(),
  address: yup.string().required('Address is required'),
  phone1: yup.string().required('Phone 1 is required'),
  phone2: yup.string(),
  groupcode: yup.number().integer().max(2147483647),
  zipcode: yup.string(),
  mailaddres: yup.string().email('Invalid email'),
  cmpprivate: yup.string(),
  mailzipcod: yup.string(),
  addid: yup.string(),
  currency: yup.string(),
  cardtype: yup.string(),
  lictradnum: yup.string()
})

export const agencySchema = yup.object().shape({
  name: yup.string().required('Name is required'),
  address: yup.string().required('Address is required'),
  company: yup.string().required('Company is required'),
  is_active: yup.boolean().required('Status is required')
})

export const dataValidationSchema = yup.object().shape({
  level: yup.number().oneOf([0, 1, 2, 3, 4]).required('Level is required'),
  level_to_validate: yup.number().oneOf([0, 1, 2, 3, 4]).required('Level to validate is required'),
  data_type: yup.string().required('Data type is required')
})

export const usersSchema = yup.object().shape({
  username: yup.string().optional(),
  email: yup.string().email('Invalid email').optional(),
  first_name: yup.string().optional(),
  last_name: yup.string().optional(),
  password: yup.string().optional(),
  confirm_password: yup.string().when('password', {
    is: (val: string) => (val && val.length > 0 ? true : false),
    then: schema =>
      schema.required('Confirm password is required').oneOf([yup.ref('password')], 'Passwords must match'),
    otherwise: schema => schema.notRequired()
  }),
  is_active: yup.boolean().optional(),
  is_staff: yup.boolean().optional(),
  is_superuser: yup.boolean().optional(),
  groups: yup.array(),
  user_permissions: yup.array(),
  supervisor_id: yup.string().optional(),
  type_of_operation: yup.string().optional(),
  other_phone: yup.string().optional().nullable(),
  main_department_id: yup.string().optional(),
  annex_departments_ids: yup.array()
})
