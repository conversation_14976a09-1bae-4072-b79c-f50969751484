import axios from 'axios'
import { decrypt } from './secure-store'
import { LoginCredentials, LoginResponse, Tokens } from 'src/types/auth/auth'
import { httpAuthClient } from './axios'
import { User } from 'src/@core/types/auth'

export const api = axios.create({
  baseURL: process.env.NEXT_PUBLIC_API_URL,

  // baseURL: 'http://192.168.1.105:8089/api',

  // baseURL: 'https://dory-thorough-commonly.ngrok-free.app/api',

  // baseURL: 'http://192.168.1.105:8089/api/',
  headers: {
    'Content-Type': 'application/json'
  },

  withCredentials: true
})

export const setAuthHeader = async () => {
  const encryptedAccessToken = localStorage.getItem('access_token')
  if (encryptedAccessToken) {
    const accessToken = decrypt(encryptedAccessToken)
    api.defaults.headers.common['Authorization'] = `Bearer ${accessToken}`
  } else {
    delete api.defaults.headers.common['Authorization']
  }
}

export const login = async (credentials: LoginCredentials) => {
  const response = await httpAuthClient.post('/auth/login/', credentials)

  return response.data
}

export const logout = async () => {
  // await api.post('/auth/logout/')
  console.log('logout')
}

export const refreshToken = async (): Promise<any> => {
  const encryptedRefreshToken = localStorage.getItem('refresh_token')
  if (!encryptedRefreshToken) {
    throw new Error('No refresh token available')
  }
  const refreshToken = await decrypt(encryptedRefreshToken)
  const response = await httpAuthClient.post('/auth/refresh/', { refresh_token: refreshToken })

  return response.data
}

export const fetchUser = async (): Promise<User> => {
  const response = await api.get('/user/me')

  return response.data
}
