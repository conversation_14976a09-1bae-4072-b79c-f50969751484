// // ** React Imports
// import { SyntheticEvent } from 'react'

// // ** MUI Imports
// import Tab from '@mui/material/Tab'
// import TabPanel from '@mui/lab/TabPanel'
// import TabContext from '@mui/lab/TabContext'
// import { styled } from '@mui/material/styles'
// import Accordion from '@mui/material/Accordion'
// import Typography from '@mui/material/Typography'
// import Box, { BoxProps } from '@mui/material/Box'
// import MuiTabList, { TabListProps } from '@mui/lab/TabList'
// import AccordionSummary from '@mui/material/AccordionSummary'
// import AccordionDetails from '@mui/material/AccordionDetails'

// // ** Icon Imports
// import Icon from 'src/@core/components/icon'

// // ** Custom Components Imports
// import CustomAvatar from 'src/@core/components/mui/avatar'

// // ** Types
// import { FaqType } from 'src/@fake-db/types'

// interface Props {
//   activeTab: string
//   data: { faqData: FaqType }
//   handleChange: (event: SyntheticEvent, newValue: string) => void
// }

// // Styled TabList component
// const MuiBox = styled(Box)<BoxProps>(({ theme }) => ({
//   display: 'flex',
//   marginTop: theme.spacing(6),
//   [theme.breakpoints.down('md')]: {
//     flexDirection: 'column'
//   }
// }))

// const TabList = styled(MuiTabList)<TabListProps>(({ theme }) => ({
//   borderRight: 0,
//   '&, & .MuiTabs-scroller': {
//     boxSizing: 'content-box',
//     padding: theme.spacing(1.25, 1.25, 2),
//     margin: `${theme.spacing(-1.25, -1.25, -2)} !important`
//   },
//   '& .MuiTabs-indicator': {
//     display: 'none'
//   },
//   '& .Mui-selected': {
//     boxShadow: theme.shadows[2],
//     backgroundColor: theme.palette.primary.main,
//     color: `${theme.palette.common.white} !important`
//   },
//   '& .MuiTab-root': {
//     minWidth: 280,
//     lineHeight: 1,
//     textAlign: 'center',
//     flexDirection: 'row',
//     justifyContent: 'flex-start',
//     color: theme.palette.text.primary,
//     borderRadius: theme.shape.borderRadius,
//     '&:hover': {
//       color: theme.palette.primary.main
//     },
//     '& svg': {
//       marginBottom: 0,
//       marginRight: theme.spacing(2)
//     },
//     [theme.breakpoints.down('md')]: {
//       maxWidth: '100%'
//     }
//   }
// }))

// const Faqs = ({ data, activeTab, handleChange }: Props) => {
//   const renderTabContent = () => {
//     return Object.values(data.faqData).map(tab => {
//       return (
//         <TabPanel key={tab.id} value={tab.id} sx={{ p: 6.5, pt: 0, width: '100%' }}>
//           <Box key={tab.id}>
//             <Box sx={{ display: 'flex', alignItems: 'center' }}>
//               <CustomAvatar skin='light' variant='rounded' sx={{ height: 48, width: 48 }}>
//                 <Icon icon={tab.icon} fontSize='2.25rem' />
//               </CustomAvatar>
//               <Box sx={{ ml: 4 }}>
//                 <Typography variant='h4'>{tab.title}</Typography>
//                 <Typography sx={{ color: 'text.secondary' }}>{tab.subtitle}</Typography>
//               </Box>
//             </Box>
//             <Box sx={{ mt: 6 }}>
//               {tab.qandA.map(item => {
//                 return (
//                   <Accordion key={item.id}>
//                     <AccordionSummary expandIcon={<Icon fontSize='1.25rem' icon='tabler:chevron-down' />}>
//                       <Typography sx={{ fontWeight: '500' }}>{item.question}</Typography>
//                     </AccordionSummary>
//                     <AccordionDetails>
//                       <Typography sx={{ color: 'text.secondary' }}>{item.answer}</Typography>
//                     </AccordionDetails>
//                   </Accordion>
//                 )
//               })}
//             </Box>
//           </Box>
//         </TabPanel>
//       )
//     })
//   }

//   const renderTabs = () => {
//     if (data !== null) {
//       return Object?.values(data?.faqData).map(tab => {
//         if (tab.qandA.length) {
//           return <Tab key={tab.id} value={tab.id} label={tab.title} icon={<Icon icon={tab.icon} />} />
//         } else {
//           return null
//         }
//       })
//     } else {
//       return null
//     }
//   }

//   return (
//     <MuiBox>
//       <TabContext value={activeTab}>
//         <Box sx={{ display: 'flex', flexDirection: 'column' }}>
//           <TabList orientation='vertical' onChange={handleChange}>
//             {renderTabs()}
//           </TabList>
//           <Box
//             sx={{
//               mt: 5.5,
//               display: 'flex',
//               justifyContent: 'center',
//               '& img': { maxWidth: '100%', display: { xs: 'none', md: 'block' } }
//             }}
//           >
//             <img src='/images/pages/faq-illustration.png' alt='illustration' width='230' />
//           </Box>
//         </Box>
//         {renderTabContent()}
//       </TabContext>
//     </MuiBox>
//   )
// }

// export default Faqs

import React from 'react'

export default function Faqs() {
  return <div></div>
}
