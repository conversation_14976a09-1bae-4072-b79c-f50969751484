import { useEffect, useState } from 'react'
import {
  Box,
  Grid,
  Card,
  Table,
  Divider,
  TableRow,
  TableHead,
  TableBody,
  TableCell,
  Typography,
  TableContainer,
  Alert,
  styled,
  useTheme
} from '@mui/material'
import { useInvoiceById } from 'src/hooks/useInvoice'
import { formatDate } from 'src/@core/utils/format'
import themeConfig from 'src/configs/themeConfig'
import { Invoice } from 'src/types/models/invoice'

interface Props {
  id: string
}

const CalcWrapper = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'space-between',
  '&:not(:last-of-type)': {
    marginBottom: theme.spacing(2)
  }
}))

const MUITableCell = styled(TableCell)(({ theme }) => ({
  borderBottom: 0,
  padding: `${theme.spacing(1)} !important`
}))

const PrintPage = ({ id }: Props) => {
  const [error, setError] = useState<boolean>(false)
  const theme = useTheme()
  const { data: invoice, isError } = useInvoiceById(id)

  useEffect(() => {
    if (isError) {
      setError(true)
    }
  }, [isError])

  useEffect(() => {
    setTimeout(() => {
      window.print()
    }, 100)
  }, [])

  if (error) {
    return (
      <Box sx={{ p: 5 }}>
        <Grid container spacing={6}>
          <Grid item xs={12}>
            <Alert severity='error'>Invoice with the id: {id} does not exist. Please check the list of invoices.</Alert>
          </Grid>
        </Grid>
      </Box>
    )
  }

  if (!invoice) {
    return null
  }

  return (
    <Box sx={{ p: 5 }}>
      <Grid container spacing={6}>
        <Grid item xs={12}>
          <Card>
            <Box sx={{ p: 6 }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 6 }}>
                <Box sx={{ display: 'flex', flexDirection: 'column' }}>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <Typography variant='h4' sx={{ mr: 2 }}>
                      {themeConfig.templateName}
                    </Typography>
                  </Box>
                  <Typography sx={{ mb: 1, color: 'text.secondary' }}>Invoice #{invoice.code}</Typography>
                </Box>
                <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'flex-end' }}>
                  <Typography sx={{ mb: 1, color: 'text.secondary' }}>Status: {invoice.status}</Typography>
                  <Typography sx={{ color: 'text.secondary' }}>Due Date: {formatDate(invoice.echeance)}</Typography>
                </Box>
              </Box>

              <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 6 }}>
                <Box>
                  <Typography variant='body2' sx={{ mb: 1, fontWeight: 600 }}>
                    Bill To:
                  </Typography>
                  <Typography variant='body2' sx={{ mb: 1 }}>
                    {invoice.customer.name}
                  </Typography>
                  <Typography variant='body2'>{invoice.agence}</Typography>
                </Box>
                <Box>
                  <Typography variant='body2' sx={{ mb: 1, fontWeight: 600 }}>
                    Order Info:
                  </Typography>
                  <Typography variant='body2' sx={{ mb: 1 }}>
                    Order Number: {invoice.order.code}
                  </Typography>
                  <Typography variant='body2'>Status: {invoice.order.validate ? 'Validated' : 'Pending'}</Typography>
                </Box>
              </Box>

              <Divider sx={{ my: theme => `${theme.spacing(6)} !important` }} />

              <TableContainer>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Truck</TableCell>
                      <TableCell>BE Number</TableCell>
                      <TableCell>BC Number</TableCell>
                      <TableCell>Destination</TableCell>
                      <TableCell>Product</TableCell>
                      <TableCell>Price</TableCell>
                      <TableCell>Quantity</TableCell>
                      <TableCell>Amount HT</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {invoice.invoice_lines.map((item, index) => (
                      <TableRow key={index}>
                        <MUITableCell>{item.truck}</MUITableCell>
                        <MUITableCell>{item.be_number}</MUITableCell>
                        <MUITableCell>{item.bc_number}</MUITableCell>
                        <MUITableCell>{item.destination}</MUITableCell>
                        <MUITableCell>{item.product}</MUITableCell>
                        <MUITableCell>{item.price}</MUITableCell>
                        <MUITableCell>{item.qty}</MUITableCell>
                        <MUITableCell>{item.amount_ht}</MUITableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>

              <Box sx={{ mt: 6 }}>
                <Grid container>
                  <Grid item xs={12} sm={7} lg={9}>
                    <Box sx={{ mb: 2, display: 'flex', alignItems: 'center' }}>
                      <Typography variant='body2' sx={{ mr: 2, fontWeight: 600 }}>
                        Bank Details:
                      </Typography>
                    </Box>
                    <Typography variant='body2' sx={{ mb: 1 }}>
                      Bank Reference: {invoice.bank_ref || 'N/A'}
                    </Typography>
                    <Typography variant='body2'>Account Number: {invoice.account_number || 'N/A'}</Typography>
                  </Grid>
                  <Grid item xs={12} sm={5} lg={3}>
                    <CalcWrapper>
                      <Typography variant='body2'>Amount:</Typography>
                      <Typography variant='body2' sx={{ fontWeight: 600 }}>
                        {invoice.amount} XAF
                      </Typography>
                    </CalcWrapper>
                    <CalcWrapper>
                      <Typography variant='body2'>Total Amount:</Typography>
                      <Typography variant='body2' sx={{ fontWeight: 600 }}>
                        {invoice.total_amount} XAF
                      </Typography>
                    </CalcWrapper>
                  </Grid>
                </Grid>
              </Box>

              {/* {invoice.conditions && (
                <>
                  <Divider sx={{ my: theme => `${theme.spacing(6)} !important` }} />
                  <Typography variant='body2'>
                    <strong>Conditions:</strong> {invoice.conditions}
                  </Typography>
                </>
              )} */}
            </Box>
          </Card>
        </Grid>
      </Grid>
    </Box>
  )
}

export default PrintPage
