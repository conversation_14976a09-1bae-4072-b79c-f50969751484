// ** MUI Imports
import Avatar from '@mui/material/Avatar'
import Tooltip from '@mui/material/Tooltip'
import AvatarGroup from '@mui/material/AvatarGroup'

const AvatarsGroupedPullUpWithTooltip = () => {
  return (
    <AvatarGroup className='pull-up' max={4}>
      <Tooltip title='<PERSON> Sparks'>
        <Avatar src='/images/avatars/4.png' alt='Olivia Sparks' />
      </Tooltip>
      <Tooltip title='<PERSON> Lloyd'>
        <Avatar src='/images/avatars/5.png' alt='<PERSON>' />
      </Tooltip>
      <Tooltip title='<PERSON><PERSON>'>
        <Avatar src='/images/avatars/6.png' alt='<PERSON><PERSON>' />
      </Tooltip>
      <Tooltip title='<PERSON>'>
        <Avatar src='/images/avatars/8.png' alt='<PERSON>' />
      </Tooltip>
      <Tooltip title='<PERSON><PERSON> Warner'>
        <Avatar src='/images/avatars/7.png' alt='<PERSON><PERSON> Warner' />
      </Tooltip>
    </AvatarGroup>
  )
}

export default AvatarsGroupedPullUpWithTooltip
