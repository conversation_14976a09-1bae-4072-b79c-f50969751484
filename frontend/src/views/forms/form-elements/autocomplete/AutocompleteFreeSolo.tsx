// // ** Custom Component Imports
// import CustomTextField from 'src/@core/components/mui/text-field'
// import CustomAutocomplete from 'src/@core/components/mui/autocomplete'

// // ** Data
// import { top100Films } from 'src/@fake-db/autocomplete'

// const AutocompleteFreeSolo = () => {
//   return (
//     <CustomAutocomplete
//       freeSolo
//       sx={{ width: 250 }}
//       id='autocomplete-free-solo'
//       options={top100Films.map(option => option.title)}
//       renderInput={params => <CustomTextField {...params} label='Free Solo' />}
//     />
//   )
// }

// export default AutocompleteFreeSolo

import React from 'react'

export default function AutocompleteFreeSolo() {
  return <div></div>
}
