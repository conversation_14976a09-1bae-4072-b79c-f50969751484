import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Grid,
  CircularProgress,
  Box,
  Typography
} from '@mui/material'
import { useTranslation } from 'react-i18next'
import { useRef, useImperativeHandle, useState, use } from 'react'
import DatePicker from 'react-datepicker'
import { DateType } from 'src/types/forms/reactDatepickerTypes'
import { useForm, Controller } from 'react-hook-form'
import { yupResolver } from '@hookform/resolvers/yup'
import * as yup from 'yup'

// ** Custom Components Imports
import CustomTextField from 'src/@core/components/mui/text-field'
import CustomInput from 'src/views/forms/form-elements/pickers/PickersCustomInput'
import DatePickerWrapper from 'src/@core/styles/libs/react-datepicker'
import { useLoadTravel, useTravelById } from 'src/hooks/_services/useTravelmanagement'
import LoadingComponent from 'src/@core/shared/components/loading-component'
import toast from 'react-hot-toast'

interface LoadTravelFormProps {
  travelId: string
  onSubmit: (data: any) => void
  formRef: React.RefObject<{ submitForm: () => void }>
}

const loadTravelSchema = yup.object().shape({
  date: yup.date().required('Date is required'),
  time: yup.date().required('Time is required'),
  comment: yup.string().required('Comment is required')
})

interface FormData {
  date: DateType
  time: DateType
  comment: string
}

const LoadTravelForm = ({ travelId, onSubmit, formRef }: LoadTravelFormProps) => {
  const { t } = useTranslation()
  const { data, isPending, isLoading } = useTravelById(travelId)
  const {
    control,
    handleSubmit,
    formState: { errors }
  } = useForm<FormData>({
    resolver: yupResolver(loadTravelSchema),
    defaultValues: {
      date: new Date(),
      time: new Date(),
      comment: ''
    }
  })

  useImperativeHandle(formRef, () => ({
    submitForm: handleSubmit(onSubmit)
  }))

  if (isLoading || isPending) return <LoadingComponent message='Chargement...' />

  return (
    <Box sx={{ mt: 1 }}>
      <Box>
        <Typography>
          Mise à jour de vayage: Chargement de la {''}
          <Typography
            variant='overline'
            fontSize={14}
            style={{ fontWeight: 'bold', textTransform: 'uppercase' }}
            color={'primary'}
          >
            {data.camion.split('|')[0]}{' '}
          </Typography>{' '}
          Chauffeur
          <Typography
            variant='overline'
            fontSize={14}
            style={{ fontWeight: 'bold', textTransform: 'uppercase' }}
            color={'primary'}
          >
            {data.camion.split('|')[1]}
          </Typography>
        </Typography>
      </Box>
      <form onSubmit={handleSubmit(onSubmit)}>
        <Grid container spacing={4}>
          {/* Date and Time Row */}
          <Grid item xs={12}>
            <Grid container spacing={4}>
              <Grid item xs={6}>
                <DatePickerWrapper>
                  <Controller
                    name='date'
                    control={control}
                    render={({ field }) => (
                      <DatePicker
                        selected={field.value}
                        onChange={field.onChange}
                        dateFormat='dd/MM/yyyy'
                        id='load-date-picker'
                        customInput={
                          <CustomInput
                            fullWidth
                            label={t('travels.dialogs.load.date', 'Date de chargement') as string}
                          />
                        }
                      />
                    )}
                  />
                </DatePickerWrapper>
              </Grid>
              <Grid item xs={6}>
                <DatePickerWrapper>
                  <Controller
                    name='time'
                    control={control}
                    render={({ field }) => (
                      <DatePicker
                        selected={field.value}
                        onChange={field.onChange}
                        showTimeSelect
                        showTimeSelectOnly
                        timeIntervals={15}
                        timeFormat='HH:mm'
                        dateFormat='HH:mm'
                        id='load-time-picker'
                        customInput={
                          <CustomInput
                            fullWidth
                            label={t('travels.dialogs.load.time', 'Heure de chargement') as string}
                          />
                        }
                      />
                    )}
                  />
                </DatePickerWrapper>
              </Grid>
            </Grid>
          </Grid>

          {/* Comment Row */}
          <Grid item xs={12}>
            <Controller
              name='comment'
              control={control}
              render={({ field }) => (
                <CustomTextField
                  {...field}
                  fullWidth
                  multiline
                  rows={4}
                  error={Boolean(errors.comment)}
                  helperText={errors.comment?.message}
                  label={t('travels.dialogs.load.comment', 'Commentaire')}
                />
              )}
            />
          </Grid>
        </Grid>
      </form>
    </Box>
  )
}

interface LoadTravelDialogProps {
  open: boolean
  onClose: () => void
  travelId: string
}

const LoadTravelDialog = ({ open, onClose, travelId }: LoadTravelDialogProps) => {
  const { t } = useTranslation()

  const formRef = useRef<{ submitForm: () => void }>(null)
  const { mutateAsync, isPending } = useLoadTravel(travelId, {
    onSuccess: () => {
      toast.success(t('travels.dialogs.load.success', 'Voyage chargé avec succès'))
      onClose()
    },
    onError: (error: any) => {
      toast.error(error.response.data.message)
    }
  })

  const handleSubmit = async (data: FormData) => {
    try {
      await mutateAsync({
        date: data.date,
        time: data.time,
        comment: data.comment
      })
      onClose()
    } catch (err) {}
  }

  const handleConfirm = () => {
    formRef.current?.submitForm()
  }

  return (
    <Dialog open={open} onClose={onClose} maxWidth='sm' fullWidth PaperProps={{ sx: { overflow: 'visible' } }}>
      <DialogTitle>{t('travels.dialogs.load.title')}</DialogTitle>
      <DialogContent>
        <DatePickerWrapper>
          <LoadTravelForm travelId={travelId} onSubmit={handleSubmit} formRef={formRef} />
        </DatePickerWrapper>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose} disabled={isPending}>
          {t('common.cancel', 'Annuler')}
        </Button>
        <Button variant='contained' onClick={handleConfirm} disabled={isPending}>
          {isPending ? <CircularProgress size={24} color='inherit' /> : t('common.confirm', 'Confirmer')}
        </Button>
      </DialogActions>
    </Dialog>
  )
}

export default LoadTravelDialog
