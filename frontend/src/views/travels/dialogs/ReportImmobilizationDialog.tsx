import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  <PERSON>alogActions,
  Button,
  Grid,
  CircularProgress,
  Box,
  Typography,
  MenuItem
} from '@mui/material'
import { useTranslation } from 'react-i18next'
import { useImperativeHandle, useRef, useState } from 'react'
import DatePicker from 'react-datepicker'
import { DateType } from 'src/types/forms/reactDatepickerTypes'

// ** Custom Components Imports
import CustomTextField from 'src/@core/components/mui/text-field'
import CustomInput from 'src/views/forms/form-elements/pickers/PickersCustomInput'
import DatePickerWrapper from 'src/@core/styles/libs/react-datepicker'
import { useReportBlocking, useTravelById } from 'src/hooks/_services/useTravelmanagement'
import { Controller, useForm } from 'react-hook-form'
import { yupResolver } from '@hookform/resolvers/yup'
import * as yup from 'yup'
import LoadingComponent from 'src/@core/shared/components/loading-component'
import toast from 'react-hot-toast'

interface ReportImmobilizationFormProps {
  travelId: string
  onSubmit: (data: any) => void
  formRef: React.RefObject<{ submitForm: () => void }>
}

interface formData {
  date: DateType
  time: DateType
  comment: string
  type: string
}
const addReturnLoadSchema = yup.object().shape({
  date: yup.date().required('La date est requise'),
  type: yup.string().required(''),
  time: yup.date().required("L'heure est requise"),
  comment: yup.string()
})

const ReportImmobilizationForm = ({ travelId, onSubmit, formRef }: ReportImmobilizationFormProps) => {
  const { t } = useTranslation()
  const { data, isLoading, isPending } = useTravelById(travelId)
  useImperativeHandle(formRef, () => ({
    submitForm: handleSubmit(onSubmit)
  }))

  const {
    handleSubmit,
    control,
    formState: { errors }
  } = useForm<formData>({
    resolver: yupResolver(addReturnLoadSchema)
  })

  if (isLoading || isPending) return <LoadingComponent message='Chargement...' />

  return (
    <>
      <Box sx={{ mt: 1 }}>
        <Box>
          <Typography>
            Mise à jour de vayage: Immobilisation de la {''}
            <Typography
              variant='overline'
              fontSize={14}
              style={{ fontWeight: 'bold', textTransform: 'uppercase' }}
              color={'primary'}
            >
              {data.camion.split('|')[0]}{' '}
            </Typography>{' '}
            Chauffeur
            <Typography
              variant='overline'
              fontSize={14}
              style={{ fontWeight: 'bold', textTransform: 'uppercase' }}
              color={'primary'}
            >
              {data.camion.split('|')[1]}
            </Typography>
          </Typography>
        </Box>
        <form onSubmit={handleSubmit(onSubmit)}>
          <Grid container spacing={4}>
            {/* Date and Time Row */}
            <Grid item xs={12}>
              <Grid container spacing={4}>
                <Grid item xs={6}>
                  <DatePickerWrapper>
                    <Controller
                      name='date'
                      control={control}
                      render={({ field }) => (
                        <DatePicker
                          selected={field.value}
                          onChange={field.onChange}
                          dateFormat='dd/MM/yyyy'
                          id='start-date-picker'
                          customInput={
                            <CustomInput
                              fullWidth
                              label={t('travels.dialogs.start.date', 'Date de départ') as string}
                            />
                          }
                        />
                      )}
                    />
                  </DatePickerWrapper>
                </Grid>
                <Grid item xs={6}>
                  <DatePickerWrapper>
                    <Controller
                      name='time'
                      control={control}
                      render={({ field }) => (
                        <DatePicker
                          selected={field.value}
                          onChange={field.onChange}
                          showTimeSelect
                          showTimeSelectOnly
                          timeIntervals={15}
                          timeFormat='HH:mm'
                          dateFormat='HH:mm'
                          id='start-time-picker'
                          customInput={
                            <CustomInput
                              fullWidth
                              label={t('travels.dialogs.start.time', 'Heure de départ') as string}
                            />
                          }
                        />
                      )}
                    />
                  </DatePickerWrapper>
                </Grid>
              </Grid>
            </Grid>
            <Grid item xs={12}>
              <Controller
                name='type'
                control={control}
                render={({ field }) => (
                  <CustomTextField select fullWidth label={t('travels.dialogs.start.type', 'Type')} {...field}>
                    <MenuItem value='Logistique'>Logistique</MenuItem>
                    <MenuItem value='Maintenance'>Maintenance</MenuItem>
                    <MenuItem value='Client'>Client</MenuItem>
                  </CustomTextField>
                )}
              />
            </Grid>

            {/* Comment Row */}
            <Grid item xs={12}>
              <Controller
                name='comment'
                control={control}
                render={({ field }) => (
                  <CustomTextField
                    {...field}
                    fullWidth
                    multiline
                    rows={4}
                    error={Boolean(errors.comment)}
                    helperText={errors.comment?.message}
                    label={t('travels.dialogs.start.comment', 'Commentaire')}
                  />
                )}
              />
            </Grid>
          </Grid>
        </form>
      </Box>
    </>
  )
}

interface ReportImmobilizationDialogProps {
  open: boolean
  onClose: () => void
  travelId: string
}

const ReportImmobilizationDialog = ({ open, onClose, travelId }: ReportImmobilizationDialogProps) => {
  const { t } = useTranslation()
  const formRef = useRef<{ submitForm: () => void }>(null)
  const { isPending, mutateAsync } = useReportBlocking(travelId, {
    onSuccess: () => {
      toast.success(t('travels.dialogs.reportImmobilization.success', 'Immobilisation signalée avec succès'))
      onClose()
    },
    onError: (error: any) => {
      toast.error(error.response.data.message)
    }
  })

  const handleSubmit = async (data: formData) => {
    try {
      await mutateAsync({
        date: data.date,
        time: data.time,
        comment: data.comment,
        immobilization_type: data.type
      })
      onClose()
    } catch (error) {}
  }

  const handleConfirm = () => {
    formRef.current?.submitForm()
  }

  return (
    <Dialog open={open} onClose={onClose} maxWidth='sm' fullWidth PaperProps={{ sx: { overflow: 'visible' } }}>
      <DialogContent>
        <ReportImmobilizationForm travelId={travelId} onSubmit={handleSubmit} formRef={formRef} />
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose}>{t('common.actions.cancel')}</Button>
        <Button variant='contained' onClick={handleConfirm}>
          {isPending ? <CircularProgress size={24} color='inherit' /> : t('common.confirm', 'Confirmer')}
        </Button>
      </DialogActions>
    </Dialog>
  )
}

export default ReportImmobilizationDialog
