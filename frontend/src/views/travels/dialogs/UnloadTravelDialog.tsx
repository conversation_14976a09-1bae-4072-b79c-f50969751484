import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  <PERSON>alog<PERSON><PERSON>,
  Button,
  Grid,
  CircularProgress,
  Box,
  Typography
} from '@mui/material'
import { useTranslation } from 'react-i18next'
import { useRef, useImperativeHandle } from 'react'
import DatePicker from 'react-datepicker'
import { DateType } from 'src/types/forms/reactDatepickerTypes'

// ** Custom Components Imports
import CustomTextField from 'src/@core/components/mui/text-field'
import CustomInput from 'src/views/forms/form-elements/pickers/PickersCustomInput'
import DatePickerWrapper from 'src/@core/styles/libs/react-datepicker'
import { useTravelById, useUnloadTravel } from 'src/hooks/_services/useTravelmanagement'
import { Controller, useForm } from 'react-hook-form'
import { yupResolver } from '@hookform/resolvers/yup'
import * as yup from 'yup'
import toast from 'react-hot-toast'
import LoadingComponent from 'src/@core/shared/components/loading-component'

interface FormData {
  date: DateType
  time: DateType
  comment: string
}

interface UnloadTravelFormProps {
  travelId: string
  onSubmit: (data: any) => void
  formRef: React.RefObject<{ submitForm: () => void }>
}

const addReturnLoadSchema = yup.object().shape({
  date: yup.date().required('La date est requise'),
  time: yup.date().required("L'heure est requise"),
  comment: yup.string()
})

const UnloadTravelForm = ({ travelId, onSubmit, formRef }: UnloadTravelFormProps) => {
  const { t } = useTranslation()
  const { data, isLoading, isPending } = useTravelById(travelId)

  const {
    control,
    handleSubmit,
    formState: { errors }
  } = useForm<FormData>({
    resolver: yupResolver(addReturnLoadSchema),
    defaultValues: {
      date: new Date(),
      time: new Date(),
      comment: ''
    }
  })

  useImperativeHandle(formRef, () => ({
    submitForm: handleSubmit(onSubmit)
  }))

  if (isLoading || isPending) return <LoadingComponent message='Chargement...' />

  return (
    <Box sx={{ mt: 1 }}>
      <Box>
        <Typography>
          Mise à jour de vayage: Démmarer le voyage pour {''}
          <Typography
            variant='overline'
            fontSize={14}
            style={{ fontWeight: 'bold', textTransform: 'uppercase' }}
            color={'primary'}
          >
            {data.camion.split('|')[0]}{' '}
          </Typography>{' '}
          Chauffeur
          <Typography
            variant='overline'
            fontSize={14}
            style={{ fontWeight: 'bold', textTransform: 'uppercase' }}
            color={'primary'}
          >
            {data.camion.split('|')[1]}
          </Typography>
        </Typography>
      </Box>
      <form onSubmit={handleSubmit(onSubmit)}>
        <Grid container spacing={4}>
          {/* Date and Time Row */}
          <Grid item xs={12}>
            <Grid container spacing={4}>
              <Grid item xs={6}>
                <DatePickerWrapper>
                  <Controller
                    name='date'
                    control={control}
                    render={({ field }) => (
                      <DatePicker
                        selected={field.value}
                        onChange={field.onChange}
                        dateFormat='dd/MM/yyyy'
                        id='start-date-picker'
                        customInput={
                          <CustomInput fullWidth label={t('travels.dialogs.start.date', 'Date de départ') as string} />
                        }
                      />
                    )}
                  />
                </DatePickerWrapper>
              </Grid>
              <Grid item xs={6}>
                <DatePickerWrapper>
                  <Controller
                    name='time'
                    control={control}
                    render={({ field }) => (
                      <DatePicker
                        selected={field.value}
                        onChange={field.onChange}
                        showTimeSelect
                        showTimeSelectOnly
                        timeIntervals={15}
                        timeFormat='HH:mm'
                        dateFormat='HH:mm'
                        id='start-time-picker'
                        customInput={
                          <CustomInput fullWidth label={t('travels.dialogs.start.time', 'Heure de départ') as string} />
                        }
                      />
                    )}
                  />
                </DatePickerWrapper>
              </Grid>
            </Grid>
          </Grid>

          {/* Comment Row */}
          <Grid item xs={12}>
            <Controller
              name='comment'
              control={control}
              render={({ field }) => (
                <CustomTextField
                  {...field}
                  fullWidth
                  multiline
                  rows={4}
                  error={Boolean(errors.comment)}
                  helperText={errors.comment?.message}
                  label={t('travels.dialogs.start.comment', 'Commentaire')}
                />
              )}
            />
          </Grid>
        </Grid>
      </form>
    </Box>
  )
}

interface UnloadTravelDialogProps {
  open: boolean
  onClose: () => void
  travelId: string
}

const UnloadTravelDialog = ({ open, onClose, travelId }: UnloadTravelDialogProps) => {
  const { t } = useTranslation()
  const formRef = useRef<{ submitForm: () => void }>(null)
  const { mutateAsync, isPending } = useUnloadTravel(travelId, {
    onSuccess: () => {
      toast.success(t('travels.dialogs.unload.success', 'Voyage déchargé avec succès'))
      onClose()
    },
    onError: (error: any) => {
      toast.error(error.response.data.message)
    }
  })
  const handleSubmit = async (data: FormData) => {
    try {
      await mutateAsync({
        date: data.date,
        time: data.time,
        comment: data.comment
      })
    } catch (error) {}
  }

  const handleConfirmClick = () => {
    formRef.current?.submitForm()
  }

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth='sm'
      fullWidth

      // PaperProps={{
      //   sx: {
      //     overflow: 'visible'
      //   }
      // }}
    >
      <DialogTitle>{t('travels.dialogs.unload.title')}</DialogTitle>
      <DialogContent>
        <UnloadTravelForm travelId={travelId} onSubmit={handleSubmit} formRef={formRef} />
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose} disabled={isPending}>
          {t('common.cancel', 'Annuler')}
        </Button>
        <Button variant='contained' onClick={handleConfirmClick} disabled={isPending}>
          {isPending ? <CircularProgress size={24} color='inherit' /> : t('common.confirm', 'Confirmer')}
        </Button>
      </DialogActions>
    </Dialog>
  )
}

export default UnloadTravelDialog
