import { useTranslation } from 'react-i18next'
import { useRef, useImperativeHandle, ReactNode } from 'react'
import DatePicker from 'react-datepicker'
import { DateType } from 'src/types/forms/reactDatepickerTypes'
import { Dialog, DialogContent, DialogActions, Button, Box, Grid, Typography, CircularProgress } from '@mui/material'
import CustomTextField from 'src/@core/components/mui/text-field'
import CustomInput from 'src/views/forms/form-elements/pickers/PickersCustomInput'
import DatePickerWrapper from 'src/@core/styles/libs/react-datepicker'
import { Controller, useForm } from 'react-hook-form'
import { yupResolver } from '@hookform/resolvers/yup'
import * as yup from 'yup'
import { useStartTravel, useTravelById } from 'src/hooks/_services/useTravelmanagement'
import LoadingComponent from 'src/@core/shared/components/loading-component'
import ErrorComponent from 'src/@core/shared/components/error-component'
import toast from 'react-hot-toast'

interface FormData {
  date: DateType
  time: DateType
  comment: string
}

const startTravelSchema = yup.object().shape({
  date: yup.date().required('La date est requise'),
  time: yup.date().required("L'heure est requise"),
  comment: yup.string()
})

interface StartTravelFormProps {
  travelId: string
  onSubmit: (data: FormData) => void
  formRef: React.RefObject<{ submitForm: () => void }>
}

const StartTravelForm = ({ travelId, onSubmit, formRef }: StartTravelFormProps) => {
  const { t } = useTranslation()
  const { data, isLoading, isPending, isError } = useTravelById(travelId)

  const {
    control,
    handleSubmit,
    formState: { errors }
  } = useForm<FormData>({
    resolver: yupResolver(startTravelSchema),
    defaultValues: {
      date: new Date(),
      time: new Date(),
      comment: ''
    }
  })

  useImperativeHandle(formRef, () => ({
    submitForm: handleSubmit(onSubmit)
  }))

  if (isLoading || isPending) return <LoadingComponent message='Chargement...' />
  if (isError) return <ErrorComponent message='Erreur lors du chargement' />

  return (
    <Box sx={{ mt: 1 }}>
      <Box>
        <Typography>
          Mise à jour de voyage: Démarrer le voyage pour {''}
          <Typography
            variant='overline'
            fontSize={14}
            style={{ fontWeight: 'bold', textTransform: 'uppercase' }}
            color={'primary'}
          >
            {data.camion.split('|,:')[0]}{' '}
          </Typography>{' '}
          Chauffeur
          <Typography
            variant='overline'
            fontSize={14}
            style={{ fontWeight: 'bold', textTransform: 'uppercase' }}
            color={'primary'}
          >
            {data.camion.split('|,:')[1]}
          </Typography>
        </Typography>
      </Box>
      <form>
        <Grid container spacing={4}>
          <Grid item xs={12}>
            <Grid container spacing={4}>
              <Grid item xs={6}>
                <DatePickerWrapper>
                  <Controller
                    name='date'
                    control={control}
                    render={({ field }) => (
                      <DatePicker
                        selected={field.value}
                        onChange={field.onChange}
                        dateFormat='dd/MM/yyyy'
                        id='start-date-picker'
                        customInput={
                          <CustomInput fullWidth label={t('travels.dialogs.start.date', 'Date de départ') as string} />
                        }
                      />
                    )}
                  />
                </DatePickerWrapper>
              </Grid>
              <Grid item xs={6}>
                <DatePickerWrapper>
                  <Controller
                    name='time'
                    control={control}
                    render={({ field }) => (
                      <DatePicker
                        selected={field.value}
                        onChange={field.onChange}
                        showTimeSelect
                        showTimeSelectOnly
                        timeIntervals={15}
                        timeFormat='HH:mm'
                        dateFormat='HH:mm'
                        id='start-time-picker'
                        customInput={
                          <CustomInput fullWidth label={t('travels.dialogs.start.time', 'Heure de départ') as string} />
                        }
                      />
                    )}
                  />
                </DatePickerWrapper>
              </Grid>
            </Grid>
          </Grid>

          <Grid item xs={12}>
            <Controller
              name='comment'
              control={control}
              render={({ field }) => (
                <CustomTextField
                  {...field}
                  fullWidth
                  multiline
                  rows={4}
                  error={Boolean(errors.comment)}
                  helperText={errors.comment?.message}
                  label={t('travels.dialogs.start.comment', 'Commentaire')}
                />
              )}
            />
          </Grid>
        </Grid>
      </form>
    </Box>
  )
}

interface StartTravelDialogProps {
  open: boolean
  onClose: () => void
  travelId: string
}

const StartTravelDialog = ({ open, onClose, travelId }: StartTravelDialogProps) => {
  const { t } = useTranslation()
  const formRef = useRef<{ submitForm: () => void }>(null)

  const { mutateAsync, isPending } = useStartTravel(travelId, {
    onSuccess: () => {
      toast.success(t('travels.dialogs.start.success', 'Voyage démarré avec succès'))
      onClose()
    },
    onError: (error: any) => {
      toast.error(error.response.data.message)
    }
  })

  const handleSubmit = async (data: FormData) => {
    try {
      await mutateAsync({
        date: data.date,
        time: data.time,
        comment: data.comment
      })
    } catch (err) {}
  }

  const handleConfirmClick = () => {
    formRef.current?.submitForm()
  }

  return (
    <Dialog open={open} onClose={onClose} maxWidth='sm' fullWidth>
      <DialogContent>
        <StartTravelForm travelId={travelId} onSubmit={handleSubmit} formRef={formRef} />
      </DialogContent>
      <DialogActions sx={{ px: 6, py: 3 }}>
        <Button onClick={onClose} disabled={isPending}>
          {t('common.cancel', 'Annuler')}
        </Button>
        <Button variant='contained' onClick={handleConfirmClick} disabled={isPending}>
          {isPending ? <CircularProgress size={24} color='inherit' /> : t('common.confirm', 'Confirmer')}
        </Button>
      </DialogActions>
    </Dialog>
  )
}

export default StartTravelDialog
