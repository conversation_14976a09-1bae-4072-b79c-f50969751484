import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  <PERSON>alog<PERSON><PERSON>,
  Button,
  Grid,
  CircularProgress,
  Box,
  Typography
} from '@mui/material'
import { useTranslation } from 'react-i18next'
import { useRef, useImperativeHandle } from 'react'
import DatePicker from 'react-datepicker'
import { DateType } from 'src/types/forms/reactDatepickerTypes'

// ** Custom Components Imports
import CustomTextField from 'src/@core/components/mui/text-field'
import CustomInput from 'src/views/forms/form-elements/pickers/PickersCustomInput'
import DatePickerWrapper from 'src/@core/styles/libs/react-datepicker'
import { useEndTravel, useTravelById } from 'src/hooks/_services/useTravelmanagement'
import { Controller, useForm } from 'react-hook-form'
import { yupResolver } from '@hookform/resolvers/yup'
import * as yup from 'yup'
import LoadingComponent from 'src/@core/shared/components/loading-component'
import toast from 'react-hot-toast'

interface EndTravelFormProps {
  travelId: string
  onSubmit: (data: any) => void
  formRef: React.RefObject<{ submitForm: () => void }>
}

const addReturnLoadSchema = yup.object().shape({
  date: yup.date().required('La date est requise'),
  time: yup.date().required("L'heure est requise"),
  comments: yup.string()
})

interface FormData {
  date: DateType
  time: DateType
  comment: string
}

const EndTravelForm = ({ travelId, onSubmit, formRef }: EndTravelFormProps) => {
  const { t } = useTranslation()
  const { data, isLoading, isPending } = useTravelById(travelId)

  const {
    control,
    handleSubmit,
    formState: { errors }
  } = useForm<FormData>({
    resolver: yupResolver(addReturnLoadSchema),
    defaultValues: {
      date: new Date() as DateType,
      time: new Date() as DateType,
      comment: ''
    }
  })

  useImperativeHandle(formRef, () => ({
    submitForm: handleSubmit(onSubmit)
  }))

  if (isLoading || isPending) return <LoadingComponent message='Chargement...' />

  return (
    <Box sx={{ mt: 1 }}>
      <Box>
        <Typography>
          Mise à jour de vayage:Terminer le voyage pour {''}
          <Typography
            variant='overline'
            fontSize={14}
            style={{ fontWeight: 'bold', textTransform: 'uppercase' }}
            color={'primary'}
          >
            {data.camion.split('|,:')[0]}{' '}
          </Typography>{' '}
          Chauffeur
          <Typography
            variant='overline'
            fontSize={14}
            style={{ fontWeight: 'bold', textTransform: 'uppercase' }}
            color={'primary'}
          >
            {data.camion.split('|,:')[1]}
          </Typography>
        </Typography>
      </Box>
      <form onSubmit={handleSubmit(onSubmit)}>
        <Grid container spacing={4}>
          {/* Date and Time Row */}
          <Grid item xs={12}>
            <Grid container spacing={4}>
              <Grid item xs={6}>
                <DatePickerWrapper>
                  <Controller
                    name='date'
                    control={control}
                    render={({ field }) => (
                      <DatePicker
                        selected={field.value}
                        onChange={field.onChange}
                        dateFormat='dd/MM/yyyy'
                        id='start-date-picker'
                        customInput={
                          <CustomInput fullWidth label={t('travels.dialogs.start.date', 'Date de départ') as string} />
                        }
                      />
                    )}
                  />
                </DatePickerWrapper>
              </Grid>
              <Grid item xs={6}>
                <DatePickerWrapper>
                  <Controller
                    name='time'
                    control={control}
                    render={({ field }) => (
                      <DatePicker
                        selected={field.value}
                        onChange={field.onChange}
                        showTimeSelect
                        showTimeSelectOnly
                        timeIntervals={15}
                        timeFormat='HH:mm'
                        dateFormat='HH:mm'
                        id='start-time-picker'
                        customInput={
                          <CustomInput fullWidth label={t('travels.dialogs.start.time', 'Heure de départ') as string} />
                        }
                      />
                    )}
                  />
                </DatePickerWrapper>
              </Grid>
            </Grid>
          </Grid>

          {/* Comment Row */}
          <Grid item xs={12}>
            <Controller
              name='comment'
              control={control}
              render={({ field }) => (
                <CustomTextField
                  {...field}
                  fullWidth
                  multiline
                  rows={4}
                  error={Boolean(errors.comment)}
                  helperText={errors.comment?.message}
                  label={t('travels.dialogs.start.comment', 'Commentaire')}
                />
              )}
            />
          </Grid>
        </Grid>
      </form>
    </Box>
  )
}

interface EndTravelDialogProps {
  open: boolean
  onClose: () => void
  travelId: string
}

const EndTravelDialog = ({ open, onClose, travelId }: EndTravelDialogProps) => {
  const { t } = useTranslation()

  const formRef = useRef<{ submitForm: () => void }>(null)

  const { mutateAsync, isPending } = useEndTravel(travelId, {
    onSuccess: () => {
      toast.success(t('travels.dialogs.end.success', 'Voyage terminé avec succès'))
      onClose()
    },
    onError: (error: any) => {
      toast.error(error.response.data.message)
    }
  })

  const handleConfirmClick = () => {
    formRef.current?.submitForm()
  }

  const handleSubmit = async (data: FormData) => {
    try {
      await mutateAsync({
        date: data.date,
        time: data.time,
        comment: data.comment
      })
    } catch (err) {}
  }

  return (
    <Dialog open={open} onClose={onClose} fullWidth maxWidth='sm'>
      <DialogTitle>{t('travels.dialogs.endTravel.title')}</DialogTitle>
      <EndTravelForm travelId={travelId} onSubmit={handleSubmit} formRef={formRef} />
      <DialogActions>
        <Button onClick={onClose}>{t('common.actions.cancel')}</Button>
        <Button variant='contained' onClick={handleConfirmClick}>
          {isPending ? <CircularProgress size={24} color='inherit' /> : t('common.confirm', 'Confirmer')}
        </Button>
      </DialogActions>
    </Dialog>
  )
}

export default EndTravelDialog
