import { useState, useEffect } from 'react'
import { useRouter } from 'next/router'
import {
  Box,
  Card,
  CardContent,
  Typography,
  Divider,
  Chip,
  Grid,
  Button,
  CircularProgress,
  Avatar,
  IconButton,
  Breadcrumbs
} from '@mui/material'
import Link from 'next/link'
import Icon from 'src/@core/components/icon'
import { useNotification } from 'src/hooks/useNotification'
import moment from 'moment'
import CustomAvatar from 'src/@core/components/mui/avatar'
import { getInitials } from 'src/@core/utils/get-initials'
import { getNotificationTypeLabel } from 'src/utils'
import BackButton from 'src/@core/components/back-button'
import LoadingComponent from 'src/@core/shared/components/loading-component'

const NotificationView = () => {
  const router = useRouter()
  const { id } = router.query

  const { data: notification, isLoading, isError, error } = useNotification(id as string)

  const handleBack = () => {
    router.push('/notifications')
  }

  const getNotificationTypeColor = (type: string) => {
    switch (type) {
      case 'NEW':
        return 'success'
      case 'UPDATE':
        return 'info'
      default:
        return 'default'
    }
  }

  const renderAvatar = () => {
    if (!notification) return null

    if (notification.user_avatar) {
      return <Avatar alt={notification.user} src={notification.user_avatar} sx={{ width: 60, height: 60 }} />
    } else {
      return (
        <CustomAvatar skin='light' color='primary' sx={{ width: 60, height: 60, fontSize: '1.5rem' }}>
          {getInitials(notification.user || 'User')}
        </CustomAvatar>
      )
    }
  }

  if (isLoading) {
    return <LoadingComponent message='Loading notification' />
  }

  if (!notification && !isLoading) {
    return (
      <Box sx={{ p: 5, textAlign: 'center' }}>
        <Typography variant='h6'>Notification non trouvée</Typography>
        <Button variant='outlined' onClick={handleBack} sx={{ mt: 2 }}>
          Retour à la liste
        </Button>
      </Box>
    )
  }

  return (
    <Box sx={{ p: 5 }}>
      <BackButton />

      <Card>
        <CardContent sx={{ pt: 5, pb: 5 }}>
          <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 4 }}>
            <Box>
              <IconButton color='error'>
                <Icon icon='mdi:delete' />
              </IconButton>
            </Box>
          </Box>

          {/* Notification header */}
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 4 }}>
            {renderAvatar()}
            <Box sx={{ ml: 3 }}>
              <Typography variant='h5'>{notification?.title}</Typography>
              <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                <Typography variant='body2' sx={{ mr: 2 }}>
                  {notification?.user}
                </Typography>
                <Chip
                  size='small'
                  label={getNotificationTypeLabel(notification?.type || '')}
                  color={getNotificationTypeColor(notification?.type || '') as any}
                />
              </Box>
            </Box>
          </Box>

          <Divider sx={{ mb: 4 }} />

          {/* Notification content */}
          <Grid container spacing={4}>
            <Grid item xs={12}>
              <Typography
                variant='body1'

                // sx={{ mt: 1, whiteSpace: 'pre-wrap' }}
                dangerouslySetInnerHTML={{
                  __html: notification.message.replace('\n', '<br />')
                }}
              />
            </Grid>

            <Grid item xs={12}>
              <Box sx={{ backgroundColor: 'action.hover', p: 3, borderRadius: 1 }}>
                <Typography variant='subtitle2' sx={{ color: 'text.secondary', mb: 2 }}>
                  Informations complémentaires
                </Typography>
                <Grid container spacing={3}>
                  <Grid item xs={12} md={6}>
                    <Typography variant='caption' sx={{ color: 'text.disabled' }}>
                      Créé par
                    </Typography>
                    <Typography variant='body2'>
                      {notification?.created_by?.first_name} {notification?.created_by?.last_name || 'N/A'}
                    </Typography>
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <Typography variant='caption' sx={{ color: 'text.disabled' }}>
                      Créé le
                    </Typography>
                    <Typography variant='body2'>
                      {notification?.created_at ? moment(notification.created_at).format('LLL') : 'N/A'}
                    </Typography>
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <Typography variant='caption' sx={{ color: 'text.disabled' }}>
                      Modifié par
                    </Typography>
                    <Typography variant='body2'>
                      {notification?.modified_by?.first_name} {notification?.modified_by?.last_name || 'N/A'}
                    </Typography>
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <Typography variant='caption' sx={{ color: 'text.disabled' }}>
                      Modifié le
                    </Typography>
                    <Typography variant='body2'>
                      {notification?.modified_at ? moment(notification.modified_at).format('LLL') : 'N/A'}
                    </Typography>
                  </Grid>
                </Grid>
              </Box>
            </Grid>
          </Grid>
        </CardContent>
      </Card>
    </Box>
  )
}

export default NotificationView
