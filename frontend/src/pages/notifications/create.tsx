import { useState, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'
import {
  Box,
  Card,
  CardContent,
  Typography,
  Divider,
  Chip,
  Grid,
  Button,
  CircularProgress,
  Avatar,
  Breadcrumbs,
  Checkbox,
  FormControlLabel,
  FormGroup
} from '@mui/material'
import Link from 'next/link'
import Icon from 'src/@core/components/icon'
import { useNotification } from 'src/hooks/useNotification'
import moment from 'moment'
import toast from 'react-hot-toast'
import CustomAvatar from 'src/@core/components/mui/avatar'
import { getInitials } from 'src/@core/utils/get-initials'
import LoadingComponent from 'src/@core/shared/components/loading-component'

const NotificationView = () => {
  const params = useParams()
  const router = useRouter()
  const id = params?.id as string
  const [isLoading, setIsLoading] = useState(false)

  // State for checkboxes
  const [isRead, setIsRead] = useState(false)
  const [isImportant, setIsImportant] = useState(false)
  const [isArchived, setIsArchived] = useState(false)

  const { data: notification, isLoading: isLoadingData, isError, error } = useNotification(id)

  useEffect(() => {
    if (isError) {
      toast.error('Erreur lors du chargement de la notification')
      console.error(error)
    }

    // Set initial checkbox states based on notification data
    if (notification) {
      setIsRead(notification.is_read || false)
      setIsImportant(notification.is_important || false)
      setIsArchived(notification.is_archived || false)
    }
  }, [isError, error, notification])

  const handleBack = () => {
    router.push('/notifications')
  }

  const getNotificationTypeColor = (type: string) => {
    switch (type) {
      case 'NEW':
        return 'success'
      case 'UPDATE':
        return 'info'
      default:
        return 'default'
    }
  }

  const getNotificationTypeLabel = (type: string) => {
    switch (type) {
      case 'NEW':
        return 'Nouveau'
      case 'UPDATE':
        return 'Mise à jour'
      default:
        return type
    }
  }

  const renderAvatar = () => {
    if (!notification) return null

    if (notification.user_avatar) {
      return <Avatar alt={notification.user} src={notification.user_avatar} sx={{ width: 60, height: 60 }} />
    } else {
      return (
        <CustomAvatar skin='light' color='primary' sx={{ width: 60, height: 60, fontSize: '1.5rem' }}>
          {getInitials(notification.user || 'User')}
        </CustomAvatar>
      )
    }
  }

  const handleCreateAndExit = () => {
    setIsLoading(true)

    // Save notification with checkbox states
    setTimeout(() => {
      toast.success('Notification créée avec succès')
      router.push('/notifications')
    }, 1000)
  }

  const handleCreateAndSave = () => {
    setIsLoading(true)

    // Save notification with checkbox states and stay on the page
    setTimeout(() => {
      toast.success('Notification créée et enregistrée')
      setIsLoading(false)
    }, 1000)
  }

  const handleCreate = () => {
    setIsLoading(true)

    // Just create the notification
    setTimeout(() => {
      toast.success('Notification créée')
      setIsLoading(false)
    }, 1000)
  }

  if (isLoadingData) {
    return <LoadingComponent />
  }

  if (!notification && !isLoadingData) {
    return (
      <Box sx={{ p: 5, textAlign: 'center' }}>
        <Typography variant='h6'>Notification non trouvée</Typography>
        <Button variant='outlined' onClick={handleBack} sx={{ mt: 2 }}>
          Retour à la liste
        </Button>
      </Box>
    )
  }

  return (
    <Box sx={{ p: 5 }}>
      <Card>
        <CardContent sx={{ pt: 5, pb: 5 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 4 }}>
            <Button
              variant='outlined'
              color='secondary'
              startIcon={<Icon icon='mdi:arrow-left' />}
              onClick={handleBack}
            >
              Retour
            </Button>
          </Box>

          {/* Notification header */}
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 4 }}>
            {renderAvatar()}
            <Box sx={{ ml: 3 }}>
              <Typography variant='h5'>{notification?.title}</Typography>
              <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                <Typography variant='body2' sx={{ mr: 2 }}>
                  {notification?.user}
                </Typography>
                <Chip
                  size='small'
                  label={getNotificationTypeLabel(notification?.type || '')}
                  color={getNotificationTypeColor(notification?.type || '') as any}
                />
              </Box>
            </Box>
          </Box>

          <Divider sx={{ mb: 4 }} />

          {/* Notification content */}
          <Grid container spacing={4}>
            <Grid item xs={12}>
              <Typography variant='subtitle2' sx={{ color: 'text.secondary' }}>
                Contenu
              </Typography>
              <Typography variant='body1' sx={{ mt: 1, whiteSpace: 'pre-wrap' }}>
                {notification?.content || 'Aucun contenu'}
              </Typography>
            </Grid>

            {/* Checkboxes */}
            <Grid item xs={12}>
              <Typography variant='subtitle2' sx={{ color: 'text.secondary', mb: 2 }}>
                Options
              </Typography>
              <FormGroup>
                <FormControlLabel
                  control={<Checkbox checked={isRead} onChange={e => setIsRead(e.target.checked)} />}
                  label='Marquer comme lu'
                />
                <FormControlLabel
                  control={<Checkbox checked={isImportant} onChange={e => setIsImportant(e.target.checked)} />}
                  label='Marquer comme important'
                />
                <FormControlLabel
                  control={<Checkbox checked={isArchived} onChange={e => setIsArchived(e.target.checked)} />}
                  label='Archiver'
                />
              </FormGroup>
            </Grid>

            <Grid item xs={12}>
              <Box sx={{ backgroundColor: 'action.hover', p: 3, borderRadius: 1 }}>
                <Typography variant='subtitle2' sx={{ color: 'text.secondary', mb: 2 }}>
                  Informations complémentaires
                </Typography>
                <Grid container spacing={3}>
                  <Grid item xs={12} md={6}>
                    <Typography variant='caption' sx={{ color: 'text.disabled' }}>
                      Créé par
                    </Typography>
                    <Typography variant='body2'>
                      {notification?.created_by?.first_name} {notification?.created_by?.last_name || 'N/A'}
                    </Typography>
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <Typography variant='caption' sx={{ color: 'text.disabled' }}>
                      Créé le
                    </Typography>
                    <Typography variant='body2'>
                      {notification?.created_at ? moment(notification.created_at).format('LLL') : 'N/A'}
                    </Typography>
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <Typography variant='caption' sx={{ color: 'text.disabled' }}>
                      Modifié par
                    </Typography>
                    <Typography variant='body2'>
                      {notification?.modified_by?.first_name} {notification?.modified_by?.last_name || 'N/A'}
                    </Typography>
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <Typography variant='caption' sx={{ color: 'text.disabled' }}>
                      Modifié le
                    </Typography>
                    <Typography variant='body2'>
                      {notification?.modified_at ? moment(notification.modified_at).format('LLL') : 'N/A'}
                    </Typography>
                  </Grid>
                </Grid>
              </Box>
            </Grid>

            {/* Action buttons */}
            <Grid item xs={12}>
              <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 2 }}>
                <Button
                  variant='contained'
                  color='primary'
                  onClick={handleCreateAndExit}
                  disabled={isLoading}
                  startIcon={<Icon icon='mdi:check-circle' />}
                >
                  {isLoading ? (
                    <>
                      <CircularProgress size={20} sx={{ mr: 2 }} />
                      Création en cours...
                    </>
                  ) : (
                    'Créer et Quitter'
                  )}
                </Button>
                <Button
                  variant='contained'
                  color='success'
                  onClick={handleCreateAndSave}
                  disabled={isLoading}
                  startIcon={<Icon icon='mdi:content-save-edit' />}
                >
                  {isLoading ? (
                    <>
                      <CircularProgress size={20} sx={{ mr: 2 }} />
                      Enregistrement...
                    </>
                  ) : (
                    'Créer et Enregistrer'
                  )}
                </Button>
                <Button
                  variant='contained'
                  color='secondary'
                  onClick={handleCreate}
                  disabled={isLoading}
                  startIcon={<Icon icon='mdi:plus-circle' />}
                >
                  {isLoading ? (
                    <>
                      <CircularProgress size={20} sx={{ mr: 2 }} />
                      Création...
                    </>
                  ) : (
                    'Créer'
                  )}
                </Button>
              </Box>
            </Grid>
          </Grid>
        </CardContent>
      </Card>
    </Box>
  )
}

export default NotificationView
