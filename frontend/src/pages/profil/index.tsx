import React, { useState } from 'react'
import { use<PERSON><PERSON>, Controller } from 'react-hook-form'
import { yupResolver } from '@hookform/resolvers/yup'
import * as yup from 'yup'
import {
  Avatar,
  Box,
  Button,
  Card,
  CardContent,
  Grid,
  IconButton,
  Snackbar,
  Alert,
  Typography,
  CircularProgress
} from '@mui/material'
import Icon from 'src/@core/components/icon'
import CustomTextField from 'src/@core/components/mui/text-field'
import { useSelector } from 'react-redux'
import { selectUser, updateUser } from 'src/store/auth'
import { useUpdateProfileImage } from 'src/hooks/user-management/useUsers'
import { useDispatch } from 'react-redux'
import { useTranslation } from 'react-i18next'

// ✅ Validation Schema
const schema = yup.object().shape({
  firstName: yup
    .string()
    .required('First name is required')
    .min(2, 'First name must be at least 2 characters')
    .max(50, 'First name cannot exceed 50 characters'),
  lastName: yup
    .string()
    .required('Last name is required')
    .min(2, 'Last name must be at least 2 characters')
    .max(50, 'Last name cannot exceed 50 characters'),
  email: yup.string().required('Email is required').email('Please enter a valid email address')
})

export default function ProfilePage() {
  const [profileImage, setProfileImage] = useState<File | null>(null)
  const dispatch = useDispatch()
  const user = useSelector(selectUser)
  const { t } = useTranslation()
  const [previewImage, setPreviewImage] = useState<string | null>(null)
  const [loading, setLoading] = useState(false)
  const [notification, setNotification] = useState({
    open: false,
    message: '',
    severity: 'success'
  })

  const { mutateAsync: updateProfileImage, isPending: isUpdating } = useUpdateProfileImage({
    onSuccess: data => {
      dispatch(updateUser({ picture: data.picture, profil: data.picture }))
      setNotification({ open: true, message: t('forms.profile.message.update_success'), severity: 'success' })
      setLoading(false)
    },
    onError: () => {
      setNotification({ open: true, message: t('forms.profile.message.update_error'), severity: 'error' })
      setLoading(false)
    }
  })

  // ✅ React Hook Form setup with Yup
  const {
    control,
    handleSubmit,
    formState: { errors }
  } = useForm({
    resolver: yupResolver(schema),
    defaultValues: {
      firstName: user?.first_name || '',
      lastName: user?.last_name || '',
      email: user?.email || ''
    }
  })

  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file) {
      setProfileImage(file)

      const reader = new FileReader()
      reader.onload = () => {
        setPreviewImage(reader.result as string)
      }
      reader.readAsDataURL(file)
    }
  }

  const convertImageToBase64 = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()

      reader.onload = () => {
        resolve(reader.result as string)
      }

      reader.onerror = error => {
        reject(error)
      }

      reader.readAsDataURL(file)
    })
  }

  const removeImage = () => {
    setProfileImage(null)
    setPreviewImage(null)
  }

  const onSubmit = (data: any) => {
    setLoading(true)

    if (profileImage) {
      convertImageToBase64(profileImage).then(base64Image => {
        updateProfileImage({ image: base64Image, id: user?.id || '' })
      })
    }
  }

  const handleNotificationClose = () => {
    setNotification({ ...notification, open: false })
  }

  return (
    <Card elevation={3}>
      <CardContent sx={{ p: 4 }}>
        {/* Header */}
        <Box sx={{ textAlign: 'center', mb: 4 }}>
          <Typography variant='h4' component='h1' gutterBottom>
            {t('forms.profile.title')}
          </Typography>
          <Typography variant='subtitle1' color='text.secondary'>
            {t('forms.profile.desc')}
          </Typography>
        </Box>

        {/* Image Upload */}
        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', mb: 4 }}>
          <Box sx={{ position: 'relative', mb: 2 }}>
            <Avatar
              src={previewImage || '/api/placeholder/150/150'}
              alt='Profile'
              sx={{ width: 128, height: 128, border: '4px solid #e0e0e0' }}
            />
            {previewImage && (
              <IconButton
                size='small'
                sx={{
                  position: 'absolute',
                  top: -8,
                  right: -8,
                  bgcolor: 'error.main',
                  color: 'white',
                  '&:hover': { bgcolor: 'error.dark' }
                }}
                onClick={removeImage}
              >
                <Icon icon='ion:close-circle' fontSize='1.125rem' />
              </IconButton>
            )}
            <IconButton
              color='primary'
              aria-label='upload picture'
              component='label'
              sx={{
                position: 'absolute',
                bottom: 0,
                right: 0,
                bgcolor: 'primary.main',
                color: 'white',
                '&:hover': { bgcolor: 'primary.dark' }
              }}
            >
              <input hidden accept='image/*' type='file' onChange={handleImageUpload} />
              <Icon icon='ion:camera' fontSize='1.125rem' />
            </IconButton>
          </Box>

          <Button variant='outlined' component='label' startIcon={<Icon icon='ion:cloud-upload' />} sx={{ mb: 1 }}>
            {t('forms.profile.upload')}
            <input hidden accept='image/*' type='file' onChange={handleImageUpload} />
          </Button>

          <Typography variant='caption' color='text.secondary' align='center'>
            {t('forms.profile.picDes')}
          </Typography>
        </Box>

        {/* Form */}
        <Box sx={{ maxWidth: '600px', mx: 'auto' }}>
          <form onSubmit={handleSubmit(onSubmit)} noValidate>
            <Grid container spacing={3}>
              {/* First Name */}
              <Grid item xs={12}>
                <Box sx={{ maxWidth: 400, mx: 'auto' }}>
                  <Controller
                    name='firstName'
                    control={control}
                    render={({ field }) => (
                      <CustomTextField
                        {...field}
                        label={t('forms.profile.fields.firstName')}
                        variant='outlined'
                        fullWidth
                        error={!!errors.firstName}
                        helperText={errors.firstName?.message}
                      />
                    )}
                  />
                </Box>
              </Grid>

              {/* Last Name */}
              <Grid item xs={12}>
                <Box sx={{ maxWidth: 400, mx: 'auto' }}>
                  <Controller
                    name='lastName'
                    control={control}
                    render={({ field }) => (
                      <CustomTextField
                        {...field}
                        label={t('forms.profile.fields.lastName')}
                        variant='outlined'
                        fullWidth
                        error={!!errors.lastName}
                        helperText={errors.lastName?.message}
                      />
                    )}
                  />
                </Box>
              </Grid>

              {/* Email */}
              <Grid item xs={12}>
                <Box sx={{ maxWidth: 400, mx: 'auto' }}>
                  <Controller
                    name='email'
                    control={control}
                    render={({ field }) => (
                      <CustomTextField
                        {...field}
                        label={t('forms.profile.fields.email')}
                        variant='outlined'
                        fullWidth
                        error={!!errors.email}
                        helperText={errors.email?.message}
                      />
                    )}
                  />
                </Box>
              </Grid>

              {/* Submit */}
              <Grid item xs={12}>
                <Box sx={{ maxWidth: 400, mx: 'auto' }}>
                  <Button
                    variant='contained'
                    color='primary'
                    type='submit'
                    disabled={isUpdating}
                    startIcon={isUpdating ? <CircularProgress size={20} /> : <Icon icon='ion:save' />}
                    endIcon={<Icon icon='ion:chevron-forward' />}
                    size='large'
                    fullWidth
                  >
                    {loading ? t('forms.profile.saving') : t('forms.profile.save')}
                  </Button>
                </Box>
              </Grid>
            </Grid>
          </form>
        </Box>

        {/* Notification */}
        <Snackbar
          open={notification.open}
          autoHideDuration={6000}
          onClose={handleNotificationClose}
          anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
        >
          <Alert severity={notification.severity as any} onClose={handleNotificationClose} sx={{ width: '100%' }}>
            {notification.message}
          </Alert>
        </Snackbar>
      </CardContent>
    </Card>
  )
}
