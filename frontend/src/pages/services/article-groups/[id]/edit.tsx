import { Box } from '@mui/system'
import { useRouter } from 'next/router'
import React from 'react'
import BackButton from 'src/@core/components/back-button'
import { ArticleGroupForm } from 'src/@core/shared/components/article-group-form'
import ErrorComponent from 'src/@core/shared/components/error-component'
import LoadingComponent from 'src/@core/shared/components/loading-component'
import { useGroupArticleById } from 'src/hooks/helpers/useGroupArticles'

export default function EditArticleGroup() {
  const router = useRouter()
  const { id } = router.query

  const articleGroupId = id as string
  const { data, isLoading, isPending, isError, error } = useGroupArticleById(articleGroupId)

  if (isLoading || isPending) return <LoadingComponent message='Chargement...' />
  if (isError) return <ErrorComponent message='Erreur lors du chargement des données' />

  return (
    <Box>
      <BackButton />
      <ArticleGroupForm mode='edit' articleGroupId={articleGroupId} initialData={data} />
    </Box>
  )
}
