import { Box } from '@mui/material'
import { useRouter } from 'next/router'
import BackButton from 'src/@core/components/back-button'
import ErrorComponent from 'src/@core/shared/components/error-component'
import LoadingComponent from 'src/@core/shared/components/loading-component'
import OrderForm from 'src/@core/shared/order-form'
import { useOrderById } from 'src/hooks/_services/useOrders'

export default function EditOrder() {
  const router = useRouter()
  const { id } = router.query
  const orderId = id as string
  const { data, isError, isLoading, refetch } = useOrderById(orderId)

  if (isLoading) return <LoadingComponent message='Chargement...' />
  if (isError) return <ErrorComponent message='Erreur lors du chargement des données' />

  return (
    <Box>
      <BackButton />
      <OrderForm
        mode='edit'
        refetch={refetch}
        initialData={data}
        orderId={orderId}
        onSuccess={() => {
          console.log('sucess')
        }}
      />
    </Box>
  )
}
