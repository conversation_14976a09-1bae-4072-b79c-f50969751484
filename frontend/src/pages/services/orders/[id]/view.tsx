import { useRouter } from 'next/router'
import {
  Alert,
  Box,
  <PERSON>ton,
  Card,
  CardContent,
  CardHeader,
  Chip,
  Divider,
  Grid,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow
} from '@mui/material'
import { useOrderById } from 'src/hooks/_services/useOrders'
import Icon from 'src/@core/components/icon'
import { format } from 'date-fns'
import { DetailRow } from 'src/@core/shared/DetailRow'
import styled from '@mui/material/styles/styled'
import BackButton from 'src/@core/components/back-button'
import LoadingComponent from 'src/@core/shared/components/loading-component'
import ErrorComponent from 'src/@core/shared/components/error-component'

const getValidationStatus = (validations: any[]) => {
  const validationCount = validations?.length || 0
  if (validationCount === 4) return 'success'
  if (validationCount > 0) return 'warning'

  return 'default'
}

const StyledTableCell = styled(TableCell)(({ theme }) => ({
  whiteSpace: 'nowrap',
  textTransform: 'capitalize',
  padding: theme.spacing(3),
  borderBottom: `1px solid ${theme.palette.divider}`,
  '&.MuiTableCell-head': {
    fontWeight: 600,
    backgroundColor: theme.palette.background.default
  }
}))

const TableWrapper = styled(Box)(({ theme }) => ({
  overflowX: 'auto',
  '&::-webkit-scrollbar': {
    height: 6
  },
  '&::-webkit-scrollbar-thumb': {
    borderRadius: 20,
    background: theme.palette.grey[300]
  }
}))

export default function SingleOrderPage() {
  const router = useRouter()
  const { id: orderId } = router.query
  const { data, isLoading, isError } = useOrderById(orderId as string)

  if (isLoading) {
    return <LoadingComponent message='Chargement...' />
  }

  if (isError) {
    return <ErrorComponent message='Failed to load order details. Please try again later.' />
  }

  return (
    <Grid container spacing={6}>
      <Grid item xs={12}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <BackButton />
          <Box sx={{ display: 'flex', gap: 2 }}>
            <Button
              variant='contained'
              color='primary'
              startIcon={<Icon icon='mdi:printer' />}
              onClick={() => router.push(`/services/orders/${orderId}/print`)}
            >
              Print
            </Button>
          </Box>
        </Box>
      </Grid>

      <Grid item xs={12}>
        <Card>
          <CardHeader
            title='Order Details'
            action={
              <Box sx={{ display: 'flex', gap: 1 }}>
                <Chip label={data?.prestation} color='primary' size='small' />
                {data?.is_city && <Chip label='City' color='info' size='small' />}
                <Chip
                  label={`Validation ${data?.validation?.length}/4`}
                  color={getValidationStatus(data?.validation as any)}
                  size='small'
                />
              </Box>
            }
          />
          <CardContent>
            <DetailRow label='Order ID' value={data?.id} />
            <Divider />
            <DetailRow label='Code' value={data?.code} />
            <Divider />
            <DetailRow label='Customer' value={data?.customer.name} />
            <Divider />
            <DetailRow label='Created At' value={format(new Date(data?.created_at as any), 'PPP')} />
            <Divider />
            <DetailRow label='Created By' value={`${data?.created_by.first_name} ${data?.created_by.last_name}`} />
          </CardContent>
        </Card>
      </Grid>

      {/* Order Lines Card */}
      <Grid item xs={12}>
        <Card>
          <CardHeader title='Order Lines' />
          <CardContent sx={{ p: 0 }}>
            {!data?.lines?.length ? (
              <Alert severity='info' sx={{ m: 4 }}>
                No order lines available
              </Alert>
            ) : (
              <TableWrapper>
                <Table sx={{ minWidth: 800 }}>
                  <TableHead>
                    <TableRow>
                      <StyledTableCell>CAM./TRACTEUR</StyledTableCell>
                      <StyledTableCell>REMORQUE</StyledTableCell>
                      <StyledTableCell>DEPART</StyledTableCell>
                      <StyledTableCell>DESTINATION</StyledTableCell>
                      <StyledTableCell>N° BE</StyledTableCell>
                      <StyledTableCell>N°BC</StyledTableCell>
                      <StyledTableCell>DATE</StyledTableCell>
                      <StyledTableCell>PRODUIT/SERVICE</StyledTableCell>
                      <StyledTableCell>QTE</StyledTableCell>
                      <StyledTableCell>MESURE</StyledTableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {JSON.stringify(data?.lines, null, 3)}
                    {/* {data?.lines.map((line, index) => (
                      <TableRow key={index} hover>
                        <StyledTableCell>{line.vehicle}</StyledTableCell>
                        <StyledTableCell>{line.trailer}</StyledTableCell>
                        <StyledTableCell>{line.departure}</StyledTableCell>
                        <StyledTableCell>{line.destination}</StyledTableCell>
                        <StyledTableCell>{line.be_number}</StyledTableCell>
                        <StyledTableCell>{line.bc_number}</StyledTableCell>
                        <StyledTableCell>{line.date ? format(new Date(line.date), 'dd/MM/yyyy') : '-'}</StyledTableCell>
                        <StyledTableCell>{line.product}</StyledTableCell>
                        <StyledTableCell>{Number(line.qty).toLocaleString()}</StyledTableCell>
                        <StyledTableCell>{line.unit}</StyledTableCell>
                      </TableRow>
                    ))} */}
                  </TableBody>
                </Table>
              </TableWrapper>
            )}
          </CardContent>
        </Card>
      </Grid>
    </Grid>
  )
}
