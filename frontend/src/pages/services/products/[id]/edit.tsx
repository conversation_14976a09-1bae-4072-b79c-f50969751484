import { Al<PERSON>, Box, CircularProgress, Grid } from '@mui/material'
import { useRouter } from 'next/router'
import React from 'react'
import BackButton from 'src/@core/components/back-button'
import ErrorComponent from 'src/@core/shared/components/error-component'
import LoadingComponent from 'src/@core/shared/components/loading-component'
import ProductForm from 'src/@core/shared/product-form'
import { useProductById } from 'src/hooks/_services/useProduct'

export default function UpdateProductPage() {
  const router = useRouter()
  const { id: productId } = router.query

  const { data, isLoading, isError } = useProductById(productId as string)

  if (isLoading) {
    return <LoadingComponent message='Chargement...' />
  }

  if (isError) {
    return <ErrorComponent message='Failed to load order details. Please try again later.' />
  }

  console.log(data)

  return (
    <Box>
      <BackButton />
      <ProductForm
        mode='edit'
        productId={productId as string}
        initialData={{
          name: data?.name,
          service_id: data?.service.id
        }}
      />
    </Box>
  )
}
