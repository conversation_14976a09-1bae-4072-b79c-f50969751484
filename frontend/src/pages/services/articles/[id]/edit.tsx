import { Box } from '@mui/material'
import { log } from 'console'
import { useRouter } from 'next/router'
import BackButton from 'src/@core/components/back-button'
import ArticleForm from 'src/@core/shared/article-form'
import { useArticleById } from 'src/hooks/_services/useArticles'

export default function EditArticle() {
  const router = useRouter()
  const { id } = router.query
  const { data, isLoading, isPending, isError, error } = useArticleById(id as string)
  if (isLoading || isPending) {
    return <div>Chargement...</div>
  }

  if (isError) {
    return <div>Erreur lors du chargement des données</div>
  }

  const initialData = {
    code: data.code,
    label: data.label,
    related_documents: data.related_documents,
    related_articles: data.related_article,
    operation_type: data.operation_type,
    measure_unit: data?.measure_unit,
    group: data.group.id
  }

  return (
    <Box>
      <BackButton />
      <ArticleForm
        mode='edit'
        articleId={id as string}
        onSuccess={() => router.push('/services/articles')}
        initialData={initialData}
      />
    </Box>
  )
}
