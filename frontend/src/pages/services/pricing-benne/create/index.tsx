import React from 'react'
import { Box } from '@mui/material'
import { useRouter } from 'next/router'
import Icon from 'src/@core/components/icon'
import { PricingBenneForm } from 'src/@core/shared/pricing-benne-form'

export default function CreatePricingBenne() {
  const router = useRouter()

  return (
    <>
      <Box
        sx={{
          display: 'flex',
          alignItems: 'center',
          mb: 6,
          cursor: 'pointer'
        }}
        onClick={() => router.back()}
      >
        <Icon icon='mdi:arrow-left' fontSize={20} />
        <span style={{ marginLeft: '8px' }}>Retour aux tarifs</span>
      </Box>

      <PricingBenneForm mode='create' onSuccess={() => router.push('/services/pricing-benne')} />
    </>
  )
}
