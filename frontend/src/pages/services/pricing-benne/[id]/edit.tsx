import { Al<PERSON>, Box, CircularProgress } from '@mui/material'
import { useRouter } from 'next/router'
import BackButton from 'src/@core/components/back-button'
import Icon from 'src/@core/components/icon'
import ErrorComponent from 'src/@core/shared/components/error-component'
import LoadingComponent from 'src/@core/shared/components/loading-component'
import { PricingBenneForm } from 'src/@core/shared/pricing-benne-form'
import { usePricingBenneById } from 'src/hooks/_services/usePrisingBanne'

export default function UpdatePricingBenne() {
  const router = useRouter()
  const { id } = router.query
  const pricingId = id as string

  const { data, isLoading, isError } = usePricingBenneById(pricingId)

  if (!id || isLoading) {
    return <LoadingComponent message='Chargement...' />
  }

  if (isError) {
    return <ErrorComponent message='Erreur lors du chargement des données' />
  }

  return (
    <>
      <BackButton />
      <PricingBenneForm
        mode='edit'
        pricingId={pricingId}
        initialData={data}
        onSuccess={() => router.push('/services/pricing-benne')}
      />
    </>
  )
}
