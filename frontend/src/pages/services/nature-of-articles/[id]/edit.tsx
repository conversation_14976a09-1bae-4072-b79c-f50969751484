import { Box } from '@mui/material'
import { useRouter } from 'next/router'
import BackButton from 'src/@core/components/back-button'

import { useNatureOfArticleById } from 'src/hooks/helpers/useNatureOfArticles'
import { NatureOfArticleForm } from 'src/@core/shared/nature-of-articles'
import LoadingComponent from 'src/@core/shared/components/loading-component'
import ErrorComponent from 'src/@core/shared/components/error-component'

export default function EditNatureOfArticle() {
  const router = useRouter()
  const { id } = router.query
  const natureOfArticleId = id as string
  const { data, isLoading, isPending, isError, error } = useNatureOfArticleById(natureOfArticleId)

  if (isLoading || isPending) return <LoadingComponent message='Chargement...' />
  if (isError) return <ErrorComponent message='Erreur lors du chargement des données' />

  return (
    <Box>
      <BackButton />
      <NatureOfArticleForm
        mode='edit'
        natureOfArticleId={natureOfArticleId}
        initialData={{
          name: data.name
        }}
        onSuccess={() => router.push('/services/nature-of-articles')}
      />
    </Box>
  )
}
