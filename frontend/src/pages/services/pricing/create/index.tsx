import { Box } from '@mui/material'
import { useRouter } from 'next/router'
import BackButton from 'src/@core/components/back-button'
import Icon from 'src/@core/components/icon'
import { PricingRouteForm } from 'src/@core/shared/pricing-route-form'

export default function CreatePricingRoute() {
  const router = useRouter()

  return (
    <>
      <BackButton />
      <PricingRouteForm mode='create' onSuccess={() => router.push('/services/pricing')} />
    </>
  )
}
