import { Al<PERSON>, Box, CircularProgress } from '@mui/material'
import { useRouter } from 'next/router'
import BackButton from 'src/@core/components/back-button'
import Icon from 'src/@core/components/icon'
import ErrorComponent from 'src/@core/shared/components/error-component'
import LoadingComponent from 'src/@core/shared/components/loading-component'
import { PricingRouteForm } from 'src/@core/shared/pricing-route-form'
import { usePricingRouteById } from 'src/hooks/_services/usePricingRoute'

export default function UpdatePricingRoute() {
  const router = useRouter()
  const { id } = router.query
  const pricingId = id as string

  const { data, isLoading, isError } = usePricingRouteById(pricingId)

  if (!id || isLoading) {
    return <LoadingComponent message='Chargement...' />
  }

  if (isError) {
    return <ErrorComponent message='Erreur lors du chargement des données' />
  }

  return (
    <>
      <BackButton />

      <PricingRouteForm
        mode='edit'
        pricingId={pricingId}
        initialData={data}
        onSuccess={() => router.push('/services/pricing')}
      />
    </>
  )
}
