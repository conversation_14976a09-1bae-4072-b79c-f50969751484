import { Box } from '@mui/material'
import { useRouter } from 'next/router'
import BackButton from 'src/@core/components/back-button'
import ErrorComponent from 'src/@core/shared/components/error-component'
import LoadingComponent from 'src/@core/shared/components/loading-component'
import TravelForm from 'src/@core/shared/travel-form'
import { useTravelById } from 'src/hooks/_services/useTravelmanagement'

export default function EditTravel() {
  const router = useRouter()
  const { id } = router.query

  const { data, isLoading, isError } = useTravelById(id as string)

  if (!id || isLoading) {
    return <LoadingComponent message='Chargement...' />
  }

  if (isError) {
    return <ErrorComponent message='Failed to load order details. Please try again later.' />
  }

  return (
    <Box>
      <BackButton />
      <TravelForm mode='edit' travelId={id as string} travel={data} />
    </Box>
  )
}
