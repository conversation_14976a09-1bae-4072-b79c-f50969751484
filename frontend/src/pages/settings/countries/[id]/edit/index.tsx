import { Box } from '@mui/material'
import { useRouter } from 'next/router'
import BackButton from 'src/@core/components/back-button'
import Icon from 'src/@core/components/icon'
import { CountryForm } from 'src/@core/shared/country-form'

export default function EditCountry() {
  const router = useRouter()
  const { id } = router.query

  return (
    <>
      <BackButton />

      <CountryForm mode='edit' countryId={id as string} onSuccess={() => router.push('/settings/countries')} />
    </>
  )
}
