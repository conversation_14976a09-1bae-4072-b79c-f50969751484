import { useState } from 'react'
import { Box, Alert, FormControl, InputLabel, Select, MenuItem } from '@mui/material'
import Icon from 'src/@core/components/icon'
import { useCountries, useDeleteCountry } from 'src/hooks/_services/useCountries'
import { ColDef } from 'ag-grid-community'
import { useRouter } from 'next/navigation'
import toast from 'react-hot-toast'
import BaseTable from 'src/@core/components/tables/BaseTable'
import { useTranslation } from 'react-i18next'
import BackButton from 'src/@core/components/back-button'
import TableActions from 'src/@core/components/tables/TableActions'

export default function CountriesAgGridList() {
  const router = useRouter()
  const { t } = useTranslation()
  const [activeFilter, setActiveFilter] = useState<string>('all')

  const [queryParams, setQueryParams] = useState<Partial<any>>({
    limit: 100,
    offset: 0,
    search: '',
    is_active: undefined as boolean | undefined
  })

  const { data: countriesData, isLoading, isPending, isError, refetch } = useCountries(queryParams)

  const { mutate: deleteCountry } = useDeleteCountry({
    onSuccess: () => {
      toast.success(t('tables.country.deleteSuccess'))
      refetch()
    },
    onError: error => {
      toast.error(t('tables.country.deleteError'))
      console.error(error)
    }
  })

  const handleDeleteClick = (id: string) => {
    deleteCountry(id)
  }

  const handleActiveFilterChange = (event: any) => {
    const value = event.target.value as string
    setActiveFilter(value)

    let isActiveValue: boolean | undefined = undefined
    if (value === 'active') isActiveValue = true
    if (value === 'inactive') isActiveValue = false

    setQueryParams(prev => ({ ...prev, is_active: isActiveValue, offset: 0 }))
  }

  // Column definitions for AG Grid
  const columnDefs: ColDef[] = [
    {
      headerCheckboxSelection: true,
      checkboxSelection: true,
      width: 50,
      pinned: 'left'
    },
    {
      headerName: t('tables.country.columns.name') as string,
      field: 'name',
      sortable: true,
      flex: 1
    },
    {
      headerName: t('tables.country.columns.code') as string,
      field: 'code',
      sortable: true,
      width: 100
    },
    {
      headerName: t('tables.country.columns.isActive') as string,
      field: 'is_active',
      sortable: true,
      width: 120,
      cellRenderer: (params: any) => {
        return (
          <Icon
            icon={params.value ? 'mdi:check-circle' : 'mdi:close-circle'}
            fontSize={20}
            style={{ color: params.value ? '#56CA00' : '#FF4C51' }}
          />
        )
      }
    },
    {
      headerName: t('tables.country.columns.modifiedBy') as string,
      field: 'modified_by',
      sortable: true,
      width: 150,
      valueFormatter: (params: any) => {
        return params.value ? `${params.value.first_name} ${params.value.last_name}` : '-'
      }
    },
    {
      headerName: t('tables.country.columns.modifiedAt') as string,
      field: 'modified_at',
      sortable: true,
      width: 150,
      valueFormatter: (params: any) => {
        if (!params.value) return '-'

        return new Date(params.value).toLocaleDateString('fr-FR', {
          day: '2-digit',
          month: '2-digit',
          year: 'numeric'
        })
      }
    },
    {
      headerName: t('tables.country.columns.actions') as string,
      field: 'actions',
      sortable: false,
      width: 150,
      cellRenderer: (params: any) => {
        return (
          <TableActions
            id={params.data.id}
            basePath='/settings/countries'
            onDelete={handleDeleteClick}
            module={'settings'}
            model={'country'}
          />
        )
      }
    }
  ]

  const renderCustomFilters = () => (
    <FormControl size='small' sx={{ minWidth: 120 }}>
      <InputLabel id='active-filter-label'>{t('tables.country.filters.status') as string}</InputLabel>
      <Select
        labelId='active-filter-label'
        value={activeFilter}
        label={t('tables.country.filters.status')}
        onChange={handleActiveFilterChange}
      >
        <MenuItem value='all'>{t('tables.country.filters.all')}</MenuItem>
        <MenuItem value='active'>{t('tables.country.filters.active')}</MenuItem>
        <MenuItem value='inactive'>{t('tables.country.filters.inactive')}</MenuItem>
      </Select>
    </FormControl>
  )

  if (isError) {
    return <Alert severity='error'>{t('table.error')}</Alert>
  }

  return (
    <>
      <BackButton />
      <Box sx={{ p: 2 }}>
        <BaseTable
          data={countriesData}
          isLoading={isLoading}
          model='settings'
          module='countries'
          isPending={isPending}
          isError={isError}
          queryParams={queryParams}
          setQueryParams={setQueryParams}
          columnDefs={columnDefs}
          title={t('tables.country.title') as string}
          searchPlaceholder={t('tables.country.searchPlaceholder') as string}
          createButtonText={t('tables.country.createButton') as string}
          onCreateClick={() => router.push('/settings/countries/create')}
          renderCustomFilters={renderCustomFilters}
          rowSelection='multiple'
        />
      </Box>
    </>
  )
}
