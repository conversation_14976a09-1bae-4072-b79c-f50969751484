import { Box } from '@mui/material'
import { useRouter } from 'next/router'
import BackButton from 'src/@core/components/back-button'
import ErrorComponent from 'src/@core/shared/components/error-component'
import LoadingComponent from 'src/@core/shared/components/loading-component'
import PathForm from 'src/@core/shared/path-form'
import { useRouteById } from 'src/hooks/helpers/useRoutes'

function ExitRoute() {
  const router = useRouter()
  const { id } = router.query
  const routeId = id as string
  const { data, isLoading, isPending, isError, error } = useRouteById(routeId)

  if (isLoading || isPending) return <LoadingComponent message='Chargement...' />
  if (isError) return <ErrorComponent message='Erreur lors du chargement des données' />

  return (
    <Box>
      <BackButton />
      <PathForm mode='edit' initialData={data} pathId={routeId} />
    </Box>
  )
}
export default ExitRoute
