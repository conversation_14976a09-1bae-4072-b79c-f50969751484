import { Box } from '@mui/material'
import { useRouter } from 'next/router'
import Icon from 'src/@core/components/icon'
import { DestinationForm } from 'src/@core/shared/destination-form'

export default function EditDestination() {
  const router = useRouter()
  const { id } = router.query

  return (
    <>
      <Box
        sx={{
          display: 'flex',
          alignItems: 'center',
          mb: 6,
          cursor: 'pointer'
        }}
        onClick={() => router.back()}
      >
        <Icon icon='mdi:arrow-left' fontSize={20} />
        <span style={{ marginLeft: '8px' }}>Retour aux destinations</span>
      </Box>

      <DestinationForm
        mode='edit'
        destinationId={id as string}
        onSuccess={() => router.push('/settings/destinations')}
      />
    </>
  )
}
