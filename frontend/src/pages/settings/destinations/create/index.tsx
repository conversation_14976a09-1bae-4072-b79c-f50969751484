import React from 'react'
import { Box } from '@mui/material'
import { useRouter } from 'next/router'
import Icon from 'src/@core/components/icon'
import { DestinationForm } from 'src/@core/shared/destination-form'

export default function CreateDestination() {
  const router = useRouter()

  return (
    <>
      <Box
        sx={{
          display: 'flex',
          alignItems: 'center',
          mb: 6,
          cursor: 'pointer'
        }}
        onClick={() => router.back()}
      >
        <Icon icon='mdi:arrow-left' fontSize={20} />
        <span style={{ marginLeft: '8px' }}>Retour aux destinations</span>
      </Box>

      <DestinationForm mode='create' onSuccess={() => router.push('/settings/destinations')} />
    </>
  )
}
