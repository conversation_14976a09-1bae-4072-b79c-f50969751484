import { Box } from '@mui/material'
import { useRouter } from 'next/router'
import BackButton from 'src/@core/components/back-button'
import ErrorComponent from 'src/@core/shared/components/error-component'
import LoadingComponent from 'src/@core/shared/components/loading-component'

// import DocumentTypeForm from 'src/@core/shared/document-type-form'
import { useDocumentTypeById } from 'src/hooks/helpers/useDocumentType'

export default function EditDocumentType() {
  const router = useRouter()
  const { id } = router.query
  const documentTypeId = id as string
  const { data, isLoading, isPending, isError, error } = useDocumentTypeById(documentTypeId)
  if (isLoading || isPending) return <LoadingComponent message='Chargement...' />
  if (isError) return <ErrorComponent message='Erreur lors du chargement des données' />

  return (
    <Box>
      <BackButton />
      {/* <DocumentTypeForm mode='edit' documentTypeId={documentTypeId} initialData={data} /> */}
    </Box>
  )
}
