import { Box } from '@mui/material'
import { useRouter } from 'next/router'
import BackButton from 'src/@core/components/back-button'
import ErrorComponent from 'src/@core/shared/components/error-component'
import LoadingComponent from 'src/@core/shared/components/loading-component'
import StationForm from 'src/@core/shared/station-form'
import { useStationById } from 'src/hooks/useStation'

export default function EditFuelStation() {
  const router = useRouter()
  const { id } = router.query
  const fuelStationId = id as string
  const { data, isLoading, isPending, isError, error } = useStationById(fuelStationId)

  if (isLoading || isPending) return <LoadingComponent message='Chargement...' />
  if (isError) return <ErrorComponent message='Erreur lors du chargement des données' />

  return (
    <Box>
      <BackButton />
      <StationForm mode='edit' initialData={data} stationId={fuelStationId} />
    </Box>
  )
}
