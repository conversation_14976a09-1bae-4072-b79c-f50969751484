import { Al<PERSON>, Box, CircularProgress, Grid, Typography } from '@mui/material'
import { useRouter } from 'next/router'
import React from 'react'
import BackButton from 'src/@core/components/back-button'
import ErrorComponent from 'src/@core/shared/components/error-component'
import FuelConsumptionForm from 'src/@core/shared/consumption-form'
import LoadingComponent from 'src/@core/shared/components/loading-component'
import { useFuelConsumptionById } from 'src/hooks/useFeulConsumption'

export default function SingleFuelConSumption() {
  const router = useRouter()
  const { id } = router.query

  const fuelId = id as string

  const { data, isLoading, isError } = useFuelConsumptionById(fuelId)

  if (isLoading) {
    return <LoadingComponent message='Chargement...' />
  }

  if (isError) {
    return <ErrorComponent message='Failed to load Consumption details. Please try again later.' />
  }

  return (
    <Box>
      <BackButton />
      <FuelConsumptionForm mode='edit' initialData={data} consumptionId={fuelId} />
    </Box>
  )
}
