import { Can } from 'src/layouts/components/acl/Can'
import { useState } from 'react'
import { useRouter } from 'next/router'
import { useGetVehicleById } from 'src/hooks/useVehicle'

// ** MUI Imports
import Grid from '@mui/material/Grid'
import Card from '@mui/material/Card'
import CardHeader from '@mui/material/CardHeader'
import CardContent from '@mui/material/CardContent'
import Typography from '@mui/material/Typography'
import Divider from '@mui/material/Divider'
import Box from '@mui/material/Box'
import Chip from '@mui/material/Chip'
import Button from '@mui/material/Button'
import Dialog from '@mui/material/Dialog'
import DialogContent from '@mui/material/DialogContent'
import DialogActions from '@mui/material/DialogActions'
import CircularProgress from '@mui/material/CircularProgress'
import Alert from '@mui/material/Alert'

import Icon from 'src/@core/components/icon'
import BackButton from 'src/@core/components/back-button'

export default function SinglePage({ params }: { params: { id: string } }) {
  const router = useRouter()
  const { id } = router.query
  const vehicleId = id as string
  const { data, isLoading, isPending, isError, error } = useGetVehicleById(vehicleId)

  // Dialog states
  const [openDialog, setOpenDialog] = useState<boolean>(false)
  const [secondDialogOpen, setSecondDialogOpen] = useState<boolean>(false)
  const [userInput, setUserInput] = useState<string>('')

  const handleClose = () => setOpenDialog(false)
  const handleSecondDialogClose = () => {
    setSecondDialogOpen(false)
    if (userInput === 'yes') {
      // Perform delete action here
      router.push('/vehicles')
    }
  }

  const handleConfirmation = (value: string) => {
    handleClose()
    setUserInput(value)
    setSecondDialogOpen(true)
  }

  if (!id || isLoading || isPending) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: 400 }}>
        <CircularProgress />
      </Box>
    )
  }

  if (isError) {
    return (
      <Alert severity='error' sx={{ m: 4 }}>
        {error instanceof Error ? error.message : 'An error occurred while fetching data'}
      </Alert>
    )
  }

  if (!data) {
    return (
      <Alert severity='warning' sx={{ m: 4 }}>
        No vehicle data found
      </Alert>
    )
  }

  const DetailRow = ({ label, value }: { label: string; value: React.ReactNode }) => (
    <Box sx={{ display: 'flex', alignItems: 'center', py: 2 }}>
      <Typography sx={{ mr: 2, minWidth: 150, fontWeight: 600 }}>{label}:</Typography>
      <Typography>{value}</Typography>
    </Box>
  )

  return (
    <Can action={'read'} model='vehicle' module='vehicle'>
      <BackButton />
      <Grid container spacing={6}>
        {/* Action buttons row */}
        <Grid item xs={12}>
          <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 2 }}>
            <Can action={'update'} model='vehicle' module='financial'>
              <Button
                variant='contained'
                color='info'
                startIcon={<Icon icon='mdi:pencil' />}
                onClick={() => router.push(`/vehicles/${vehicleId}/update`)}
              >
                Edit Vehicle
              </Button>
            </Can>
            <Can action={'delete'} model='vehicle' module='financial'>
              <Button
                variant='contained'
                color='error'
                startIcon={<Icon icon='mdi:delete' />}
                onClick={() => setOpenDialog(true)}
              >
                Delete Vehicle
              </Button>
            </Can>
          </Box>
        </Grid>

        {/* Details card */}
        <Grid item xs={12}>
          <Card>
            <CardHeader
              title='Vehicle Details'
              action={
                <Chip
                  label={data.is_active ? 'Active' : 'Inactive'}
                  color={data.is_active ? 'success' : 'error'}
                  size='small'
                />
              }
            />
            <CardContent>
              <DetailRow label='ID' value={data.id} />
              <Divider />
              <DetailRow label='SAP Code' value={data.code} />
              <Divider />
              <DetailRow label='Number Plate' value={data.number_plate} />
              <Divider />
              <DetailRow label='Vehicle Type' value={data.vehicle_type} />
              <Divider />
              <DetailRow label='Container' value={data.container || 'N/A'} />
              <Divider />
              <DetailRow label='Kilometer Old' value={data.kilometer_old?.toLocaleString() || 'N/A'} />
              <Divider />
              <DetailRow label='Kilometer New' value={data.kilometer_new?.toLocaleString() || 'N/A'} />
              <Divider />
              <DetailRow label='Petrol Consumption' value={data.petrol_consumption || 'N/A'} />
              <Divider />
              <DetailRow label='Created At' value={new Date(data.created_at).toLocaleString()} />
              <Divider />
              <DetailRow label='Modified At' value={new Date(data.modified_at).toLocaleString()} />
            </CardContent>
          </Card>
        </Grid>
      </Grid>
      {/* Delete Confirmation Dialog */}
      <Dialog
        fullWidth
        open={openDialog}
        onClose={handleClose}
        sx={{ '& .MuiPaper-root': { width: '100%', maxWidth: 512 } }}
      >
        <DialogContent
          sx={{
            pb: theme => `${theme.spacing(6)} !important`,
            px: theme => [`${theme.spacing(5)} !important`, `${theme.spacing(15)} !important`],
            pt: theme => [`${theme.spacing(8)} !important`, `${theme.spacing(12.5)} !important`]
          }}
        >
          <Box
            sx={{
              display: 'flex',
              textAlign: 'center',
              alignItems: 'center',
              flexDirection: 'column',
              justifyContent: 'center',
              '& svg': { mb: 6, color: 'warning.main' }
            }}
          >
            <Icon icon='tabler:alert-circle' fontSize='5.5rem' />
            <Typography variant='h4' sx={{ mb: 5 }}>
              Are you sure?
            </Typography>
            <Typography>You won't be able to revert this!</Typography>
          </Box>
        </DialogContent>
        <DialogActions
          sx={{
            justifyContent: 'center',
            px: theme => [`${theme.spacing(5)} !important`, `${theme.spacing(15)} !important`],
            pb: theme => [`${theme.spacing(8)} !important`, `${theme.spacing(12.5)} !important`]
          }}
        >
          <Button variant='contained' color='success' onClick={() => handleConfirmation('yes')}>
            Yes, delete it!
          </Button>
          <Button variant='outlined' color='secondary' onClick={() => handleConfirmation('cancel')}>
            Cancel
          </Button>
        </DialogActions>
      </Dialog>
      {/* Result Dialog */}
      <Dialog
        fullWidth
        open={secondDialogOpen}
        onClose={handleSecondDialogClose}
        sx={{ '& .MuiPaper-root': { width: '100%', maxWidth: 512 } }}
      >
        <DialogContent
          sx={{
            pb: theme => `${theme.spacing(6)} !important`,
            px: theme => [`${theme.spacing(5)} !important`, `${theme.spacing(15)} !important`],
            pt: theme => [`${theme.spacing(8)} !important`, `${theme.spacing(12.5)} !important`]
          }}
        >
          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
              flexDirection: 'column',
              '& svg': { mb: 6 }
            }}
          >
            <Icon
              fontSize='5.5rem'
              icon={userInput === 'yes' ? 'tabler:circle-check' : 'tabler:circle-x'}
              color={userInput === 'yes' ? 'success.main' : 'error.main'}
            />
            <Typography variant='h4' sx={{ mb: 5 }}>
              {userInput === 'yes' ? 'Deleted!' : 'Cancelled'}
            </Typography>
            <Typography>{userInput === 'yes' ? 'Vehicle has been deleted.' : 'Vehicle deletion cancelled'}</Typography>
          </Box>
        </DialogContent>
        <DialogActions
          sx={{
            justifyContent: 'center',
            px: theme => [`${theme.spacing(5)} !important`, `${theme.spacing(15)} !important`],
            pb: theme => [`${theme.spacing(8)} !important`, `${theme.spacing(12.5)} !important`]
          }}
        >
          <Button variant='contained' color='success' onClick={handleSecondDialogClose}>
            OK
          </Button>
        </DialogActions>
      </Dialog>
    </Can>
  )
}
