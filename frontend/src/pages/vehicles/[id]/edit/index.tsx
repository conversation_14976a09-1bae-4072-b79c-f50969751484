import { Al<PERSON>, Box, CircularProgress } from '@mui/material'
import { useRouter } from 'next/router'
import React from 'react'
import Icon from 'src/@core/components/icon'
import { toast } from 'react-hot-toast'
import { useGetVehicleById } from 'src/hooks/useVehicle'
import { updateVehicle } from 'src/services/vehicles'
import VehicleForm from 'src/@core/shared/vehicle-form'
import ErrorComponent from 'src/@core/shared/components/error-component'
import LoadingComponent from 'src/@core/shared/components/loading-component'
import BackButton from 'src/@core/components/back-button'

export default function UpdateVehicle() {
  const router = useRouter()
  const { id } = router.query
  const vehicleId = id as string
  const { data, isLoading, isPending, isError, error } = useGetVehicleById(vehicleId)

  if (!id || isLoading || isPending) {
    return <LoadingComponent message='' />
  }

  if (isError) {
    return <ErrorComponent message='' />
  }

  if (!data) {
    return <ErrorComponent severity='warning' message='' />
  }

  return (
    <>
      <BackButton />
      <VehicleForm mode='edit' initialData={data} vehicleId={vehicleId} />
    </>
  )
}
