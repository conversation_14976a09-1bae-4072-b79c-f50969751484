import Link from 'next/link'
import { Contain<PERSON>, <PERSON>po<PERSON>, <PERSON><PERSON>, <PERSON>rid, Card, CardContent, CardActions, CardHeader, Box } from '@mui/material'
import Icon from 'src/@core/components/icon'
import { formatDistanceToNow } from 'date-fns'

// import { getTemplates, deleteTemplate } from '@api/email/templates'
// import { Template } from 'src/types/models/email'

export default async function TemplatesPage() {
  return (
    <Container maxWidth='lg' sx={{ py: 4 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
        <Box>
          <Typography variant='h4' component='h1' gutterBottom fontWeight='bold'>
            Email Templates
          </Typography>
          <Typography variant='subtitle1' color='text.secondary'>
            Manage your email templates and create new ones
          </Typography>
        </Box>
        <Button component={Link} href='/editor/new' variant='contained' startIcon={<Icon icon='tabler:plus' />}>
          Create New Template
        </Button>
      </Box>
      {/* 
      {templates.length === 0 ? (
        <Box
          sx={{
            textAlign: 'center',
            p: 6,
            border: 1,
            borderColor: 'divider',
            borderRadius: 1,
            bgcolor: 'background.paper'
          }}
        >
          <Typography variant='h6' gutterBottom>
            No templates yet
          </Typography>
          <Typography color='text.secondary' paragraph>
            Create your first email template to get started
          </Typography>
          <Button component={Link} href='/editor/new' variant='contained' startIcon={<AddIcon />}>
            Create Template
          </Button>
        </Box>
      ) : (
        <Grid container spacing={3}>
          {templates.map(template => (
            <Grid item xs={12} sm={6} md={4} key={template.id}>
              <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
                <CardHeader
                  title={template.name}
                  subheader={`Last updated ${formatDistanceToNow(new Date(template.updatedAt), { addSuffix: true })}`}
                  titleTypographyProps={{ noWrap: true, title: template.name }}
                />
                <CardContent sx={{ flexGrow: 1 }}>
                  <Box
                    sx={{
                      height: 160,
                      border: 1,
                      borderColor: 'divider',
                      borderRadius: 1,
                      overflow: 'hidden',
                      bgcolor: 'background.paper'
                    }}
                  >
                    <iframe
                      srcDoc={template.html}
                      title={template.name}
                      style={{ width: '100%', height: '100%', pointerEvents: 'none' }}
                    />
                  </Box>
                </CardContent>
                <CardActions sx={{ justifyContent: 'space-between', px: 2, pb: 2 }}>
                  <Box sx={{ display: 'flex', gap: 1 }}>
                    <Button
                      component={Link}
                      href={`/editor/${template.id}`}
                      variant='outlined'
                      size='small'
                      startIcon={<EditIcon />}
                    >
                      Edit
                    </Button>
                    <Button
                      component={Link}
                      href={`/preview/${template.id}`}
                      variant='outlined'
                      size='small'
                      startIcon={<VisibilityIcon />}
                    >
                      Preview
                    </Button>
                  </Box>
                  <form action={deleteTemplate.bind(null, template.id)}>
                    <Button type='submit' variant='outlined' color='error' size='small' startIcon={<DeleteIcon />}>
                      Delete
                    </Button>
                  </form>
                </CardActions>
              </Card>
            </Grid>
          ))}
        </Grid>
      )} */}
    </Container>
  )
}
