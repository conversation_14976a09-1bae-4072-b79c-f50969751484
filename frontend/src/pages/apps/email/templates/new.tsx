import { Container, Typography, <PERSON><PERSON>, <PERSON>rid, <PERSON>, Box } from '@mui/material'
import Link from 'next/link'

// ** Icon Imports
import Icon from 'src/@core/components/icon'

// import DynamicPreview from 'src/components/dynamic-template-view'

// import type { Template } from 'src/types/models/email'

export default async function PreviewPage({ params }: { params: { id: string } }) {
  // const template: Template | null = await getTemplate(params.id)
  const template = {}

  if (!template) {
    return (
      <Container maxWidth='lg' sx={{ py: 4 }}>
        <Box
          sx={{
            textAlign: 'center',
            p: 6,
            border: 1,
            borderColor: 'divider',
            borderRadius: 1,
            bgcolor: 'background.paper'
          }}
        >
          <Typography variant='h6' gutterBottom>
            Template not found
          </Typography>
          <Button component={Link} href='/templates' variant='contained' startIcon={<Icon icon='tabler:arrow-left' />}>
            Back to Templates
          </Button>
        </Box>
      </Container>
    )
  }

  return (
    <Container maxWidth='lg' sx={{ py: 4 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
        <Box>
          {/* <Typography variant='h4' component='h1' gutterBottom fontWeight='bold'>
            {template.name}
          </Typography> */}
          <Typography variant='subtitle1' color='text.secondary'>
            Preview and test your email template with dynamic data
          </Typography>
        </Box>
        <Button component={Link} href='/templates' variant='outlined' startIcon={<Icon icon='tabler:arrow-left' />}>
          Back to Templates
        </Button>
      </Box>

      {/* <Grid container spacing={4}>
        <Grid item xs={12} md={4}>
          <Card sx={{ p: 3, height: '100%' }}>
            <Typography variant='h6' gutterBottom>
              Test Dynamic Data
            </Typography>
            <DynamicPreview templateHtml={template.html} />
          </Card>
        </Grid>

        <Grid item xs={12} md={8}>
          <Card sx={{ p: 3 }}>
            <Typography variant='h6' gutterBottom>
              Template Preview
            </Typography>
            <Box
              sx={{
                border: 1,
                borderColor: 'divider',
                borderRadius: 1,
                p: 2,
                bgcolor: 'background.paper'
              }}
            >
              <iframe
                id='preview-iframe'
                srcDoc={template.html}
                title={template.name}
                style={{ width: '100%', minHeight: 500 }}
              />
            </Box>
          </Card>
        </Grid>
      </Grid> */}
    </Container>
  )
}
