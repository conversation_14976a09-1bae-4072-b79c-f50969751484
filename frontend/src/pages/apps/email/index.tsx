import Link from 'next/link'
import { Typography, Button, Container, Box, Paper } from '@mui/material'

export default function Home() {
  return (
    <Container maxWidth='lg'>
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          minHeight: '100vh',
          py: 4,
          textAlign: 'center'
        }}
      >
        <Box sx={{ mb: 4 }}>
          <Typography variant='h2' component='h1' gutterBottom fontWeight='bold'>
            Email Template Builder
          </Typography>
          <Typography variant='h6' color='text.secondary' sx={{ maxWidth: 600, mx: 'auto', mb: 4 }}>
            Create beautiful, responsive email templates with our drag-and-drop editor. Add dynamic content placeholders
            and save your templates for future use.
          </Typography>
        </Box>

        <Box sx={{ display: 'flex', gap: 2, mb: 6 }}>
          <Button component={Link} href='/apps/email/editor/new' variant='contained' size='large'>
            Create New Template
          </Button>
          <Button component={Link} href='/templates' variant='outlined' size='large'>
            View Templates
          </Button>
        </Box>

        <Paper
          elevation={3}
          sx={{
            width: '100%',
            maxWidth: 700,
            overflow: 'hidden',
            borderRadius: 2
          }}
        >
          <Box
            component='img'
            src='/placeholder.svg?height=400&width=700'
            alt='Email editor preview'
            sx={{ width: '100%', height: 'auto' }}
          />
        </Paper>
      </Box>
    </Container>
  )
}
