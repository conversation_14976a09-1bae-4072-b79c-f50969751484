import type React from 'react'
import { useEffect, useRef, useState } from 'react'
import { useRouter } from 'next/router'
import dynamic from 'next/dynamic'
import {
  Box,
  Button,
  Container,
  TextField,
  Typography,
  AppBar,
  Toolbar,
  CircularProgress,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions
} from '@mui/material'
import Icon from 'src/@core/components/icon'
import { GrapeJSEditorRef, Template } from 'src/types/email-template'
import BlankLayout from 'src/@core/layouts/BlankLayout'

// Dynamically import GrapeJS to avoid SSR issues
const GrapeJSEditor = dynamic(() => import('src/components/grapes-js-editor'), {
  ssr: false,
  loading: () => (
    <Box
      sx={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center'

        // height: 'calc(100vh - 120px)'
      }}
    >
      <CircularProgress size={40} />
      <Typography sx={{ ml: 2 }}>Loading editor...</Typography>
    </Box>
  )
})

function EditorPage() {
  const router = useRouter()
  const { id } = router.query
  const editorRef = useRef<GrapeJSEditorRef | null>(null)
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [templateName, setTemplateName] = useState('')
  const [templateData, setTemplateData] = useState<Template | null>(null)
  const [saveDialogOpen, setSaveDialogOpen] = useState(false)
  const [saveError, setSaveError] = useState('')

  useEffect(() => {
    if (!id) return

    const loadTemplate = async () => {
      if (id === 'new') {
        setTemplateName('Untitled Template')
        setLoading(false)

        return
      }

      try {
        // Uncomment when ready to use getTemplate
        // const template = await getTemplate(id as string)
        // if (template) {
        //   setTemplateData(template)
        //   setTemplateName(template.name)
        // }
        setLoading(false)
      } catch (error) {
        console.error('Failed to load template:', error)
        setLoading(false)
      }
    }

    loadTemplate()
  }, [id])

  const handleSaveClick = () => {
    if (!editorRef.current) {
      alert('Editor not initialized')

      return
    }
    setSaveError('')
    setSaveDialogOpen(true)
  }

  const handleSaveConfirm = async () => {
    if (!templateName.trim()) {
      setSaveError('Please enter a template name')

      return
    }

    setSaving(true)
    try {
      const editor = editorRef.current!
      const html = editor.getHtml()
      const css = editor.getCss()
      const components = editor.getComponents()

      // Uncomment when ready to use saveTemplate
      // const templateId = await saveTemplate({
      //   id: id === 'new' ? null : id as string,
      //   name: templateName,
      //   html,
      //   css,
      //   components: JSON.stringify(components)
      // })

      // if (id === 'new' && templateId) {
      //   router.push(`/editor/${templateId}`)
      // }

      alert('Template saved successfully (mock)')
      setSaveDialogOpen(false)
    } catch (error) {
      console.error('Failed to save template:', error)
      setSaveError('Failed to save template')
    } finally {
      setSaving(false)
    }
  }

  if (loading) {
    return (
      <Box
        sx={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          height: '100vh'
        }}
      >
        <CircularProgress size={40} />
        <Typography sx={{ ml: 2 }}>Loading template...</Typography>
      </Box>
    )
  }

  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', height: '100vh' }}>
      {/* <AppBar position='static' color='default' elevation={1}>
        <Toolbar>
          <Container maxWidth='lg' sx={{ display: 'flex', justifyContent: 'space-between', width: '100%' }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Button
                variant='outlined'
                startIcon={<Icon icon='tabler:arrow-left' />}
                onClick={() => router.push('/apps/email/templates')}
              >
                Back to Templates
              </Button>
            </Box>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Button
                variant='contained'
                startIcon={<Icon icon='tabler:device-floppy' />}
                onClick={handleSaveClick}
                disabled={saving}
              >
                {saving ? 'Saving...' : 'Save Template'}
              </Button>
            </Box>
          </Container>
        </Toolbar>
      </AppBar> */}

      <Box sx={{ flexGrow: 1 }}>
        <GrapeJSEditor ref={editorRef} initialData={templateData} />
      </Box>

      {/* Save Template Dialog */}
      <Dialog open={saveDialogOpen} onClose={() => setSaveDialogOpen(false)} maxWidth='sm' fullWidth>
        <DialogTitle>Save Template</DialogTitle>
        <DialogContent>
          <Box sx={{ mt: 2 }}>
            <TextField
              autoFocus
              label='Template Name'
              fullWidth
              value={templateName}
              onChange={e => setTemplateName(e.target.value)}
              error={!!saveError}
              helperText={saveError}
              variant='outlined'
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setSaveDialogOpen(false)} disabled={saving}>
            Cancel
          </Button>
          <Button onClick={handleSaveConfirm} variant='contained' disabled={saving}>
            {saving ? (
              <>
                <CircularProgress size={20} sx={{ mr: 2 }} />
                Saving...
              </>
            ) : (
              'Save'
            )}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  )
}

EditorPage.getLayout = (page: React.ReactNode) => <BlankLayout>{page}</BlankLayout>

export default EditorPage
