import { useRouter } from 'next/router'
import React from 'react'
import { AgencyForm } from 'src/@core/shared/agency-form'
import ErrorComponent from 'src/@core/shared/components/error-component'
import LoadingComponent from 'src/@core/shared/components/loading-component'
import { useAgencyById } from 'src/hooks/useAgency'

export default function EditAgency() {
  const router = useRouter()
  const { id } = router.query
  const agencyId = id as string
  const { data, isLoading, isPending, isError, error } = useAgencyById(agencyId)
  if (isLoading || isPending) return <LoadingComponent message='Chargement...' />
  if (isError) return <ErrorComponent message='Erreur lors du chargement des données' />

  return (
    <div>
      <AgencyForm
        mode='edit'
        agencyId={agencyId}
        initialData={{
          name: data.name,
          address: data.address,
          company: data.company.id,
          is_active: data.is_active
        }}
        onSuccess={() => router.push('/user-management/agencies')}
      />
    </div>
  )
}
