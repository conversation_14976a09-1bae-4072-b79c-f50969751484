import { Box } from '@mui/material'
import { useRouter } from 'next/router'
import BackButton from 'src/@core/components/back-button'
import ErrorComponent from 'src/@core/shared/components/error-component'
import LoadingComponent from 'src/@core/shared/components/loading-component'
import DataValidationForm from 'src/@core/shared/data-validation-fron'

// import ValidationForm from 'src/@core/shared/validation-form'
import { useValidationById } from 'src/hooks/user-management/useValidation'

export default function EditValidation() {
  const router = useRouter()
  const { id } = router.query
  const validationId = id as string
  const { data, isLoading, isPending, isError, error } = useValidationById(validationId)
  if (isLoading || isPending) return <LoadingComponent message='Chargement...' />
  if (isError) return <ErrorComponent message='Erreur lors du chargement des données' />

  return (
    <Box>
      <BackButton />
      <DataValidationForm
        mode='edit'
        dataValidationId={validationId}
        initialData={{
          level: data.level,
          level_to_validate: data.level_to_validate,
          data_type: data.data_type
        }}
      />
    </Box>
  )
}
