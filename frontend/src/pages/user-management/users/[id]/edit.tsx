import { Box } from '@mui/material'
import { useRouter } from 'next/router'
import BackButton from 'src/@core/components/back-button'
import ErrorComponent from 'src/@core/shared/components/error-component'
import LoadingComponent from 'src/@core/shared/components/loading-component'
import UserForm from 'src/@core/shared/users-forms'
import { useUserById } from 'src/hooks/user-management/useUsers'

export default function EditUser() {
  const router = useRouter()
  const { id } = router.query
  const userId = id as string
  const { data, isLoading, isPending, isError, error } = useUserById(userId)
  if (isLoading || isPending) return <LoadingComponent message='Chargement...' />
  if (isError) return <ErrorComponent message='Erreur lors du chargement des données' />

  return (
    <Box>
      <BackButton />
      <UserForm mode='edit' userId={userId} initialData={data} />
    </Box>
  )
}
