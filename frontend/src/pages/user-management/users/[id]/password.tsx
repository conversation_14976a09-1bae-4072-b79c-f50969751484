import { <PERSON>, <PERSON><PERSON>, <PERSON>, CardContent, CardHeader, <PERSON>rid, IconButton, InputAdornment, Typography } from '@mui/material'
import { useTranslation } from 'react-i18next'
import { useState } from 'react'
import { Icon } from '@iconify/react'
import { useRouter } from 'next/router'
import { useAdminChangePassword, useUserById } from 'src/hooks/user-management/useUsers'
import { toast } from 'react-hot-toast'
import CustomTextField from 'src/@core/components/mui/text-field'
import { useForm, Controller } from 'react-hook-form'
import { yupResolver } from '@hookform/resolvers/yup'
import { passwordSchema } from 'src/lib/schemas'
import { PasswordPayload, UserRequestPayload } from 'src/types/models/user-management/users'
import { useQueryClient } from '@tanstack/react-query'
import { LoadingButton } from '@mui/lab'
import BackButton from 'src/@core/components/back-button'
import LoadingComponent from 'src/@core/shared/components/loading-component'
import ErrorComponent from 'src/@core/shared/components/error-component'

export default function ChangePassword() {
  const { t } = useTranslation()
  const router = useRouter()
  const queryClient = useQueryClient()
  const { id } = router.query
  const userId = id as string
  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)

  const { data: user, isLoading, isPending, isError, error } = useUserById(userId)

  const {
    mutateAsync: updateUser,
    isPending: isUpdating,
    data: updateUserRes
  } = useAdminChangePassword({
    onSuccess: () => {
      toast.success(t('forms.usersForm.messages.update_success'))
      queryClient.invalidateQueries({ queryKey: ['users'] })
      router.push('/user-management/users')
    },
    onError: () => {
      toast.error(t('forms.usersForm.messages.update_error'))
    }
  })

  const {
    control,
    handleSubmit,
    reset,
    formState: { errors }
  } = useForm<PasswordPayload>({
    resolver: yupResolver(passwordSchema),
    defaultValues: {}
  })

  const onSubmit = async (data: any) => {
    try {
      await updateUser({ id: userId, ...data })
    } catch (error) {
      console.error(error)
    }
  }

  if (isLoading || isPending) return <LoadingComponent message='Chargement...' />
  if (isError) return <ErrorComponent message='Erreur lors du chargement des données' />

  return (
    <>
      <BackButton />
      <Card>
        <CardHeader title={t('forms.changePasswordForm.title')} />
        <CardContent>
          <form onSubmit={handleSubmit(onSubmit)}>
            <Grid item xs={12} sm={4} sx={{ mb: 4, mt: 4 }}>
              <Grid container xs={12} sm={6}>
                <Typography variant='h5' sx={{ mb: 2 }} color={'primary.main'}>
                  {t('forms.changePasswordForm.reset.description')} {`${user?.first_name} ${user?.last_name}`}
                </Typography>

                <Box sx={{ pl: 2, borderLeft: '3px solid', borderColor: 'primary.main', mt: 1 }}>
                  <Typography variant='body2' sx={{ mb: 1 }}>
                    {t('forms.usersForm.descriptions.password.1')}
                  </Typography>
                  <Typography variant='body2' sx={{ mb: 1 }}>
                    {t('forms.usersForm.descriptions.password.2')}
                  </Typography>
                  <Typography variant='body2' sx={{ mb: 1 }}>
                    {t('forms.usersForm.descriptions.password.3')}
                  </Typography>
                  <Typography variant='body2'>{t('forms.usersForm.descriptions.password.4')}</Typography>
                </Box>
                <Controller
                  name='new_password'
                  control={control}
                  render={({ field }) => (
                    <CustomTextField
                      {...field}
                      sx={{ mt: 4 }}
                      fullWidth
                      type={showPassword ? 'text' : 'password'}
                      label={t('forms.changePasswordForm.fields.password')}
                      error={Boolean(errors.new_password)}
                      helperText={errors.new_password ? 'Password must be at least 8 characters' : ''}
                      InputProps={{
                        endAdornment: (
                          <InputAdornment position='end'>
                            <IconButton
                              edge='end'
                              onClick={() => setShowPassword(!showPassword)}
                              onMouseDown={e => e.preventDefault()}
                              aria-label='toggle password visibility'
                            >
                              <Icon fontSize='1.25rem' icon={showPassword ? 'tabler:eye' : 'tabler:eye-off'} />
                            </IconButton>
                          </InputAdornment>
                        )
                      }}
                    />
                  )}
                />
              </Grid>
            </Grid>

            <Grid item xs={12} sm={4}>
              <Grid container xs={12} sm={6}>
                <Controller
                  name='confirm_new_password'
                  control={control}
                  render={({ field }) => (
                    <CustomTextField
                      {...field}
                      fullWidth
                      type={showConfirmPassword ? 'text' : 'password'}
                      label={t('forms.changePasswordForm.fields.confirmPassword')}
                      error={Boolean(errors.confirm_new_password)}
                      helperText={
                        errors.confirm_new_password
                          ? errors.confirm_new_password.message
                          : t('forms.usersForm.descriptions.confirmPassword')
                      }
                      InputProps={{
                        endAdornment: (
                          <InputAdornment position='end'>
                            <IconButton
                              edge='end'
                              onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                              onMouseDown={e => e.preventDefault()}
                              aria-label='toggle password visibility'
                            >
                              <Icon fontSize='1.25rem' icon={showConfirmPassword ? 'tabler:eye' : 'tabler:eye-off'} />
                            </IconButton>
                          </InputAdornment>
                        )
                      }}
                    />
                  )}
                />
              </Grid>
            </Grid>
            <Box sx={{ mt: 5, display: 'flex', justifyContent: 'flex-end' }}>
              <LoadingButton loading={isUpdating} variant='contained' type='submit' sx={{ mr: 4 }}>
                {t('forms.changePasswordForm.save')}
              </LoadingButton>
              <Button type='reset' variant='tonal' color='secondary' onClick={() => reset()}>
                {t('forms.changePasswordForm.resetButton')}
              </Button>
            </Box>
          </form>
        </CardContent>
      </Card>
    </>
  )
}
