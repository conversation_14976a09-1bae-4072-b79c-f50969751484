import { Box } from '@mui/material'
import { useRouter } from 'next/router'
import BackButton from 'src/@core/components/back-button'
import ErrorComponent from 'src/@core/shared/components/error-component'
import LoadingComponent from 'src/@core/shared/components/loading-component'
import CustomerForm from 'src/@core/shared/customer-form'
import { useCustomerById } from 'src/hooks/useCustomer'

export default function EditCustomer() {
  const router = useRouter()
  const { id } = router.query
  const customerId = id as string
  const { data, isLoading, isPending, isError, error } = useCustomerById(customerId)
  if (isLoading || isPending) return <LoadingComponent message='Chargement...' />
  if (isError) return <ErrorComponent message='Erreur lors du chargement des données' />

  return (
    <Box>
      <BackButton />
      <CustomerForm mode='edit' customerId={customerId} initialData={data} />
    </Box>
  )
}
