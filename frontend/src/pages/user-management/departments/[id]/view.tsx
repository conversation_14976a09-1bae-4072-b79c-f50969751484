import { Box } from '@mui/system'
import { useRouter } from 'next/router'
import React from 'react'
import BackButton from 'src/@core/components/back-button'
import ErrorComponent from 'src/@core/shared/components/error-component'
import LoadingComponent from 'src/@core/shared/components/loading-component'
import UserDepartmentForm from 'src/@core/shared/user-departmen-form'
import { useUserDepartmentById } from 'src/hooks/_services/useUserDepartment'

export default function ViewUserDepartment() {
  const router = useRouter()
  const { id } = router.query
  const departmentId = id as string
  const { data, isLoading, isPending, isError, error } = useUserDepartmentById(departmentId)
  if (isLoading || isPending) return <LoadingComponent message='Chargement...' />
  if (isError) return <ErrorComponent message='Erreur lors du chargement des données' />

  return (
    <Box>
      <BackButton />
      <UserDepartmentForm mode='edit' departmentId={departmentId} initialData={data} />
    </Box>
  )
}
