import { Box } from '@mui/material'
import { useRouter } from 'next/router'
import BackButton from 'src/@core/components/back-button'
import DriverForm from 'src/@core/shared/drivers-form'
import { useDriverById } from 'src/hooks/user-management/useDrivers'
import ErrorComponent from 'src/@core/shared/components/error-component'
import LoadingComponent from 'src/@core/shared/components/loading-component'

export default function EditDriver() {
  const router = useRouter()
  const { id } = router.query
  const driverId = id as string
  const { data, isLoading, isError } = useDriverById(driverId)

  if (isLoading) return <LoadingComponent message='Chargement...' />

  if (isError) return <ErrorComponent message='Failed to load order details. Please try again later.' />

  return (
    <Box>
      <BackButton />
      <DriverForm mode='edit' driverId={driverId} initialData={data} />
    </Box>
  )
}
