import { useRouter } from 'next/router'
import React from 'react'
import BlankLayout from 'src/@core/layouts/BlankLayout'
import MissionExpenseOthersReport from 'src/@core/pdf-templates/mission-expense-other'
import ErrorComponent from 'src/@core/shared/components/error-component'
import LoadingComponent from 'src/@core/shared/components/loading-component'
import { useMissionExpenseOtherById } from 'src/hooks/useMissionExpenseOthers'

function SingleMissionExpenseOtherPage() {
  const router = useRouter()
  const { id } = router.query
  const missionExpenseOtherId = id as string

  const { data, isLoading, isPending, isError, error } = useMissionExpenseOtherById(missionExpenseOtherId)

  if (isLoading) {
    return <LoadingComponent message='Loading Mission Expense Others...' />
  }

  if (isError) {
    return <ErrorComponent message='Failed to load invoice details. Please try again later.' />
  }

  return <MissionExpenseOthersReport data={data} />
}

SingleMissionExpenseOtherPage.getLayout = (page: React.ReactNode) => <BlankLayout>{page}</BlankLayout>
export default SingleMissionExpenseOtherPage
