import React from 'react'
import { useRouter } from 'next/router'
import { useMissionExpenseOtherById } from 'src/hooks/useMissionExpenseOthers'
import ErrorComponent from 'src/@core/shared/components/error-component'
import LoadingComponent from 'src/@core/shared/components/loading-component'
import MissionExpenceOthersForm from 'src/@core/shared/mission-expense-other-form'
import { Box } from '@mui/material'
import BackButton from 'src/@core/components/back-button'

export default function EditMissionExpenseOther() {
  const router = useRouter()
  const { id } = router.query
  const missionExpenseOtherId = id as string
  const { data, isLoading, isPending, isError, error } = useMissionExpenseOtherById(missionExpenseOtherId)
  if (isLoading || isPending) return <LoadingComponent message='Chargement...' />
  if (isError) return <ErrorComponent message='Erreur lors du chargement des données' />

  return (
    <Box>
      <BackButton />
      <MissionExpenceOthersForm initialData={data} mode='edit' missionExpenseOtherId={missionExpenseOtherId} />
    </Box>
  )
}
