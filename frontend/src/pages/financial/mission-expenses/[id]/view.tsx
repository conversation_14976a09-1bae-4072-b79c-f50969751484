import { Alert, Box, CircularProgress, Grid } from '@mui/material'
import { useRouter } from 'next/router'
import React from 'react'
import BlankLayout from 'src/@core/layouts/BlankLayout'
import MissionExpenseReport from 'src/@core/pdf-templates/mission-expense'
import { useMissionExpenseById } from 'src/hooks/useMissionExpenses'

function SingleMissionExpensePage() {
  const router = useRouter()
  const { id: missionExpenseId } = router.query

  const { data, isLoading, isError } = useMissionExpenseById(missionExpenseId as string)

  if (!missionExpenseId || typeof missionExpenseId !== 'string') {
    return (
      <Grid container spacing={6}>
        <Grid item xs={12}>
          <Alert severity='error'>Invalid invoice ID</Alert>
        </Grid>
      </Grid>
    )
  }

  if (isLoading) {
    return (
      <Box sx={{ mt: 6, display: 'flex', alignItems: 'center', flexDirection: 'column' }}>
        <CircularProgress sx={{ mb: 4 }} />
      </Box>
    )
  }

  if (isError) {
    return (
      <Grid container spacing={6}>
        <Grid item xs={12}>
          <Alert severity='error'>Failed to load invoice details. Please try again later.</Alert>
        </Grid>
      </Grid>
    )
  }

  return <MissionExpenseReport data={data} />
}

SingleMissionExpensePage.getLayout = (page: React.ReactNode) => <BlankLayout>{page}</BlankLayout>

export default SingleMissionExpensePage
