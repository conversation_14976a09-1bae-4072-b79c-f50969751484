import { Box } from '@mui/material'
import React from 'react'
import BackButton from 'src/@core/components/back-button'
import { usePermission } from 'src/@core/hooks/usePermission'
import { MODULES } from 'src/layouts/components/acl/can'
import InvoiceGrid from '../../../@core/tables/invoice'

export default function InvoicePage() {
  const { ACTIONS, can, getModulePrivileges, getModelPrivileges } = usePermission()

  // Get all privileges for financial module
  const financialPrivileges = getModulePrivileges(MODULES.FINANCIAL)
  console.log('Financial module privileges:', financialPrivileges)

  // Get specific model privileges
  const invoicePrivileges = getModelPrivileges(MODULES.FINANCIAL, 'missionexpenses')
  console.log('missionexpenses model privileges:', invoicePrivileges)

  // Check specific permission
  const canReadInvoice = can(MODULES.FINANCIAL, 'invoice', ACTIONS.READ)

  return (
    <Box>
      <BackButton />
      <InvoiceGrid />
    </Box>
  )
}
