import { ReactNode } from 'react'
import BlankLayout from 'src/@core/layouts/BlankLayout'
import { useRouter } from 'next/router'
import PrintPage from 'src/views/print/invoice'

const InvoicePrint = () => {
  const router = useRouter()
  const { id } = router.query

  return <PrintPage id={id as string} />
}

InvoicePrint.getLayout = (page: ReactNode) => <BlankLayout>{page}</BlankLayout>

InvoicePrint.setConfig = () => {
  return {
    mode: 'light'
  }
}

export default InvoicePrint
