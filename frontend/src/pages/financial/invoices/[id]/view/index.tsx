import { useRouter } from 'next/router'
import {
  Alert,
  Box,
  <PERSON>ton,
  Card,
  CardContent,
  CardHeader,
  Chip,
  Divider,
  Grid,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  CircularProgress,
  Typography
} from '@mui/material'
import { useInvoiceById } from 'src/hooks/useInvoice'
import Icon from 'src/@core/components/icon'
import { formateStringDate } from 'src/@core/utils/format'
import { DetailRow } from 'src/@core/shared/DetailRow'
import BackButton from 'src/@core/components/back-button'
import moment from 'moment'
import { da } from 'date-fns/locale'
import { cd } from '@fullcalendar/core/internal-common'

const tableCellStyles = {
  whiteSpace: 'nowrap',
  overflow: 'hidden',
  textOverflow: 'ellipsis',
  maxWidth: '200px' // adjust this value based on your needs
}

const getStatusColor = (status: string) => {
  switch (status.toUpperCase()) {
    case 'PAYEE':
      return 'success'
    case 'EN_ATTENTE':
      return 'warning'
    case 'ANNULEE':
      return 'error'
    default:
      return 'default'
  }
}

export default function InvoiceDetail() {
  const router = useRouter()
  const { id } = router.query
  const { data, isLoading, isError } = useInvoiceById(id as string)

  console.log(data)

  if (!id || typeof id !== 'string') {
    return (
      <Grid container spacing={6}>
        <Grid item xs={12}>
          <Alert severity='error'>Invalid invoice ID</Alert>
        </Grid>
      </Grid>
    )
  }

  if (isLoading) {
    return (
      <Box sx={{ mt: 6, display: 'flex', alignItems: 'center', flexDirection: 'column' }}>
        <CircularProgress sx={{ mb: 4 }} />
      </Box>
    )
  }

  if (isError) {
    return (
      <Grid container spacing={6}>
        <Grid item xs={12}>
          <Alert severity='error'>Failed to load invoice details. Please try again later.</Alert>
        </Grid>
      </Grid>
    )
  }

  return (
    <Grid container spacing={6}>
      {/* Header Actions */}
      <Grid item xs={12}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <BackButton />
          <Box sx={{ display: 'flex', gap: 2 }}>
            <Button
              variant='contained'
              color='primary'
              startIcon={<Icon icon='mdi:printer' />}
              onClick={() => router.push(`/financial/invoices/${id}/print`)}
            >
              Print
            </Button>
          </Box>
        </Box>
      </Grid>

      {/* Details card */}
      <Grid item xs={12}>
        <Card sx={{ mb: 4 }}>
          <CardContent>
            <Typography>
              Facture: {data?.customer.sap_uid}({data?.customer.name})
            </Typography>
            <Typography>{moment(data?.created_at).format('LL')}</Typography>
          </CardContent>
        </Card>
        <Card>
          <CardHeader title='Invoice Details' />
          <CardContent>
            <Grid container spacing={4} style={{ alignItems: 'center' }}>
              {/* Left Column */}
              <Grid item xs={12} md={6}>
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                  <Typography variant='h6' sx={{ mb: 2 }}>
                    Invoice Information
                  </Typography>

                  <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                    <Typography>
                      N° Pièce:
                      <Box component='span' sx={{ fontWeight: 'bold', mr: 1 }}>
                        {data?.customer.sap_uid} ({data?.customer.name})
                      </Box>
                    </Typography>

                    <Typography>
                      Référence:
                      <Box component='span' sx={{ fontWeight: 'bold', mr: 1 }}>
                        {data?.code}
                      </Box>
                    </Typography>

                    <Typography>
                      Réf. Dossier:
                      <Box component='span' sx={{ fontWeight: 'bold', mr: 1 }}>
                        {data?.order.code}
                      </Box>
                    </Typography>
                  </Box>
                </Box>
              </Grid>

              {/* Right Column */}
              <Grid item xs={12} md={6}>
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                  <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                    <Typography>
                      Agence/Station:
                      <Box component='span' sx={{ fontWeight: 'bold', mr: 1 }}>
                        {data?.agence}
                      </Box>
                    </Typography>

                    <Box component='span' sx={{ fontWeight: 'bold', mr: 1 }}>
                      {data?.customer.address}
                    </Box>

                    <Box component='span' sx={{ fontWeight: 'bold', mr: 1 }}>
                      {data?.customer.lictradnum}
                    </Box>

                    <Box component='span' sx={{ fontWeight: 'bold', mr: 1 }}>
                      {data?.customer.addid}
                    </Box>

                    <Box component='span' sx={{ fontWeight: 'bold', mr: 1 }}>
                      {data?.customer.mailaddres}
                    </Box>

                    <Box component='span' sx={{ fontWeight: 'bold', mr: 1 }}>
                      {moment(data?.created_at).format('LL')}
                    </Box>
                  </Box>
                </Box>
              </Grid>
            </Grid>

            {/* Invoice Lines Table */}
            <Grid item xs={12} sx={{ mt: 8 }}>
              {data?.invoice_lines.length === 0 ? (
                <Alert severity='info'>No invoice lines available</Alert>
              ) : (
                <Box sx={{ overflowX: 'auto' }}>
                  <Table sx={{ minWidth: 800 }}>
                    <TableHead>
                      <TableRow>
                        <TableCell sx={{ ...tableCellStyles, fontWeight: 'bold' }}>Truck</TableCell>
                        <TableCell sx={{ ...tableCellStyles, fontWeight: 'bold' }}>BE Number</TableCell>
                        <TableCell sx={{ ...tableCellStyles, fontWeight: 'bold' }}>BC Number</TableCell>
                        <TableCell sx={{ ...tableCellStyles, fontWeight: 'bold' }}>Destination</TableCell>
                        <TableCell sx={{ ...tableCellStyles, fontWeight: 'bold' }}>Product</TableCell>
                        <TableCell sx={{ ...tableCellStyles, fontWeight: 'bold' }}>Price</TableCell>
                        <TableCell sx={{ ...tableCellStyles, fontWeight: 'bold' }}>Quantity</TableCell>
                        <TableCell sx={{ ...tableCellStyles, fontWeight: 'bold' }}>Amount HT</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {data?.invoice_lines.map((line, index) => (
                        <TableRow key={index}>
                          <TableCell sx={tableCellStyles}>{line.truck}</TableCell>
                          <TableCell sx={tableCellStyles}>{line.be_number}</TableCell>
                          <TableCell sx={tableCellStyles}>{line.bc_number}</TableCell>
                          <TableCell sx={tableCellStyles}>{line.destination}</TableCell>
                          <TableCell sx={tableCellStyles}>{line.product}</TableCell>
                          <TableCell sx={tableCellStyles}>{line.price}</TableCell>
                          <TableCell sx={tableCellStyles}>{line.qty}</TableCell>
                          <TableCell sx={tableCellStyles}>{line.amount_ht}</TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </Box>
              )}
            </Grid>
          </CardContent>
        </Card>
      </Grid>
    </Grid>
  )
}
