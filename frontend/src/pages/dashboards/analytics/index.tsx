import { useState } from 'react'
import { useTranslation } from 'react-i18next'
import { useSelector } from 'react-redux'
import { selectUser } from 'src/store/auth'
import { Box, Tab, Tabs, Typography } from '@mui/material'
import { TabContext, TabPanel } from '@mui/lab'

import { Icon } from '@iconify/react'
import OrdrersAnalytics from 'src/@core/shared/analytics/orders'
import MissionExpensesAnalytics from 'src/@core/shared/analytics/mission-expenses'
import InvoiceAnalytics from 'src/@core/shared/analytics/invoice'
import TravelAnalytics from 'src/@core/shared/analytics/travel'

function AnalyticsPage() {
  const user = useSelector(selectUser)
  const { t } = useTranslation()
  const [tabValue, setTabValue] = useState('0')

  const handleTabChange = (event: React.SyntheticEvent, newValue: string) => {
    setTabValue(newValue)
  }

  return (
    <Box>
      <Typography variant='h5' sx={{ mb: 4 }}>
        <Typography variant='h4' fontWeight={'bold'}>
          Welcome {user?.first_name} {user?.last_name}!
        </Typography>
        <Typography variant='body1' color={'primary.main'}>
          {user?.is_superuser ? 'Admin' : user?.type_of_operation ? user?.type_of_operation : 'Staff'}
        </Typography>
      </Typography>
      <TabContext value={tabValue}>
        <Box>
          <Tabs
            value={tabValue}
            onChange={handleTabChange}
            TabIndicatorProps={{ style: { display: 'none' } }}
            sx={{
              '& .MuiTab-root': {
                // minHeight: '64px',
                borderBottom: 'none !important',
                textTransform: 'none',
                '&.Mui-selected': {
                  color: 'primary.main',
                  backgroundColor: 'rgba(0, 0, 0, 0.04)',
                  borderRadius: '8px'
                }
              }
            }}
          >
            <Tab
              icon={<Icon icon='tabler:shopping-cart' />}
              iconPosition='start'
              label={t('Orders Analytics')}
              value='0'
            />
            <Tab
              icon={<Icon icon='tabler:currency-dollar' />}
              iconPosition='start'
              label={t('Mission Expense')}
              value='1'
            />
            <Tab icon={<Icon icon='tabler:file-invoice' />} iconPosition='start' label={t('Invoices')} value='2' />
            <Tab icon={<Icon icon='tabler:truck-delivery' />} iconPosition='start' label={t('Travel')} value='3' />
          </Tabs>
        </Box>
        <TabPanel value='0'>
          <OrdrersAnalytics />
        </TabPanel>
        <TabPanel value='1'>
          <MissionExpensesAnalytics />
        </TabPanel>
        <TabPanel value='2'>
          <InvoiceAnalytics />
        </TabPanel>
        <TabPanel value='3'>
          <TravelAnalytics />
        </TabPanel>
      </TabContext>
    </Box>
  )
}

export default AnalyticsPage
