// ** React Imports
import { ReactNode, useState } from 'react'

// ** Next Imports
import Head from 'next/head'
import { Router } from 'next/router'
import type { NextPage } from 'next'
import type { AppProps } from 'next/app'

// ** Store Imports
import { store } from 'src/store'
import { Provider } from 'react-redux'

// ** Loader Import
import NProgress from 'nprogress'

// ** Emotion Imports
import { CacheProvider } from '@emotion/react'
import type { EmotionCache } from '@emotion/cache'

// ** Config Imports
import 'src/configs/i18n'
import { defaultACLObj } from 'src/configs/acl'
import themeConfig from 'src/configs/themeConfig'

// ** Third Party Import
import { Toaster } from 'react-hot-toast'
import { useTheme } from '@mui/material/styles'

// ** Component Imports
import UserLayout from 'src/layouts/UserLayout'
import AclGuard from 'src/@core/components/auth/AclGuard'
import ThemeComponent from 'src/@core/theme/ThemeComponent'
import AuthGuard from 'src/@core/components/auth/AuthGuard'
import GuestGuard from 'src/@core/components/auth/GuestGuard'

// ** Spinner Import
import Spinner from 'src/@core/components/spinner'

// ** Contexts
import { AuthProvider } from 'src/context/AuthContext'
import { SettingsConsumer, SettingsProvider } from 'src/@core/context/settingsContext'
import { AbilityProvider } from 'src/@core/context/ability-provider'

// ** Styled Components
import ReactHotToast from 'src/@core/styles/libs/react-hot-toast'

// ** Utils Imports
import { createEmotionCache } from 'src/@core/utils/create-emotion-cache'

// ** Prismjs Styles
import 'prismjs'
import 'prismjs/themes/prism-tomorrow.css'
import 'prismjs/components/prism-jsx'
import 'prismjs/components/prism-tsx'

// ** React Perfect Scrollbar Style
import 'react-perfect-scrollbar/dist/css/styles.css'

// ** Global css styles
import '../../styles/globals.css'

// ** React Query
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'

// ** Types
type ExtendedAppProps = AppProps & {
  Component: NextPage & {
    getLayout?: (page: ReactNode) => ReactNode
    contentHeightFixed?: boolean
    authGuard?: boolean
    guestGuard?: boolean
    setConfig?: () => void
    acl?: {
      action: string
      subject: string
    }
  }
  emotionCache: EmotionCache
}

type GuardProps = {
  authGuard: boolean
  guestGuard: boolean
  children: ReactNode
}

const clientSideEmotionCache = createEmotionCache()

// ** Pace Loader
if (themeConfig.routingLoader) {
  Router.events.on('routeChangeStart', () => {
    NProgress.start()
  })
  Router.events.on('routeChangeError', () => {
    NProgress.done()
  })
  Router.events.on('routeChangeComplete', () => {
    NProgress.done()
  })
}

const Guard = ({ children, authGuard, guestGuard }: GuardProps) => {
  if (guestGuard) {
    return <GuestGuard fallback={<Spinner />}>{children}</GuestGuard>
  } else if (!guestGuard && !authGuard) {
    return <>{children}</>
  } else {
    return <AuthGuard fallback={<Spinner />}>{children}</AuthGuard>
  }
}

// ** Configure JSS & ClassName
const App = (props: ExtendedAppProps) => {
  const theme = useTheme()
  const { Component, emotionCache = clientSideEmotionCache, pageProps } = props
  const [queryClient] = useState(
    () =>
      new QueryClient({
        defaultOptions: {
          queries: {
            staleTime: 1000 * 60 * 5
          }
        }
      })
  )

  // Variables
  const contentHeightFixed = Component.contentHeightFixed ?? false
  const getLayout =
    Component.getLayout ?? (page => <UserLayout contentHeightFixed={contentHeightFixed}>{page}</UserLayout>)
  const setConfig = Component.setConfig ?? undefined
  const authGuard = Component.authGuard ?? true
  const guestGuard = Component.guestGuard ?? false
  const aclAbilities = Component.acl ?? defaultACLObj

  return (
    <Provider store={store}>
      <CacheProvider value={emotionCache}>
        <Head>
          <title>{`${themeConfig.templateName}`}</title>
          <meta name='description' content={`${themeConfig.templateName} `} />
          <meta name='keywords' content='winlog' />
          <meta name='viewport' content='initial-scale=1, width=device-width' />
        </Head>

        <QueryClientProvider client={queryClient}>
          <AuthProvider>
            <AbilityProvider>
              <SettingsProvider {...(setConfig ? { pageSettings: setConfig() } : {})}>
                <SettingsConsumer>
                  {({ settings }) => {
                    return (
                      <ThemeComponent settings={settings}>
                        <Guard authGuard={authGuard} guestGuard={guestGuard}>
                          {/* <AclGuard aclAbilities={aclAbilities} guestGuard={guestGuard} authGuard={authGuard}> */}
                          {getLayout(<Component {...pageProps} />)}
                          {/* </AclGuard> */}
                        </Guard>
                        <ReactHotToast>
                          <Toaster
                            position={'bottom-center'}
                            containerStyle={{
                              // Ensure toasts appear above everything else, including MUI dialogs
                              zIndex: theme.zIndex.tooltip + 1000, // MUI tooltip is usually highest at 1500
                              position: 'fixed'
                            }}
                            toastOptions={{
                              style: {
                                background: theme.palette.background.paper,
                                color: theme.palette.text.primary,
                                border: `1px solid ${theme.palette.divider}`,
                                padding: theme.spacing(2),
                                borderRadius: theme.shape.borderRadius,
                                boxShadow: `0 4px 8px ${
                                  theme.palette.mode === 'light' ? 'rgba(0, 0, 0, 0.1)' : 'rgba(255, 255, 255, 0.1)'
                                }`,
                                fontFamily: theme.typography.fontFamily
                              },

                              success: {
                                style: {
                                  borderLeft: `4px solid ${theme.palette.success.main}`
                                },
                                iconTheme: {
                                  primary: theme.palette.success.main,
                                  secondary: theme.palette.success.contrastText
                                }
                              },
                              error: {
                                style: {
                                  borderLeft: `4px solid ${theme.palette.error.main}`
                                },
                                iconTheme: {
                                  primary: theme.palette.error.main,
                                  secondary: theme.palette.error.contrastText
                                }
                              },
                              loading: {
                                style: {
                                  borderLeft: `4px solid ${theme.palette.info.main}`
                                }
                              },
                              duration: 8000
                            }}
                          />
                        </ReactHotToast>
                      </ThemeComponent>
                    )
                  }}
                </SettingsConsumer>
              </SettingsProvider>
            </AbilityProvider>
          </AuthProvider>
        </QueryClientProvider>
      </CacheProvider>
    </Provider>
  )
}

export default App
