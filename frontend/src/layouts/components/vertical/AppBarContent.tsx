// ** MUI Imports
import Box from '@mui/material/Box'
import IconButton from '@mui/material/IconButton'

// ** Icon Imports
import Icon from 'src/@core/components/icon'

// ** Type Import
import { Settings } from 'src/@core/context/settingsContext'

// ** Components
import <PERSON>complete from 'src/layouts/components/Autocomplete'
import <PERSON><PERSON><PERSON><PERSON> from 'src/@core/layouts/components/shared-components/ModeToggler'
import UserDropdown from 'src/@core/layouts/components/shared-components/UserDropdown'
import LanguageDropdown from 'src/@core/layouts/components/shared-components/LanguageDropdown'
import NotificationDropdown, {
  NotificationsType
} from 'src/@core/layouts/components/shared-components/NotificationDropdown'
import ShortcutsDropdown, { ShortcutsType } from 'src/@core/layouts/components/shared-components/ShortcutsDropdown'

// ** Hook Import
import { useAuth } from 'src/hooks/useAuth'
import { useSelector } from 'react-redux'
import { selectUser } from 'src/store/auth'
import { MODELS, MODULES } from '../acl/can'

interface Props {
  hidden: boolean
  settings: Settings
  toggleNavVisibility: () => void
  saveSettings: (values: Settings) => void
}

const notifications: NotificationsType[] = [
  {
    meta: 'Today',
    avatarAlt: 'Flora',
    title: 'Congratulation Flora! 🎉',
    avatarImg: '/images/avatars/4.png',
    subtitle: 'Won the monthly best seller badge'
  },
  {
    meta: 'Yesterday',
    avatarColor: 'primary',
    subtitle: '5 hours ago',
    avatarText: 'Robert Austin',
    title: 'New user registered.'
  },
  {
    meta: '11 Aug',
    avatarAlt: 'message',
    title: 'New message received 👋🏻',
    avatarImg: '/images/avatars/5.png',
    subtitle: 'You have 10 unread messages'
  },
  {
    meta: '25 May',
    title: 'Paypal',
    avatarAlt: 'paypal',
    subtitle: 'Received Payment',
    avatarImg: '/images/misc/paypal.png'
  },
  {
    meta: '19 Mar',
    avatarAlt: 'order',
    title: 'Received Order 📦',
    avatarImg: '/images/avatars/3.png',
    subtitle: 'New order received from John'
  },
  {
    meta: '27 Dec',
    avatarAlt: 'chart',
    subtitle: '25 hrs ago',
    avatarImg: '/images/misc/chart.png',
    title: 'Finance report has been generated'
  }
]

const shortcuts: ShortcutsType[] = [
  {
    title: 'Vehicles Management',
    url: '/vehicles',
    icon: 'tabler:car',
    subtitle: 'Manage Vehicles and Trucks',
    module: MODULES.VEHICLE,
    model: MODELS[MODULES.VEHICLE].VEHICLE
  },
  {
    title: 'Services',
    url: '/services/travel',
    icon: 'tabler:truck-delivery',
    subtitle: 'Manage services operations ',
    module: MODULES.SERVICES,
    model: MODELS[MODULES.SERVICES].TRAVEL
  },
  {
    title: 'Financial',
    icon: 'tabler:currency-dollar',
    url: '/financial/invoices',
    subtitle: 'Manage Financial Operations',
    module: MODULES.FINANCIAL,
    model: MODELS[MODULES.FINANCIAL].INVOICE
  },
  {
    url: '/user-management/permissions',
    icon: 'tabler:lock',
    subtitle: 'Permissions',
    title: 'Role Management',
    model: MODULES.SETTINGS,
    module: MODELS[MODULES.AUTH].PERMISSION
  },
  {
    subtitle: 'CRM',
    title: 'Dashboard',
    url: '/dashboards/crm',
    icon: 'tabler:device-analytics'
  },
  {
    title: 'Settings',
    icon: 'tabler:settings',
    subtitle: 'Account Settings',
    url: '/settings/countries',
    model: MODULES.SETTINGS,
    module: MODELS[MODULES.SETTINGS].COUNTRY
  },
  {
    icon: 'tabler:help',
    title: 'Help Center',
    url: '/pages/help-center',
    subtitle: 'FAQs & Articles'
  },
  {
    title: 'Fuel consumption',
    icon: 'tabler:gas-station',
    subtitle: 'Fuel consumption',
    url: '/fuel/consumption',
    model: MODULES.FUEL_CONSUMPTION,
    module: MODELS[MODULES.FUEL_CONSUMPTION].FUEL_CONSUMPTION
  }
]

const AppBarContent = (props: Props) => {
  // ** Props
  const { hidden, settings, saveSettings, toggleNavVisibility } = props

  // ** Hook
  const user = useSelector(selectUser)

  return (
    <Box sx={{ width: '100%', display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
      <Box className='actions-left' sx={{ mr: 2, display: 'flex', alignItems: 'center' }}>
        {hidden && !settings.navHidden ? (
          <IconButton color='inherit' sx={{ ml: -2.75 }} onClick={toggleNavVisibility}>
            <Icon fontSize='1.5rem' icon='tabler:menu-2' />
          </IconButton>
        ) : null}
        {user && <Autocomplete hidden={hidden} settings={settings} />}
      </Box>
      <Box className='actions-right' sx={{ display: 'flex', alignItems: 'center' }}>
        <LanguageDropdown settings={settings} />
        <ModeToggler settings={settings} saveSettings={saveSettings} />
        {user && (
          <>
            <ShortcutsDropdown settings={settings} shortcuts={shortcuts} />
            <NotificationDropdown settings={settings} notifications={notifications} />
            <UserDropdown settings={settings} />
          </>
        )}
      </Box>
    </Box>
  )
}

export default AppBarContent
