// ** React Imports
import { ReactNode } from 'react'
import { NavGroup, NavLink } from 'src/@core/layouts/types'
import { can } from './can'
import { useSelector } from 'react-redux'
import { selectUser } from 'src/store/auth'

interface Props {
  navGroup?: NavGroup
  children: ReactNode
}

const CanViewNavGroup = (props: Props) => {
  const { children, navGroup } = props
  const user = useSelector(selectUser)

  const checkForVisibleChild = (arr: NavLink[] | NavGroup[]): boolean => {
    return arr.some((i: NavGroup) => {
      if (i.children) {
        return checkForVisibleChild(i.children)
      } else {
        return can(user, i.module || '', i.model || '', i.action || '')
      }
    })
  }

  const canViewMenuGroup = (item: NavGroup) => {
    const hasAnyVisibleChild = item.children && checkForVisibleChild(item.children)

    if (!(item.module && item.model && item.action)) {
      return hasAnyVisibleChild
    }

    return can(user, item.module, item.model, item.action) && hasAnyVisibleChild
  }

  if (
    navGroup &&
    (navGroup.auth === false ||
      (user?.user_permissions &&
        user.user_permissions[navGroup.module || '']?.some(p => p.permission.includes('update'))))
  ) {
    return <>{children}</>
  } else {
    return navGroup && canViewMenuGroup(navGroup) ? <>{children}</> : null
  }
}

export default CanViewNavGroup
