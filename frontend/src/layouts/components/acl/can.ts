import { User } from 'src/@core/types/auth'

export const MODULES = {
  FINANCIAL: 'financial',
  FUEL_CONSUMPTION: 'fuelconsumption',
  NOTIFICATION: 'notification',
  SERVICES: 'services',
  SETTINGS: 'settings',
  VEHICLE: 'vehicle',
  AUTH: 'auth',
  APP_AUTHENTICATION: 'apps_authentication'
} as const

export const MODELS = {
  [MODULES.FINANCIAL]: {
    INVOICE: 'invoice',
    MISSION_EXPENSES: 'missionexpenses',
    MISSION_EXPENSES_OTHER: 'missionexpensesother'
  },
  [MODULES.FUEL_CONSUMPTION]: {
    // CONSUMPTION_OPERATION_LINE: 'consumptionoperationline',
    STATION: 'station',
    FUEL_CONSUMPTION: 'fuelconsumption'
  },
  [MODULES.NOTIFICATION]: {
    NOTIFICATION: 'notification'
  },
  [MODULES.SERVICES]: {
    ARTICLE: 'article',
    EXTRACTION: 'extraction',
    IMMOBILIZATION: 'immobilzation',
    INVOICE_ITEMS: 'invoiceitems',
    ORDER: 'order',
    PRICING_BENNE_PER_ROUTE: 'pricingbenneperroute',
    PRICING_ROUTE: 'pricingroute',
    PRODUCT: 'product',
    TRAVEL: 'travel',
    NATURE_PRODUCT: 'natureofarticles',
    GROUP_ARTICLE: 'group_article'
  },
  [MODULES.SETTINGS]: {
    COUNTRY: 'country',
    DESTINATIONS: 'destinations',
    PRICING_ROUTE: 'pricingroute',
    ROUTE: 'route'
  },
  [MODULES.VEHICLE]: {
    TRUCK: 'truck',
    VEHICLE: 'vehicle'
  },
  [MODULES.APP_AUTHENTICATION]: {
    USER_DEPARTMENT: 'userpsl',
    AGENCY: 'agency',
    CUSTOMER: 'customer',
    DRIVER: 'driver',
    VALIDATIONS: 'datavalidation',
    USERS: 'users'
  },
  [MODULES.AUTH]: {
    GROUP: 'group',
    PERMISSION: 'permission'
  }
} as const

export const ACTIONS = {
  CREATE: 'create',
  READ: 'read',
  UPDATE: 'update',
  DELETE: 'delete',
  VALIDATE: 'validate',
  ALERT: 'alert',
  EDIT: 'edit',
  STOP: 'stop',
  MANAGE: 'manage'
} as const

export const can = (user: User | null, module: string, model: string, action: string): boolean => {
  // Super admin check
  if (user?.is_superuser) {
    return true
  }

  // No user or no permissions
  if (!user?.user_permissions) {
    return false
  }

  // Get module permissions
  const modulePermissions = user.user_permissions[module]

  if (!modulePermissions) {
    return false
  }

  // Find model permissions
  const modelPermissions = modulePermissions.find(p => {
    return p.model === model
  })
  if (!modelPermissions) {
    return false
  }

  // If checking for READ permission and user has UPDATE, grant access
  if (action === ACTIONS.READ && modelPermissions.permission.includes(ACTIONS.UPDATE)) {
    return true
  }

  // Check if action is allowed
  return modelPermissions.permission.includes(action)
}

// Helper function to get all available models for a module
export const getAvailableModels = (user: User | null, module: string): string[] => {
  if (!user?.user_permissions || !user.user_permissions[module]) {
    return []
  }

  return user.user_permissions[module].map(p => p.model)
}

// Helper function to get all available actions for a model
export const getAvailableActions = (user: User | null, module: string, model: string): string[] => {
  if (!user?.user_permissions || !user.user_permissions[module]) {
    return []
  }

  const modelPermissions = user.user_permissions[module].find(p => p.model === model)
  if (!modelPermissions) return []

  const permissions = [...modelPermissions.permission]

  // If user has UPDATE permission, automatically include READ permission
  if (permissions.includes(ACTIONS.UPDATE) && !permissions.includes(ACTIONS.READ)) {
    permissions.push(ACTIONS.READ)
  }

  return permissions
}
