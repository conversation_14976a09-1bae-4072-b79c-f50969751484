// ** React Imports
import { ReactNode, useContext } from 'react'
import { useAbility } from 'src/@core/context/ability-provider'
import { can } from './can'

import { NavSectionTitle } from 'src/@core/layouts/types'
import { useSelector } from 'react-redux'
import { selectUser } from 'src/store/auth'

interface Props {
  children: ReactNode
  navTitle?: NavSectionTitle
}

const CanViewNavSectionTitle = (props: Props) => {
  // ** Props
  const { children, navTitle } = props

  // ** Hook
  const ability = useAbility()
  const user = useSelector(selectUser)

  if (navTitle && navTitle.auth === false) {
    return <>{children}</>
  } else {
    return ability && can(user, navTitle?.module || '', navTitle?.model || '', navTitle?.action || '') ? (
      <>{children}</>
    ) : null
  }
}

export default CanViewNavSectionTitle
