import { ReactNode } from 'react'
import { usePermission } from 'src/@core/hooks/usePermission'

interface CanProps {
  module: string
  model: string
  action: string | string[]
  fallback?: ReactNode
  matchAny?: boolean
  children: ReactNode
}

export const Can = ({ module, model, action, fallback, matchAny = false, children }: CanProps): JSX.Element => {
  const { can } = usePermission()

  const checkPermission = () => {
    if (Array.isArray(action)) {
      return matchAny ? action.some(act => can(module, model, act)) : action.every(act => can(module, model, act))
    }

    return can(module, model, action)
  }

  return checkPermission() ? <>{children}</> : <>{fallback}</> || null
}
