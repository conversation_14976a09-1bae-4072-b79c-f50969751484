// ** React Imports
import { ReactNode } from 'react'
import { useAuth } from 'src/hooks/useAuth'
import { NavLink } from 'src/@core/layouts/types'
import { can } from './can'
import { useSelector } from 'react-redux'
import { selectUser } from 'src/store/auth'

interface Props {
  navLink?: NavLink
  children: ReactNode
}

const CanViewNavLink = (props: Props) => {
  const { children, navLink } = props

  const user = useSelector(selectUser)

  // If auth is explicitly set to false, always render
  if (navLink && navLink?.auth === false) {
    return <>{children}</>
  }

  // If any required permission props are missing, render by default

  if (!navLink?.module || !navLink?.model || !navLink?.action) {
    return <>{children}</>
  }

  // Check if user has required permissions
  return can(user, navLink.module, navLink.model, navLink.action) ? <>{children}</> : null
}

export default CanViewNavLink
