import { createContext, useEffect, useState, type ReactNode, useCallback } from 'react'
import { useRouter } from 'next/navigation'
import type { AuthContextType, AuthState, LoginCredentials, Tokens } from 'src/types/auth/auth'
import { api, fetchUser, login, logout, refreshToken, setAuthHeader } from 'src/lib/api'
import { encrypt } from 'src/lib/secure-store'
import { clearUser, selectUser, setUser } from 'src/store/auth'
import { useDispatch, useSelector } from 'react-redux'

export const AuthContext = createContext<AuthContextType>({
  isInitializing: true,
  isLoggingIn: false,
  isLoggingOut: false,
  isAuthenticated: false,
  loginError: null,
  logoutError: null,
  login: () => Promise.resolve(),
  logout: () => Promise.resolve()
})

type Props = {
  children: ReactNode
}

const AuthProvider = ({ children }: Props) => {
  const user = useSelector(selectUser)
  const dispatch = useDispatch()

  const [authState, setAuthState] = useState<AuthState>({
    isInitializing: true,
    isLoggingIn: false,
    isAuthenticated: false,
    isLoggingOut: false,
    loginError: null,
    logoutError: null
  })
  const router = useRouter()
  useEffect(() => {
    const initAuth = async () => {
      const encryptedAccessToken = localStorage.getItem('access_token')
      if (encryptedAccessToken) {
        try {
          await setAuthHeader()
          setAuthState(prev => ({ ...prev, isAuthenticated: true, isInitializing: false }))
        } catch (error) {
          setAuthState(prev => ({ ...prev, isInitializing: false, loginError: 'Failed to initialize auth' }))
        }
      } else {
        setAuthState(prev => ({ ...prev, isInitializing: false }))
      }
    }
    initAuth()
    api.interceptors.response.use(
    response => response,
    async error => {
      const originalRequest = error.config
      if ((error.response?.status === 401 || error.response?.status === 403) && !originalRequest._retry) {
        originalRequest._retry = true
        try {
          await refreshAccessToken()

          return api(originalRequest)
        } catch (refreshError) {
          return Promise.reject(refreshError)
        }
      }

      return Promise.reject(error)
    }
  )
  }, [])

  const handleLogin = async (credentials: LoginCredentials) => {
    setAuthState(prev => ({ ...prev, isLoggingIn: true, loginError: null }))
    try {
      const tokens = await login(credentials)

      const encryptedAccessToken = encrypt(tokens.access_token)
      const encryptedRefreshToken = encrypt(tokens.refresh_token)
      localStorage.setItem('access_token', encryptedAccessToken)
      localStorage.setItem('refresh_token', encryptedRefreshToken)
      localStorage.setItem('windlog_firts_login', tokens.is_first_login)
      await setAuthHeader()
      const user = await fetchUser()
      dispatch(setUser(user))
      setAuthState(prev => ({ ...prev, user }))
      setAuthState(prev => ({
        ...prev,
        isAuthenticated: true,
        isLoggingIn: false,
        loginError: null
      }))
    } catch (error) {
      setAuthState(prev => ({
        ...prev,
        isLoggingIn: false,
        loginError: error instanceof Error ? error.message : 'Login failed'
      }))
    }
  }

  const handleLogout = useCallback(async () => {
    setAuthState(prev => ({ ...prev, isLoggingOut: true, logoutError: null }))
    try {
      localStorage.removeItem('access_token')
      localStorage.removeItem('refresh_token')
      delete api.defaults.headers.common['Authorization']
      dispatch(clearUser())
      setAuthState({
        isInitializing: false,
        isAuthenticated: false,
        isLoggingIn: false,
        isLoggingOut: false,
        loginError: null,
        logoutError: null
      })
      router.push('/login')
    } catch (error) {
      setAuthState(prev => ({
        ...prev,
        isLoggingOut: false,
        logoutError: error instanceof Error ? error.message : 'Logout failed'
      }))
    }
  }, [dispatch, router])

async function refreshAccessToken() {
    try {
      const tokens: Tokens = await refreshToken()
      const encryptedAccessToken = encrypt(tokens.access_token)
      const encryptedRefreshToken = encrypt(tokens.refresh_token)
      localStorage.setItem('access_token', encryptedAccessToken)
      localStorage.setItem('refresh_token', encryptedRefreshToken)
      await setAuthHeader()

      return tokens.access_token
    } catch (error) {
      handleLogout()
      throw error
    }
  }

 
  const contextValue: AuthContextType = {
    ...authState,
    login: handleLogin,
    logout: handleLogout
  }

  return <AuthContext.Provider value={contextValue}>{children}</AuthContext.Provider>
}

export { AuthProvider }
