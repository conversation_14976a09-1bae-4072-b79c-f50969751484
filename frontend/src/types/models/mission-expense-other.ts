export interface MissionExpenseOtherParams {
  limit: number
  offset: number
  search?: string
}

export interface MissionExpenseOtherFormData {
  id?: string
  priority: string
  expenseLines?: MissionExpenseOtherLineOperation[]
  validation?: string
  validationReason?: string
}

export interface MissionExpenseOtherLineOperation {
  id?: string
  _id?: string
  designation?: string
  date?: string
  duration?: string
  price?: number
  quantity?: number
  amount?: number
  operations?: Array<any>
}

export interface MissionExpensesOthers {
  id: string
  code: string
  priority: string
  validation_info: Validationinfo
  validation: Validation[]
  mission_other_lines: Missionotherline[]
  total_amount: string
  validade: boolean
  created_at: string
  modified_at: string
  created_by: User
  modified_by: User
}

interface Missionotherline {
  id: string
  application: string
  order: number
  operations: Operation[]
  description: string
  getting_date: null
  duration: number
  validity_date: null
  fees: string
  qty: number
  amount: string
  created_at: string
  modified_at: string
  created_by: User
  modified_by: User
}

interface Operation {
  id: string
  modified_by: null
  tractor: Tractor
  trailer: Tractor
  path: Path
  agence: Path
  driver: User
  customer: Path
  order_code: string
  terminer: string
  direction: string
  be_number: string
  bc_number: string
  price: string
  qty: string
  qty_end: string
  amount_ht: string
  road_fees: string
  petrol_volume: number
  billed: boolean
  has_issue: boolean
  has_notification: boolean
  issue_datas: Issuedatas
  validation: string
  created_at: string
  modified_at: string
  product: number
  nature_product: null
  order: number
}

interface Issuedatas {
  [key: string]: string
}

interface Path {
  id: string
  name: string
}

interface Tractor {
  id: string
  number_plate: string
}

interface Validation {
  user: User
  validation: string
  created_at: string
  raison: string
}

interface User {
  id: string
  first_name: string
  last_name: string
}

interface Validationinfo {
  validation_count: number
  total_validations_needed: number
  next_validator: Nextvalidator | null
}

interface Nextvalidator {
  id: string
  first_name: string
  last_name: string
}
