export interface DriverFormData {
  matricule: string
  fonction: string
  dated_embauche_societe: string
  sexe: string
  date_de_naissance: string
  first_name: string
  last_name: string
  address: string
  phone_number: string
}

export interface DriverRequest {
  name: string
  phone: string
  is_active: boolean
  is_archived: boolean
}
export interface DriverResponse {
  count: number
  next: string
  previous: null
  results: Driver[]
}

export interface FetchDriverParams {
  offset: number
  limit: number
  search?: string
  id?: string
}

export interface Driver {
  id: string
  matricule: string
  fonction: string
  dated_embauche_societe: string
  sexe: string
  date_de_naissance: string
  first_name: string
  last_name: string
  address: string
  phone_number: string
  created_at: string
  modified_at: string
  created_by: null
  modified_by: null
}
