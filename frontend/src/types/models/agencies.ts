import { BaseFetchParams } from 'src/types/models'

export interface FetchAgencyParam extends BaseFetchParams {
  search?: string
  company?: string
}

export interface AgenciesResponse {
  count: number
  next: null
  previous: null
  results: Agencies[]
}

export interface Agencies {
  id: string
  name: string
  address: null | string
  company: Company
  is_active: boolean
  created_at: string
  modified_at: string
  created_by: Createdby | null
  modified_by: Createdby | null
}

interface Createdby {
  id: string
  first_name: string
  last_name: string
}

interface Company {
  id: string
  sap_uid: string
  name: string
}

export interface AgencyRequestPayload {
  name: string
  address: string | null
  company: string
  is_active: boolean
}
