// export interface Route {
//   id: string
//   name: string
//   origin: string
//   destination: string
//   distance?: number
//   estimated_time?: number
// }

export interface FetchRoutesParams {
  limit: number
  offset: number
  search?: string
}

export interface RoutesResponse {
  count: number
  next: string
  previous: null
  results: Route[]
}

export interface Route {
  id: string
  departure: string
  destination: string
  created_by: null
  modified_by: null
  name: string
  delay: number
  distance: number
  petrol_volume: number
  fees: string
  is_active: boolean
  is_city: boolean
  nbr_ligne: number
  special_consumption: boolean
  special_value: string
  archive: boolean
  created_at: string
  modified_at: string
  pricing: number[]
}

export interface CreateRoutePayload {
  departure_id: string
  destination_id: string
  delay: number
  distance: number
  petrol_volume: number
  fees: string
  is_active: boolean
  is_city: boolean
  nbr_ligne: number
  special_consumption: boolean
  special_value: string
  archive: boolean

  pricing_list: {
    good_type_id: string
    unite_price: string
  }[]
}
