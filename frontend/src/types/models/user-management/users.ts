export interface UserRequestPayload {
  username: string
  email: string
  first_name: string
  last_name: string
  password?: string
  language?: string
  is_active?: boolean
  confirm_password?: string
  is_staff?: boolean
  is_superuser?: boolean
  groups?: any
  user_permissions?: any
  supervisor_id?: string
  type_of_operation?: string
  other_phone?: string
  main_department_id?: string
  annex_departments_ids?: any
}

interface Maindepartment {
  id: string
  name: string
}
export interface PasswordPayload {
  new_password: string
  confirm_new_password: string
}

export interface UsersResponse {
  count: number
  next: null
  previous: null
  results: SingleUserResponse[]
}

export interface SingleUserResponse {
  id: string
  username: string
  email: string
  first_name: string
  last_name: string
  language: string
  picture: null
  is_active: boolean
  is_staff: boolean
  is_superuser: boolean
  last_login: null | string
  date_joined: string
  groups: any[]
  user_permissions: any[]
  supervisor: Supervisor | null
  type_of_operation: null | string
  other_phone: null | string
  main_department: Maindepartment | null
  annex_departments: any[]
}

export interface Supervisor {
  id: string
  first_name: string
  last_name: string
}
