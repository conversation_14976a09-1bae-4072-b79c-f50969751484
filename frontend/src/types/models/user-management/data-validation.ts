export interface DataValidationRequestPayload {
  level: number
  level_to_validate: number
  data_type: string
}

export interface DataValidationResponse {
  count: number
  next: null
  previous: null
  results: Validation[]
}

export interface Validation {
  id: string
  level: number
  level_to_validate: number
  data_type: string
  validateurs: Validateur[]
  created_at: string
  modified_at: string
  created_by: Validateur
  modified_by: Validateur
}

interface Validateur {
  id: string
  first_name: string
  last_name: string
}
