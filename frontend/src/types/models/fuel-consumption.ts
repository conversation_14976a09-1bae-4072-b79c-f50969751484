export interface FetchFuelConsumptionParams {
  limit: number
  offset: number
  search?: string
}

export interface FuelConsumption {
  id: string
  created_by: Createdby
  modified_by: Createdby
  operation: Operation
  repartitions: Repartition[]
  code: string
  type_conso: string
  validation: string
  created_at: string
  modified_at: string
}

interface Repartition {
  id: string
  station: string
  station_name: string
  station_localisation: string
  volume: number
  cash_card: string
  card_number: string
  created_at: string
}

interface Operation {
  id: string
  direction: string
  be_number: string
  bc_number: string
  price: string
  qty: string
  qty_end: string
  amount_ht: string
  road_fees: string
  petrol_volume: number
  billed: boolean
  has_issue: boolean
  has_notification: boolean
  issue_datas: Issuedatas
  validation: string
  created_at: string
  modified_at: string
  tractor: string
  trailer: null
  path: string
  product: number
  nature_product: null
  agence: string
  order: number
  driver: string
  modified_by: null
}

interface Issuedatas {
  [key: string]: string
}

interface Createdby {
  id: string
  first_name: string
  last_name: string
}

interface Createdby {
  id: string
  first_name: string
  last_name: string
}

export interface FuelConsumptionRequestPayload {
  operation: string | null
  type_conso: string
  validation: string
  volumeTotal: string
  code?: string
  distributions: Repartition[]
}
