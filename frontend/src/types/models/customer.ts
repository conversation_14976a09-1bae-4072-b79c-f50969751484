export interface CustomerResponse {
  count: number
  next: null
  previous: null
  results: Customer[]
}

export interface Customer {
  id: string
  name: string
  email?: string
  phone?: string
  address?: string
  city?: string
  country?: string
  postal_code?: string
  created_at: string
  updated_at: string
}

export interface CustomerRequestPayload {
  sap_uid: string
  name: string
  citerne_price_diff: string
  other_info: string
  address: string
  phone1: string
  phone2: string
  groupcode: number
  zipcode: string
  mailaddres: string
  cmpprivate: string
  mailzipcod: string
  addid: string
  currency: string
  cardtype: string
  lictradnum: string
}
export interface CustomerParams {
  search?: string
  limit?: number
  offset?: number
  ordering?: string
  [key: string]: any
}
