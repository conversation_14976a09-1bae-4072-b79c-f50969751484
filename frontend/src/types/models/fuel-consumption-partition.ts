export interface FuelConsumptionPartition {
  consumption: string
  station: string
  volume: number
  cash_card: 'Carte' | 'Sans carte'
  card_number?: string
}

interface Distribution {
  id?: string
  station: any
  station_name?: string
  station_localisation?: string
  volume: number
  _id?: string
  cash_card: string
  card_number: string
  created_at?: string
}

export interface ConsumptionFormData {
  operation: any
  type_conso: string
  validation: string
  volumeTotal: string
  code: string
  distributions: Distribution[]
}

export interface FetchFuelConsumptionPartitionParams {
  limit: number
  offset: number
  search?: string
  consumption?: string
}
