export interface FetchVehiclesParams {
  offset: number
  limit: number
  active?: boolean
  search?: string
  vehicle_type?: string
  vehicle_type_combo?: string
  container?: string
  id?: string
}

export interface VehicleRequest {
  code: string
  number_plate: string
  consumption_card_number: string
  vehicle_type: 'TRACTEUR' | 'REMORQUE' | 'CAMION' | 'PICKUP' | 'HEAD_OFFICE'
  container: string
  kilometer_old?: number
  kilometer_new?: number
  petrol_consumption: string
  long: string
  lat: string
  is_active: boolean
}

export interface VehicleResponse {
  count: number
  next: string
  previous: null
  results: Vehicle[]
}

export interface Vehicle {
  id: string
  created_by: null
  modified_by: null
  code: string
  number_plate: string
  consumption_card_number: string
  vehicle_type: string
  container: string
  kilometer_old: number
  kilometer_new: number
  petrol_consumption: string
  long: string
  lat: string
  is_active: boolean
  created_at: string
  modified_at: string
}
