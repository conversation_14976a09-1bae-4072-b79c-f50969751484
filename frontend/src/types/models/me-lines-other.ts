export interface Route {
  id: string | number
  name: string
}

export interface MissionExpenseLine {
  id: string
  route: Route
  client_agency: string
  be_number: string
  bc_number: string
  tractor?: string
  trailer?: string
  product_nature?: string
  fuel_volume?: string
  road_fees?: string
  driver?: string
  operation?: string
  direction?: string
  validation?: string
  quantity?: string
  unit_price?: string
  unloaded_quantity?: string
  product?: string
  order?: string
}

export interface MissionExpenseLineFormData {
  application: string
  operation: string[]
  description: string
  getting_date: string
  duration: number
  fees: number
  qty: number
}

export interface QueryParams {
  limit?: number
  offset?: number
  [key: string]: any
}
