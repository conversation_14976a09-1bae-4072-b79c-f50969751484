export interface FetchInvoiceParams {
  offset: number
  limit: number
  search?: string
}
interface User {
  id: string
  first_name: string
  last_name: string
}

interface Customer {
  id: string
  name: string
}

interface Order {
  id: number
  code: string
  validate: boolean
}

export interface Invoice {
  id: string
  code: string
  customer: Customer
  agence: string
  order: Order
  conditions: string
  echeance: string
  bank_ref: string
  account_number: string
  amount: string
  total_amount: string
  status: string
  created_at: string
  modified_at: string
  invoice_lines: Invoiceline[]
  created_by: Createdby
  modified_by: Createdby
}

interface Createdby {
  id: string
  first_name: string
  last_name: string
}

interface Invoiceline {
  id: string
  truck: string
  driver: null
  be_number: string
  bc_number: string
  destination: string
  product: string
  order: string
  price: string
  qty: number
  id_op: string
  amount_ht: string
  created_at: string
}

interface Order {
  id: number
  code: string
  validate: boolean
}

interface Customer {
  id: string
  sap_uid: string
  name: string
  citerne_price_diff: string
  other_info: string
  address: string
  phone1: string
  phone2: string
  groupcode: number
  zipcode: string
  mailaddres: string
  cmpprivate: string
  mailzipcod: string
  addid: string
  currency: string
  cardtype: string
  lictradnum: string
  created_at: string
  modified_at: string
  created_by: null
  modified_by: null
}
export interface InvoiceLine {
  truck: string
  be_number: string
  bc_number: string
  destination: string
  product: string
  price: string
  qty: number
  amount_ht: string
}

export interface CreateInvoiceData {
  conditions: string
  echeance: any
  bank_ref: string
  account_number: string
  amount: string
  total_amount: string
  invoice_lines: InvoiceLine[]
}
