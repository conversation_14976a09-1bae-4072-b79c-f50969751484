export interface Destination {
  id: string
  country: string
  created_by: Createdby
  modified_by: Createdby
  name: string
  long: string
  lat: string
  type: string
  is_active: boolean
  is_archived: boolean
  created_at: string
  modified_at: string
}

interface Createdby {
  id: string
  first_name: string
  last_name: string
}

export interface DestinationParams {
  limit: number
  offset: number
  search?: string
  id?: string
}

export interface DestinationFormData {
  name: string
  country?: string
  lat: string
  long: string
  type: 'NATIONAL' | 'INTERNATIONAL'
  is_active: boolean
  is_archived: boolean
}

export interface DestinationRequestPayload {
  name: string
  country?: string
  lat?: number
  type?: 'NATIONAL' | 'INTERNATIONAL'
  long?: number
  is_active?: boolean
}

export interface DestinationsResponse {
  count: number
  next: string | null
  previous: string | null
  results: Destination[]
}
