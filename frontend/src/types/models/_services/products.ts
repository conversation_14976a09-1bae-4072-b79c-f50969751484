export interface ProductParams {
  limit: number
  offset: number
  search?: string

  // product_type?: string
}
export interface ProductFormData {
  name: string
  service_id: string
}

export interface ProuductRequestPayload {
  name: string
  service_id: string
}

export interface ProductResponse {
  count: number
  next: null
  previous: null
  results: Product[]
}

export interface Product {
  id: number
  name: string
  service_info: Serviceinfo
  modified_at: string
  modified_by: Modifiedby
}

interface Modifiedby {
  id: string
  first_name: string
  last_name: string
}

interface Serviceinfo {
  id: number
  code: string
  label: string
}

export interface SingleProductResponse {
  id: number
  name: string
  service: Service
  modified_at: string
  modified_by: Modifiedby
  created_by: Modifiedby
}

interface Modifiedby {
  id: string
  first_name: string
  last_name: string
}

interface Service {
  id: number
  code: string
  label: string
  related_documents: any[]
  related_article: any[]
  group: Group
  operation_type: string
  measure_unit: string
  created_at: string
  modified_at: string
  created_by: null
  modified_by: null
}

interface Group {
  id: number
  name: string
}
