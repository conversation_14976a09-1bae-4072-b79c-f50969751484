export interface FetchPathParams {
  offset: number
  limit: number
  search?: string
}

export interface PathResponse {
  count: number
  next: string
  previous: string
  results: Path[]
}

export interface Path {
  id: string
  departure: string
  destination: string
  created_by: null
  modified_by: null
  name: string
  delay: number
  distance: number
  petrol_volume: number
  fees: string
  is_active: boolean
  is_city: boolean
  nbr_ligne: number
  special_consumption: boolean
  special_value: string
  archive: boolean
  created_at: string
  modified_at: string
  pricing: number[]
}
