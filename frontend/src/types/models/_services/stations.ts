export interface StationResponse {
  count: number
  next: null
  previous: null
  results: Station[]
}

export interface Station {
  id: string
  created_by: Createdby
  modified_by: Createdby
  station_name: string
  station_localisation: string
  email: string
  active: boolean
  created_at: string
  modified_at: string
}

interface Createdby {
  id: string
  first_name: string
  last_name: string
}

export interface FetchStationsParams {
  limit: number
  offset: number
  search?: string
}

export interface StationRequestPayload {
  station_name: string
  station_localisation: string
  email: string
  active: boolean
}
