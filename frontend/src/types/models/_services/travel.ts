import { DateType } from 'src/types/forms/reactDatepickerTypes'

export interface TravelParams {
  offset: number
  limit: number
  search?: string
}

export interface TravelFormData {
  order: string
  process: string
  duration: number
  started_date: Date
  ended_date: Date
  started_at: Date
  ended_at: Date
  charged_date: Date
  unloaded_date: Date
  charged_at: Date
  unloaded_at: Date
  is_round_trip: boolean
  immobilization: number[]
  blocking_duration: number
}
export interface Travel {
  id: number
  immobilization: any[]
  created_by: Createdby
  modified_by: Createdby
  process: Process
  trajet: string
  camion: string
  charged: boolean
  travel_type: string
  immo_duration: string
  immo: boolean
  duree: string
  date_estimee: string
  status: string
  retard: string
  order: Order
  code: string
  duration: number
  started_date: string
  ended_date: string
  started_at: string
  ended_at: string
  charged_date: string
  unloaded_date: string
  charged_at: string
  unloaded_at: string
  is_started: boolean
  is_round_trip: boolean
  is_ended: boolean
  is_canceled: boolean
  is_mobilised: boolean
  blocking_duration: number
  has_charged: boolean
  has_unloaded: boolean
  created_at: string
  modified_at: string
}

interface Process {
  id: string
  order: Order
  be_number: string
}

interface Order {
  code: string
}

interface Createdby {
  id: string
  first_name: string
  last_name: string
}

export interface TravelBasePayload extends Partial<Travel> {
  date: DateType
  time: DateType
  comment: string
}

export type TravelStartPayload = TravelBasePayload

export type TravelEndPayload = TravelBasePayload

export type TravelLoadPayload = TravelBasePayload

export type TravelUnloadPayload = TravelBasePayload

export interface TravelBlockingPayload extends TravelBasePayload {
  immobilization_type: string
}

export type TravelCancelPayload = TravelBasePayload
