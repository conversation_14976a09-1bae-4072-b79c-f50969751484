export interface FetchPricingRouteParams {
  offset: number
  limit: number
  search?: string
}
export interface PricingBenneFormData {
  route: string
  product: number
  customer: string
  unite_price: string
}
export interface PricingRoute {
  id: number
  route: Route
  good_type: Goodtype
  unite_price: string
}

interface Goodtype {
  id: number
  code: string
  label: string
}

interface Route {
  id: string
  name: string
}
