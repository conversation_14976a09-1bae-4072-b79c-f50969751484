export interface FetchArticlesParams {
  limit: number
  offset: number
  search?: string
  article_group?: string
  operation_type?: string
}

export interface ArticleFormData {
  code: string
  label: string
  related_documents: string[]
  related_articles: string[]
  operation_type: string
  measure_unit: string
  group?: number
}

export interface ArticlesResponse {
  count: number
  next: string
  previous: string
  results: Article[]
}

export interface Article {
  id: number
  code: string
  label: string
  related_documents: any[]
  related_article: any[]
  group: Group
  operation_type: string
  measure_unit: string
  created_at: string
  modified_at: string
  created_by: null
  modified_by: null
}

interface Group {
  id: number
  name: string
}
