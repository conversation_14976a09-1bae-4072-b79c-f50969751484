export interface Country {
  id: string
  name: string
  code: string
  phone_code?: string
  flag_url?: string
  is_active: boolean
}

export interface CountryFormData {
  name: string
  iso_2: string
  code: string
  is_active: boolean
}

export interface CountryParams {
  limit: number
  offset: number
  search?: string
}

export interface CountryRequestPayload {
  name: string
  code: string
  phone_code?: string
  flag_url?: string
  is_active?: boolean
}

export interface CountriesResponse {
  count: number
  results: Country[]
}
