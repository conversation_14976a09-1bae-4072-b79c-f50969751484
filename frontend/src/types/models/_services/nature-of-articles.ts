export interface FetchNatureOfArticlesParams {
  offset?: number
  limit?: number
  search?: string
}

export interface NatureOfArticleRequestPayload {
  name: string
}

export interface NatureOfArticleResponse {
  count: number
  next: null
  previous: null
  results: NatureOfArticle[]
}

export interface NatureOfArticle {
  id: number
  name: string
  created_at: string
  modified_at: string
  created_by: Createdby
  modified_by: Createdby
}

interface Createdby {
  id: string
  first_name: string
  last_name: string
}
