export interface OrderOperationLine {
  id?: string
  tractor_id: string | null
  trailer_id: string | null
  path_id: string | null
  direction: string
  be_number: string
  bc_number: string
  product_id: string | null
  nature_product_id: string | null
  agence_id: string | null
  qty: string
  road_fees: string
  petrol_volume: string
  driver_id: string | null
  validation: string
  price: string
  unloaded_quantity?: string
}

export interface OrderFormValues {
  customer: any
  prestation: string
  is_city: boolean
  operations: OrderOperationLine[]
  rejection_reason?: string
  validation_status?: string
}

export interface OrderParams {
  offset: number
  limit: number
  search?: string
  prestation?: string
}
export interface Order {
  id: number
  lines: Line[]
  customer: Path
  created_by: Driver
  modified_by: Driver
  validation: Driver[]
  code: string
  prestation: string
  is_city: boolean
  next_validator: Driver
  validate: boolean
  created_at: string
  modified_at: string
}

interface Line {
  id: string
  modified_by: null
  tractor: Tractor
  trailer: Tractor
  path: Path
  agence: Path
  driver: Driver
  customer: Path
  order_code: string
  terminer: string
  direction: string
  be_number: string
  bc_number: string
  price: string
  qty: string
  qty_end: string
  amount_ht: string
  road_fees: string
  petrol_volume: number
  billed: boolean
  has_issue: boolean
  has_notification: boolean
  issue_datas: Issuedatas
  validation: string
  created_at: string
  modified_at: string
  product: number
  nature_product: string | null
  order: number
}

interface Issuedatas {
  [key: string]: string
}

interface Driver {
  id: string
  first_name: string
  last_name: string
}

interface Path {
  id: string
  name: string
}

interface Tractor {
  id: string
  number_plate: string
}

export interface OrderRequestPayload {
  customer_id: string
  prestation: string
  is_city: boolean
  operations: OrderOperationLine[]
}

export interface OrderStats {
  earnings: string
  profit: string
  expense: string
  weekly_comparison: Weeklycomparison
  daily_comparison: Dailycomparison
}

interface Dailycomparison {
  Monday: Monday
  Tuesday: Monday
  Wednesday: Monday
  Thursday: Monday
  Friday: Monday
  Saturday: Monday
  Sunday: Monday
}

interface Monday {
  current_week: Currentweek2
  last_week: Currentweek2
}

interface Currentweek2 {
  earnings: string
}

interface Weeklycomparison {
  current_week: Currentweek
  last_week: Currentweek
}

interface Currentweek {
  earnings: string
  profit: string
  expense: string
}
