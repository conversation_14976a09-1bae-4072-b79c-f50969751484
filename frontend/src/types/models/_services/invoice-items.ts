import { DateType } from 'src/types/forms/reactDatepickerTypes'

export interface FetchInvoiceItemsParams {
  offset: number
  limit: number
  search?: string
  billed?: boolean
  order__prestation?: string
  validation?: string
  be_number?: string
  order_code?: string
  customer_name?: string
  agence_name?: string
  date_range?: string
  bc_number?: string
  date_range_before?: any
  date_range_after?: any
}

export interface InvoiceItemResponse {
  count: number
  next: string
  previous: null
  results: InvoiceItemData[]
}

export interface InvoiceItemRequestPayload {
  tractor_id: string
  trailer_id: string
  path_id: string
  agence_id: string
  driver_id: string
  direction: string
  be_number: string
  bc_number: string
  price: string
  qty: string
  road_fees: string
  petrol_volume: number
  validation: string
  product_id: number
  nature_product_id: number
  order_id: number
}

export interface InvoiceItemData {
  id: string
  modified_by: null
  tractor: Tractor
  trailer: Tractor | null
  path: Path
  agence: Path
  driver: Driver
  customer: Path
  order_code: string
  terminer: string
  direction: string
  be_number: string
  bc_number: string
  price: string
  qty: string
  qty_end: string
  amount_ht: string
  road_fees: string
  petrol_volume: number
  billed: boolean
  has_issue: boolean
  has_notification: boolean
  issue_datas: Issuedatas
  validation: string
  created_at: string
  modified_at: string
  product: number
  nature_product: null
  order: number
}

interface Issuedatas {
  [key: string]: string
}

interface Driver {
  id: string
  first_name: string
  last_name: string
}

interface Path {
  id: string
  name: string
}

interface Tractor {
  id: string
  number_plate: string
}

export interface CreateInvoicePayload {
  operation_ids: string[]
}
