export interface ExtractionResponse {
  count: number
  next: string
  previous: null
  results: Extraction[]
}

export interface Extraction {
  id: string
  order: string
  travel_number: null | string
  prestation: string
  driver: string
  path: string
  direction: string
  tractor: string
  trailer?: string
  product: string
  nature_product: null | string
  customer: string
  price: string
  qty: string
  qty_end: string
  road_fees: string
  petrol_volume: number
  petrol_fee: string
  charges: string
  charges_other: string
  margin: string
  is_mobilised: boolean | null
  has_charged: boolean | null
  charged_date: null | string
  charged_at: null | string
  immobilization_duration: null | number
  is_started: boolean | null
  started_at: null | string
  has_unloaded: boolean | null
  unloaded_date: null | string
  unloaded_at: null | string
  is_ended: boolean | null
  ended_at: null | string
  created_at: string
  billed: boolean
  da_mission: string
  da_mission_other: string
  agent: string
  agence: string
  be_number: string
  bc_number: string
  amount_ht: string
  has_issue: boolean
  has_notification: boolean
  issue_datas: Issuedatas
  validation: string
  modified_at: string
  modified_by: null
}

interface Issuedatas {
  [key: string]: string
}

export interface ExtractionParams {
  limit: number
  offset: number
  search?: string
}
