export interface MissionExpense {
  id: string
  code: string
  client: string
  for_order: number
  validation: any[]
  validation_info: Validationinfo
  mission_expense_lines: Missionexpenseline[]
  validate: boolean
  canceled: boolean
  edited: boolean
  modified_at: string
  created_at: string
  created_by: Nextvalidator
  modified_by: Nextvalidator
}

export interface Missionexpenseline {
  id: string
  operation: string
  mission_expense: string
  order: number
  driver: Nextvalidator
  departure: Departure
  destination: Departure
  vehicle: string
  be_number: string
  bc_number: string
  fees: string
  petrol_volum: number
  petrol_amount: string
  product_qty: number
  product: Product
  obs: null
  created_at: string
  edited: boolean
  modified_at: string
  created_by: Nextvalidator
  modified_by: Nextvalidator
}

interface Product {
  id: number
  label: string
}

interface Departure {
  id: string
  name: string
}

interface Validationinfo {
  validation_count: number
  total_validations_needed: number
  next_validator: Nextvalidator
  last_validation_date: string
  validation_history: ValidationHistory[]
}

interface ValidationHistory {
  user: User
  status: string
  date: string
  reason: string
}

interface User {
  id: string
  name: string
}

interface Nextvalidator {
  id: string
  first_name: string
  last_name: string
}
export interface MissionExpenseParams {
  limit: number
  offset: number
  search?: string
}
