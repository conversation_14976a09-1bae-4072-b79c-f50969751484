export interface Template {
  id: string
  name: string
  html: string
  css: string
  components: string
  createdAt: string
  updatedAt: string
}

export interface TemplateFormData {
  id: string | null
  name: string
  html: string
  css: string
  components: string
}

export interface GrapeJSEditorProps {
  initialData: Template | null
}

export interface GrapeJSEditorRef {
  getHtml: () => string
  getCss: () => string
  getComponents: () => any[]
}

export interface DynamicPreviewProps {
  templateHtml: string
}

export interface DynamicFields {
  [key: string]: string
}
