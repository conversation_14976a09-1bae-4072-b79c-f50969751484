export type InvoiceStatus = 'Paid' | string

export type InvoiceLayoutProps = {
  id: string | undefined
}

export type InvoiceClientType = {
  name: string
  address: string
  company: string
  country: string
  contact: string
  companyEmail: string
}

export type InvoiceType = {
  id: number
  name: string
  total: number
  avatar: string
  service: string
  dueDate: string
  address: string
  company: string
  country: string
  contact: string
  avatarColor?: string
  issuedDate: string
  companyEmail: string
  balance: string | number
  invoiceStatus: InvoiceStatus
}

export type InvoicePaymentType = {
  iban: string
  totalDue: string
  bankName: string
  country: string
  swiftCode: string
}

export type SingleInvoiceType = {
  invoice: InvoiceType
  paymentDetails: InvoicePaymentType
}

// export const mockInvoice: InvoiceType = {
//   id: 1001,
//   name: '<PERSON>',
//   total: 2500,
//   avatar: '/images/avatars/1.png',
//   service: 'Web Development',
//   dueDate: '25 Dec 2023',
//   address: '123 Tech Street, Suite 101',
//   company: 'TechCorp Solutions',
//   country: 'United States',
//   contact: '(555) 123-4567',
//   avatarColor: 'primary',
//   issuedDate: '10 Dec 2023',
//   companyEmail: '<EMAIL>',
//   balance: '$500',
//   invoiceStatus: 'Paid'
// }

// const mockPaymentDetails: InvoicePaymentType = {
//   iban: '**********************',
//   totalDue: '$2,500.00',
//   bankName: 'Global Bank',
//   country: 'United States',
//   swiftCode: 'GLBAUS22'
// }

// export const mockSingleInvoice: SingleInvoiceType = {
//   invoice: mockInvoice,
//   paymentDetails: mockPaymentDetails
// }

// // Generate multiple invoices
// export const mockInvoices: InvoiceType[] = [
//   mockInvoice,
//   {
//     id: 1002,
//     name: 'Jane Smith',
//     total: 3750,
//     avatar: '/images/avatars/2.png',
//     service: 'UI/UX Design',
//     dueDate: '30 Dec 2023',
//     address: '456 Design Avenue, Unit 202',
//     company: 'Creative Designs LLC',
//     country: 'Canada',
//     contact: '(*************',
//     avatarColor: 'secondary',
//     issuedDate: '15 Dec 2023',
//     companyEmail: '<EMAIL>',
//     balance: 0,
//     invoiceStatus: 'Pending'
//   },
//   {
//     id: 1003,
//     name: 'Robert Johnson',
//     total: 1800,
//     avatar: '/images/avatars/3.png',
//     service: 'Mobile App Development',
//     dueDate: '05 Jan 2024',
//     address: '789 Mobile Road',
//     company: 'App Innovators Inc',
//     country: 'United Kingdom',
//     contact: '(*************',
//     avatarColor: 'success',
//     issuedDate: '20 Dec 2023',
//     companyEmail: '<EMAIL>',
//     balance: '$1,800',
//     invoiceStatus: 'Draft'
//   }
// ]
