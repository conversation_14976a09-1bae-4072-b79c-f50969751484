export interface AuthState {
  isInitializing: boolean
  isLoggingIn: boolean
  isLoggingOut: boolean
  isAuthenticated: boolean
  loginError: string | null
  logoutError: string | null
}

export interface LoginCredentials {
  username: string
  password: string
}

export interface AuthContextType extends AuthState {
  login: (credentials: LoginCredentials) => Promise<void>
  logout: () => Promise<void>

  // getUser: () => Promise<any | null>
}

export interface LoginResponse {
  token_type: string
  access_token: string
  refresh_token: string
  expires_in: number
  scope: string
}
export interface Tokens {
  access_token: string
  refresh_token: string
}
