import { createSlice, type PayloadAction } from '@reduxjs/toolkit'
import { User } from 'src/@core/types/auth'

interface UserState {
  user: User | null
  isAuthenticated: boolean
  isLoading: boolean
  error: string | null
}

const getUserFromLocalStorage = (): User | null => {
  if (typeof window === 'undefined') {
    return null
  }

  const user = localStorage.getItem('user')

  return user ? JSON.parse(user) : null
}

const storedUser = getUserFromLocalStorage()

const initialState: UserState = {
  user: storedUser,
  isAuthenticated: !!storedUser,
  isLoading: false,
  error: null
}

const userSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    setUser: (state, action: PayloadAction<User>) => {
      state.user = action.payload
      state.isAuthenticated = true
      state.error = null

      if (typeof window !== 'undefined') {
        localStorage.setItem('user', JSON.stringify(action.payload))
      }
    },

    clearUser: state => {
      state.user = null
      state.isAuthenticated = false

      if (typeof window !== 'undefined') {
        localStorage.removeItem('user')
      }
    },

    updateUser: (state, action: PayloadAction<Partial<User>>) => {
      if (state.user) {
        state.user = { ...state.user, ...action.payload }

        if (typeof window !== 'undefined') {
          localStorage.setItem('user', JSON.stringify(state.user))
        }
      }
    },

    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload
    },

    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload
    }
  }
})

export const { setUser, clearUser, updateUser, setLoading, setError } = userSlice.actions

export default userSlice.reducer

export const selectUser = (state: { auth: UserState }) => state.auth.user

export const selectIsAuthenticated = (state: { auth: UserState }) => state.auth.isAuthenticated

export const selectIsLoading = (state: { auth: UserState }) => state.auth.isLoading

export const selectError = (state: { auth: UserState }) => state.auth.error
