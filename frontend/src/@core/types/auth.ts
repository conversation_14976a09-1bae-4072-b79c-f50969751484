export interface Permission {
  model: string
  permission: string[]
}

export interface UserPermissions {
  [key: string]: Permission[]
}

export interface User {
  id: string
  username: string
  email: string
  first_name: string
  language: string
  other_phone: string | null
  picture: string | null
  profil: string | null
  is_validator: boolean
  last_name: string
  is_superuser: boolean
  supervisor_id: string | null
  is_staff: boolean
  is_active: boolean
  type_of_operation: string

  user_permissions: UserPermissions
}
