import { Box, Typography, CardContent, Card, Grid, useTheme, Chip } from '@mui/material'
import { Icon } from '@iconify/react'
import { Vehicle } from '../../types/models/vehicles'
import Image from 'next/image'
import { display } from '@mui/system'

export const VehicleCard = ({ vehicle }: { vehicle: Vehicle }) => {
  const theme = useTheme()

  const statusRenderer = (params: any) => (
    <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
      {params.value ? (
        <Icon icon='mdi:check-circle' color={theme.palette.success.main} fontSize={20} />
      ) : (
        <Icon icon='mdi:close-circle' color={theme.palette.error.main} fontSize={20} />
      )}
    </Box>
  )

  const getVehicleImage = (type: string) => {
    switch (type) {
      case 'REMORQUE':
        return '/images/trucks/truck_plateaux.png'
      case 'CAMION':
        return '/images/trucks/truck_benne.png'
      case 'PICKUP':
        return '/images/trucks/truck_citerne.png'
      case 'TRACTEUR':
        return '/images/trucks/truck_tractor.png'
      case 'HEAD_OFFICE':
        return '/images/trucks/office.png'
      default:
        return '/images/trucks/default.png'
    }
  }

  return (
    <Card
      sx={{
        height: '100%',
        border: '1px solid',
        borderColor: vehicle.vehicle_type ? 'divider' : 'error.main',
        '&:hover': {
          boxShadow: theme.shadows[4]
        }
      }}
    >
      <CardContent>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Box sx={{ width: '100%', height: 80, position: 'relative' }}>
            <Image
              src={getVehicleImage(vehicle.vehicle_type)}
              alt={vehicle.vehicle_type}
              layout='fill'
              objectFit='contain'
            />
          </Box>
          {/* <Box sx={{ display: 'flex', alignItems: 'center' }}>{statusRenderer({ value: vehicle.is_active })}</Box> */}
        </Box>

        <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
          <Typography variant='h6' gutterBottom>
            {vehicle.number_plate}
          </Typography>

          <Chip
            label={`${vehicle.is_active ? 'Active' : 'In active'}`}
            color={vehicle.is_active ? 'success' : 'error'}
            variant='outlined'
          />
        </Box>

        <Box sx={{ mt: 2 }}>
          <Typography variant='body2' sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
            <Icon icon='mdi:gauge' fontSize={20} />
            <span>Old Km: {vehicle.kilometer_old?.toLocaleString()}</span>
          </Typography>
          <Typography variant='body2' sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
            <Icon icon='mdi:gauge-empty' fontSize={20} />
            <span>New Km: {vehicle.kilometer_new?.toLocaleString()}</span>
          </Typography>
          <Typography variant='body2' sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Icon icon='mdi:gas-station' fontSize={20} />
            <span>Consumption: {vehicle.petrol_consumption}</span>
          </Typography>
        </Box>
      </CardContent>
    </Card>
  )
}
