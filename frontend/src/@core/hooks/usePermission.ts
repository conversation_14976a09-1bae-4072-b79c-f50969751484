import { useState } from 'react'
import { useAbility } from '../context/ability-provider'
import { ACTIONS } from 'src/layouts/components/acl/can'
import { useSelector } from 'react-redux'
import { selectUser } from 'src/store/auth'

export function usePermission() {
  const user = useSelector(selectUser)
  const { ability } = useAbility()
  const [hasAccess, setHasAccess] = useState<boolean>(false)

  const can = (module: string, model: string, action: string | string[]): boolean => {
    if (user?.is_superuser) return true

    const actions = Array.isArray(action) ? action : [action]
    const subject = `${module}:${model}`

    // If checking for 'read' and user has 'update' for any model in the module, grant access
    if (actions.includes(ACTIONS.READ) && hasUpdatePermission(module, model)) {
      return true
    }

    return actions.every(act => ability.can(act, subject))
  }

  const hasUpdatePermission = (module: string, model: string): boolean => {
    if (!user?.user_permissions) return false

    const modulePermissions = user.user_permissions[module]
    if (!modulePermissions) return false

    const modelPermissions = modulePermissions.find(p => p.model === model)
    if (!modelPermissions) return false

    return modelPermissions.permission.includes(ACTIONS.UPDATE)
  }

  const getModulePrivileges = (module: string) => {
    if (!user?.user_permissions || !user.user_permissions[module]) return []

    return user.user_permissions[module].map(permission => ({
      model: permission.model,
      actions: permission.permission
    }))
  }

  const getModelPrivileges = (module: string, model: string): string[] => {
    if (!user?.user_permissions || !user.user_permissions[module]) return []

    const modelPermissions = user.user_permissions[module].find(p => p.model === model)
    if (!modelPermissions) return []

    const permissions = [...modelPermissions.permission]

    // If user has UPDATE permission, automatically add READ permission
    if (permissions.includes(ACTIONS.UPDATE) && !permissions.includes(ACTIONS.READ)) {
      permissions.push(ACTIONS.READ)
    }

    return permissions
  }

  const checkPermissionUpdate = (module: string, model: string, action: string | string[]) => {
    const currentAccess = can(module, model, action)
    setHasAccess(currentAccess)

    return currentAccess
  }

  return {
    can,
    module,
    ACTIONS,
    getModulePrivileges,
    getModelPrivileges,
    checkPermissionUpdate,
    hasAccess
  }
}
