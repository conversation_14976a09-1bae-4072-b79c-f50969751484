import { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { Actions, Subjects } from 'src/configs/abilityConfig'
import { useAbility } from '../context/ability-provider'
import { useSelector } from 'react-redux'
import { selectUser } from 'src/store/auth'

export function useRouteGuard(action: Actions, subject: Subjects) {
  const router = useRouter()
  const { ability } = useAbility()
  const user = useSelector(selectUser)

  useEffect(() => {
    if (!user) {
      router.push('/login')
    } else if (!ability.can(action, subject)) {
      router.push('/401')
    }
  }, [user, ability, action, subject, router])

  return {
    isAllowed: user && ability.can(action, subject)
  }
}
