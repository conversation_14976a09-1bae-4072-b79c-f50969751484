import { useState, useEffect } from 'react'
import {
  <PERSON><PERSON>,
  <PERSON>alogT<PERSON>le,
  DialogContent,
  DialogActions,
  Button,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem
} from '@mui/material'
import { useTranslation } from 'react-i18next'
import moment from 'moment'

import CustomInput from 'src/views/forms/form-elements/pickers/PickersCustomInput'
import DatePickerWrapper from 'src/@core/styles/libs/react-datepicker'
import DatePicker from 'react-datepicker'

interface FilterDialogProps {
  open: boolean
  onClose: () => void
  onApply: (filters: any) => void
  clients?: { id: string; name: string }[]
}

interface FiltersState {
  created_at_gte: Date | null
  created_at_lte: Date | null

  order__prestation: string
  order__customer: string
  billed: string
}

const initialFilters: FiltersState = {
  created_at_gte: null,
  created_at_lte: null,

  order__prestation: 'all',
  order__customer: 'all',
  billed: 'all'
}

export default function ExtractionFilterDialog({ open, onClose, onApply, clients = [] }: FilterDialogProps) {
  const { t } = useTranslation()
  const [filters, setFilters] = useState<FiltersState>(initialFilters)
  const [dateFilters, setDateFilters] = useState<{
    dateFrom: Date | null
    dateTo: Date | null
  }>({
    dateFrom: null,
    dateTo: null
  })

  useEffect(() => {
    const { created_at_gte, created_at_lte, ...otherFilters } = filters
    onApply(otherFilters)
  }, [filters.order__prestation, filters.order__customer, filters.billed, filters, onApply])

  const handleClear = () => {
    setFilters(initialFilters)
    setDateFilters({ dateFrom: null, dateTo: null })
  }

  const handleApply = () => {
    setFilters(prev => ({
      ...prev,
      created_at_gte: dateFilters.dateFrom,
      created_at_lte: dateFilters.dateTo
    }))

    onApply({
      ...filters,
      dateFrom: dateFilters.dateFrom ? moment(dateFilters.dateFrom).format('YYYY-MM-DD') : undefined,
      dateTo: dateFilters.dateTo ? moment(dateFilters.dateTo).format('YYYY-MM-DD') : undefined
    })
    onClose()
  }

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth='sm'
      fullWidth
      PaperProps={{
        sx: {
          p: theme => theme.spacing(2),
          overflow: 'visible'
        }
      }}
    >
      <DialogTitle sx={{ px: 0 }}>{t('filters.title', 'Filtres')}</DialogTitle>
      <DialogContent sx={{ px: 0, overflow: 'visible' }}>
        <Grid container spacing={4}>
          {/* Date Range Picker */}
          <Grid item xs={12}>
            <Grid container spacing={2}>
              <Grid item xs={6}>
                <DatePickerWrapper>
                  <DatePicker
                    selected={dateFilters.dateFrom}
                    onChange={(date: Date | null) => setDateFilters(prev => ({ ...prev, dateFrom: date }))}
                    customInput={<CustomInput fullWidth label={t('filters.dateFrom', 'Du') as string} />}
                    dateFormat='dd/MM/yyyy'
                  />
                </DatePickerWrapper>
              </Grid>
              <Grid item xs={6}>
                <DatePickerWrapper>
                  <DatePicker
                    selected={dateFilters.dateTo}
                    onChange={(date: Date | null) => setDateFilters(prev => ({ ...prev, dateTo: date }))}
                    customInput={<CustomInput fullWidth label={t('filters.dateTo', 'Au') as string} />}
                    dateFormat='dd/MM/yyyy'
                  />
                </DatePickerWrapper>
              </Grid>
            </Grid>
          </Grid>

          {/* Prestation Dropdown */}
          <Grid item xs={12}>
            <FormControl fullWidth size='small'>
              <InputLabel>{t('filters.prestation', 'Prestation')}</InputLabel>
              <Select
                value={filters.order__prestation}
                label={t('filters.prestation', 'Prestation')}
                onChange={e => setFilters(prev => ({ ...prev, order__prestation: e.target.value }))}
              >
                <MenuItem value='all'>{t('filters.all', 'Tous')}</MenuItem>
                <MenuItem value='CITERNE'>Citerne</MenuItem>
                <MenuItem value='PLATEAU'>Plateau</MenuItem>
                <MenuItem value='BENNE'>Benne</MenuItem>
              </Select>
            </FormControl>
          </Grid>

          {/* Client Dropdown */}
          <Grid item xs={12}>
            <FormControl fullWidth size='small'>
              <InputLabel>{t('filters.client', 'Clients')}</InputLabel>
              <Select
                value={filters.order__customer}
                label={t('filters.client', 'Clients')}
                onChange={e => setFilters(prev => ({ ...prev, order__customer: e.target.value }))}
              >
                <MenuItem value='all'>{t('filters.all', 'Tous')}</MenuItem>
                {clients.map(client => (
                  <MenuItem key={client.id} value={client.id}>
                    {client.name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>

          {/* Invoiced Status Dropdown */}
          <Grid item xs={12}>
            <FormControl fullWidth size='small'>
              <InputLabel>{t('filters.invoiced', 'Facturée ?')}</InputLabel>
              <Select
                value={filters.billed}
                label={t('filters.invoiced', 'Facturée ?')}
                onChange={e => setFilters(prev => ({ ...prev, billed: e.target.value }))}
              >
                <MenuItem value='all'>{t('filters.all', 'Tous')}</MenuItem>
                <MenuItem value='yes'>{t('filters.yes', 'Oui')}</MenuItem>
                <MenuItem value='no'>{t('filters.no', 'Non')}</MenuItem>
              </Select>
            </FormControl>
          </Grid>
        </Grid>
      </DialogContent>
      <DialogActions>
        <Button onClick={handleClear}>{t('filters.clear', 'Réinitialiser')}</Button>
        <Button onClick={handleApply} variant='contained'>
          {t('filters.apply', 'Appliquer')}
        </Button>
      </DialogActions>
    </Dialog>
  )
}
