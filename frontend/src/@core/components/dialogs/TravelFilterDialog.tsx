import { useState, useEffect } from 'react'
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Box
} from '@mui/material'
import DatePicker from 'react-datepicker'
import { useTranslation } from 'react-i18next'
import moment from 'moment'

import CustomInput from 'src/views/forms/form-elements/pickers/PickersCustomInput'
import DatePickerWrapper from 'src/@core/styles/libs/react-datepicker'

interface FilterDialogProps {
  open: boolean
  onClose: () => void
  onApply: (filters: any) => void
}

interface FiltersState {
  dateFrom: Date | null
  dateTo: Date | null
  prestation: string
  isStarted: string
  isEnded: string
  hasCharged: string
  hasUnloaded: string
}

const initialFilters: FiltersState = {
  dateFrom: null,
  dateTo: null,
  prestation: 'all',
  isStarted: 'all',
  isEnded: 'all',
  hasCharged: 'all',
  hasUnloaded: 'all'
}

export default function TravelFilterDialog({ open, onClose, onApply }: FilterDialogProps) {
  const { t } = useTranslation()
  const [filters, setFilters] = useState<FiltersState>(initialFilters)
  const [dateFilters, setDateFilters] = useState<{
    dateFrom: Date | null
    dateTo: Date | null
  }>({
    dateFrom: null,
    dateTo: null
  })

  useEffect(() => {
    const { dateFrom, dateTo, ...otherFilters } = filters
    onApply(otherFilters)
  }, [filters.prestation, filters.isStarted, filters.isEnded, filters.hasCharged, filters.hasUnloaded])

  const handleClear = () => {
    setFilters(initialFilters)
    setDateFilters({ dateFrom: null, dateTo: null })
  }

  const handleApply = () => {
    setFilters(prev => ({
      ...prev,
      dateFrom: dateFilters.dateFrom,
      dateTo: dateFilters.dateTo
    }))

    onApply({
      ...filters,
      dateFrom: dateFilters.dateFrom ? moment(dateFilters.dateFrom).format('YYYY-MM-DD') : undefined,
      dateTo: dateFilters.dateTo ? moment(dateFilters.dateTo).format('YYYY-MM-DD') : undefined
    })
    onClose()
  }

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth='sm'
      fullWidth
      PaperProps={{
        sx: {
          p: theme => theme.spacing(2),
          overflow: 'visible'
        }
      }}
    >
      <DialogTitle sx={{ px: 0 }}>{t('filters.title', 'Filtres')}</DialogTitle>
      <DialogContent sx={{ px: 0, overflow: 'visible' }}>
        <Grid container spacing={4}>
          {/* Date Range Picker */}
          <Grid item xs={12}>
            <Grid container spacing={2}>
              <Grid item xs={6}>
                <DatePickerWrapper>
                  <DatePicker
                    selected={dateFilters.dateFrom}
                    onChange={(date: Date | null) => setDateFilters(prev => ({ ...prev, dateFrom: date }))}
                    customInput={<CustomInput fullWidth label={t('filters.dateFrom', 'Du') as string} />}
                    dateFormat='dd/MM/yyyy'
                  />
                </DatePickerWrapper>
              </Grid>
              <Grid item xs={6}>
                <DatePickerWrapper>
                  <DatePicker
                    selected={dateFilters.dateTo}
                    onChange={(date: Date | null) => setDateFilters(prev => ({ ...prev, dateTo: date }))}
                    customInput={<CustomInput fullWidth label={t('filters.dateTo', 'Au') as string} />}
                    dateFormat='dd/MM/yyyy'
                  />
                </DatePickerWrapper>
              </Grid>
            </Grid>
          </Grid>

          {/* Prestation Dropdown */}
          <Grid item xs={12}>
            <FormControl fullWidth size='small'>
              <InputLabel>{t('filters.prestation', 'Par Prestation')}</InputLabel>
              <Select
                value={filters.prestation}
                label={t('filters.prestation', 'Par Prestation')}
                onChange={e => setFilters(prev => ({ ...prev, prestation: e.target.value }))}
              >
                <MenuItem value='all'>{t('filters.all', 'Tous')}</MenuItem>
                <MenuItem value='CITERNE'>Citerne</MenuItem>
                <MenuItem value='PLATEAU'>Plateau</MenuItem>
                <MenuItem value='BENNE'>Benne</MenuItem>
              </Select>
            </FormControl>
          </Grid>

          {/* Status Dropdowns - First Row */}
          <Grid item xs={6}>
            <FormControl fullWidth size='small'>
              <InputLabel>{t('filters.started', 'Par Débuté')}</InputLabel>
              <Select
                value={filters.isStarted}
                label={t('filters.started', 'Par Débuté')}
                onChange={e => setFilters(prev => ({ ...prev, isStarted: e.target.value }))}
              >
                <MenuItem value='all'>{t('filters.all', 'Tous')}</MenuItem>
                <MenuItem value='yes'>{t('filters.yes', 'Oui')}</MenuItem>
                <MenuItem value='no'>{t('filters.no', 'Non')}</MenuItem>
              </Select>
            </FormControl>
          </Grid>

          <Grid item xs={6}>
            <FormControl fullWidth size='small'>
              <InputLabel>{t('filters.ended', 'Par Terminé')}</InputLabel>
              <Select
                value={filters.isEnded}
                label={t('filters.ended', 'Par Terminé')}
                onChange={e => setFilters(prev => ({ ...prev, isEnded: e.target.value }))}
              >
                <MenuItem value='all'>{t('filters.all', 'Tous')}</MenuItem>
                <MenuItem value='yes'>{t('filters.yes', 'Oui')}</MenuItem>
                <MenuItem value='no'>{t('filters.no', 'Non')}</MenuItem>
              </Select>
            </FormControl>
          </Grid>

          {/* Status Dropdowns - Second Row */}
          <Grid item xs={6}>
            <FormControl fullWidth size='small'>
              <InputLabel>{t('filters.charged', 'Par A chargé')}</InputLabel>
              <Select
                value={filters.hasCharged}
                label={t('filters.charged', 'Par A chargé')}
                onChange={e => setFilters(prev => ({ ...prev, hasCharged: e.target.value }))}
              >
                <MenuItem value='all'>{t('filters.all', 'Tous')}</MenuItem>
                <MenuItem value='yes'>{t('filters.yes', 'Oui')}</MenuItem>
                <MenuItem value='no'>{t('filters.no', 'Non')}</MenuItem>
              </Select>
            </FormControl>
          </Grid>

          <Grid item xs={6}>
            <FormControl fullWidth size='small'>
              <InputLabel>{t('filters.unloaded', 'Par A déchargé')}</InputLabel>
              <Select
                value={filters.hasUnloaded}
                label={t('filters.unloaded', 'Par A déchargé')}
                onChange={e => setFilters(prev => ({ ...prev, hasUnloaded: e.target.value }))}
              >
                <MenuItem value='all'>{t('filters.all', 'Tous')}</MenuItem>
                <MenuItem value='yes'>{t('filters.yes', 'Oui')}</MenuItem>
                <MenuItem value='no'>{t('filters.no', 'Non')}</MenuItem>
              </Select>
            </FormControl>
          </Grid>
        </Grid>
      </DialogContent>
      <DialogActions sx={{ px: 0, pb: 0 }}>
        <Button onClick={handleClear} color='secondary'>
          {t('common.clear', 'Effacer')}
        </Button>
        <Button onClick={handleApply} variant='contained'>
          {t('common.apply', 'Appliquer')}
        </Button>
      </DialogActions>
    </Dialog>
  )
}
