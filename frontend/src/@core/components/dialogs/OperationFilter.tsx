import { useState, useEffect } from 'react'
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Box,
  Typography
} from '@mui/material'
import DatePicker from 'react-datepicker'
import { useTranslation } from 'react-i18next'
import moment from 'moment'

import CustomInput from 'src/views/forms/form-elements/pickers/PickersCustomInput'
import DatePickerWrapper from 'src/@core/styles/libs/react-datepicker'
import { FetchInvoiceItemsParams } from 'src/types/models/_services/invoice-items'
import { useCustomers } from 'src/hooks/useCustomer'
import { useAgencies } from 'src/hooks/useAgency'

interface FilterDialogProps {
  open: boolean
  onClose: () => void
  onApply: (filters: Partial<FetchInvoiceItemsParams>) => void
  setQueryParams: React.Dispatch<React.SetStateAction<Partial<FetchInvoiceItemsParams>>>
  setOpenFilters: React.Dispatch<React.SetStateAction<boolean>>
}

const initialFilters: Partial<FetchInvoiceItemsParams> = {
  date_range_before: null,
  date_range_after: null,
  order__prestation: 'Tous',
  customer_name: 'Tous',
  agence_name: 'Tous',
  validation: 'Tous',
  billed: false
}

const prestation = [
  { value: 'Tous', label: 'Tous' },
  { value: 'CITERNE', label: 'Citerne' },
  { value: 'PLATEAU', label: 'Plateau' },
  { value: 'BENNE', label: 'Benne' }
]

const validationOptions = [
  { value: 'Tous', label: 'Tous' },
  { value: 'EN ATTENTE', label: 'En attente' },
  { value: 'VALIDEE', label: 'Validée' },
  { value: 'REJETEE', label: 'Rejétée' },
  { value: 'ANULLEE', label: 'Annulée' }
]

const billedOptions = [
  { value: 'Tous', label: 'Tous' },
  { value: 'Oui', label: 'Oui' },
  { value: 'Non', label: 'Non' }
]

export default function OperationFilter({ open, onClose, onApply, setQueryParams, setOpenFilters }: FilterDialogProps) {
  const { t } = useTranslation()
  const [filters, setFilters] = useState<Partial<FetchInvoiceItemsParams>>(initialFilters)

  const { data: customersData, isLoading: isLoadingCustomers } = useCustomers({ limit: 100, offset: 0 })
  const { data: agenciesData, isLoading: isLoadingAgencies } = useAgencies({ limit: 100, offset: 0 })

  return (
    <Dialog open={open} onClose={onClose}>
      <DialogTitle>Filtrer</DialogTitle>
      <DialogContent>
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3, mt: 2, minWidth: 500 }}>
          {/* Date Range */}

          <Typography variant='subtitle2' sx={{ mb: 1 }}>
            Par Date
          </Typography>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
            <Box sx={{ display: 'flex', gap: 2 }}>
              <DatePickerWrapper>
                <DatePicker
                  selected={filters.date_range_before}
                  onChange={(date: any) => setFilters(prev => ({ ...prev, date_range_before: date }))}
                  customInput={<CustomInput fullWidth label={t('filters.dateFrom', 'Du') as string} />}
                />
              </DatePickerWrapper>
              <DatePickerWrapper>
                <DatePicker
                  selected={filters.date_range_after}
                  onChange={date => setFilters(prev => ({ ...prev, date_range_after: date }))}
                  customInput={<CustomInput fullWidth label={t('filters.dateTo', 'Au') as string} />}
                />
              </DatePickerWrapper>
            </Box>
            <Button
              variant='contained'
              onClick={() => {
                setQueryParams(prev => ({
                  ...prev,
                  date_range_before: moment(filters.date_range_before).format('YYYY-MM-DD'),
                  date_range_after: moment(filters.date_range_after).format('YYYY-MM-DD')
                }))
              }}
            >
              Appliquer les dates
            </Button>
          </Box>

          {/* Other filters - these update immediately on change */}
          <FormControl fullWidth size='small'>
            <InputLabel>Par Prestation</InputLabel>
            <Select
              value={filters.order__prestation}
              label='Par Prestation'
              onChange={e => {
                const value = e.target.value
                setFilters(prev => ({ ...prev, order__prestation: value }))
                setQueryParams(prev => ({ ...prev, order__prestation: value === 'Tous' ? undefined : value }))
              }}
            >
              {prestation.map(option => (
                <MenuItem key={option.value} value={option.value}>
                  {option.label}
                </MenuItem>
              ))}
            </Select>
          </FormControl>

          <FormControl fullWidth size='small'>
            <InputLabel>Par Client</InputLabel>
            <Select
              value={filters.customer_name}
              label='Par Client'
              onChange={e => {
                const value = e.target.value
                setFilters(prev => ({ ...prev, customer_name: value }))
                setQueryParams(prev => ({ ...prev, customer_name: value === 'Tous' ? undefined : value }))
              }}
            >
              {customersData?.results.map((customer: any) => (
                <MenuItem key={customer.id} value={customer.name}>
                  {customer.name}
                </MenuItem>
              ))}
            </Select>
          </FormControl>

          <FormControl fullWidth size='small'>
            <InputLabel>Par Agence</InputLabel>
            <Select
              value={filters.agence_name}
              label='Par Agence'
              onChange={e => {
                const value = e.target.value
                setFilters(prev => ({ ...prev, agence_name: value }))
                setQueryParams(prev => ({ ...prev, agence_name: value === 'Tous' ? undefined : value }))
              }}
            >
              {agenciesData?.results.map((agency: any) => (
                <MenuItem key={agency.id} value={agency.name}>
                  {agency.name}
                </MenuItem>
              ))}
            </Select>
          </FormControl>

          <FormControl fullWidth size='small'>
            <InputLabel>Par Validation</InputLabel>
            <Select
              value={filters.validation}
              label='Par Validation'
              onChange={e => {
                const value = e.target.value
                setFilters(prev => ({ ...prev, validation: value }))
                setQueryParams(prev => ({ ...prev, validation: value === 'Tous' ? undefined : value }))
              }}
            >
              {validationOptions.map(option => (
                <MenuItem key={option.value} value={option.value}>
                  {option.label}
                </MenuItem>
              ))}
            </Select>
          </FormControl>

          <FormControl fullWidth size='small'>
            <InputLabel>Par Facturé</InputLabel>
            <Select
              value={filters.billed}
              label='Par Facturé'
              onChange={e => {
                const value = e.target.value
                setFilters(prev => ({ ...prev, billed: value === 'Oui' }))
                setQueryParams(prev => ({ ...prev, billed: value === 'Tous' ? undefined : value === 'Oui' }))
              }}
            >
              {billedOptions.map(option => (
                <MenuItem key={option.value} value={option.value}>
                  {option.label}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Box>
      </DialogContent>
      <DialogActions>
        <Button
          onClick={() => {
            setFilters({
              date_range_before: null,
              date_range_after: null,
              order__prestation: 'Tous',
              customer_name: 'Tous',
              agence_name: 'Tous',
              validation: 'Tous',
              billed: false
            })
            setQueryParams({
              limit: 100,
              offset: 0
            })
          }}
        >
          Effacer
        </Button>
        <Button onClick={() => setOpenFilters(false)}>Fermer</Button>
      </DialogActions>
    </Dialog>
  )
}
