import { FC } from 'react'
import { useRouter } from 'next/navigation'
import { styled } from '@mui/material/styles'
import { Typography } from '@mui/material'
import Icon from 'src/@core/components/icon'
import { useTranslation } from 'react-i18next'

interface BackButtonProps {
  onClick?: () => void
  label?: string
  className?: string
  icon?: string
  iconSize?: number
}

const ButtonWrapper = styled('div')(({ theme }) => ({
  display: 'inline-flex',
  alignItems: 'center',
  cursor: 'pointer',
  color: theme.palette.primary.main,
  marginBottom: theme.spacing(4),
  padding: theme.spacing(1),
  borderRadius: theme.shape.borderRadius,
  transition: 'all .25s ease-in-out',
  '&:hover': {
    color: theme.palette.primary.dark,
    backgroundColor: theme.palette.action.hover,
    '& .icon': {
      transform: 'translateX(-4px)'
    }
  },
  '& .icon': {
    transition: 'transform .25s ease-in-out'
  }
}))

const BackButton: FC<BackButtonProps> = ({
  onClick,
  label,
  className = '',
  icon = 'mdi:arrow-left',
  iconSize = 20
}) => {
  const router = useRouter()
  const { t } = useTranslation()

  const handleClick = () => {
    if (onClick) {
      onClick()
    } else {
      router.back()
    }
  }

  return (
    <ButtonWrapper onClick={handleClick} className={className}>
      <Icon icon={icon} fontSize={iconSize} className='icon' />
      <Typography
        component='span'
        variant='body2'
        sx={{
          ml: 2,
          fontWeight: 500,
          textTransform: 'none'
        }}
      >
        {label || t('common.back')}
      </Typography>
    </ButtonWrapper>
  )
}

export default BackButton
