// ** React Imports
import { ReactNode } from 'react'

// ** Next Import
import { useRouter } from 'next/router'

// ** MUI Imports
import Box from '@mui/material/Box'
import Button from '@mui/material/Button'
import { LoadingButton } from '@mui/lab'

// ** Icon Imports
import Icon from 'src/@core/components/icon'

interface FormButtonsProps {
  isLoading?: boolean
  mode?: 'create' | 'edit'
  cancelPath: string
  onSave: () => void
  onSaveAndEdit?: () => void
  onSaveAndAddNew?: () => void
  disabled?: boolean
  cancelLabel?: string
  saveLabel?: string
  saveAndEditLabel?: string
  saveAndAddNewLabel?: string
  saveAndContinueLabel?: string
  customButtons?: ReactNode
}

const FormButtons = ({
  isLoading = false,
  mode = 'create',
  disabled = false,
  cancelPath,
  onSave,
  onSaveAndEdit,
  onSaveAndAddNew,
  cancelLabel = 'Annuler',
  saveLabel,
  saveAndEditLabel = 'Créer et Modifier',
  saveAndAddNewLabel = 'Créer et Ajouter',
  saveAndContinueLabel = 'Modifier et Continuer',
  customButtons
}: FormButtonsProps) => {
  const router = useRouter()

  const defaultSaveLabel = mode === 'create' ? 'Créer et Retourner' : 'Modifier et Retourner'

  return (
    <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 4 }}>
      <Box sx={{ display: 'flex', gap: 2 }}>
        {customButtons}

        {/* Main Save Button */}
        <LoadingButton
          loading={isLoading}
          onClick={onSave}
          disabled={disabled}
          variant='contained'
          startIcon={<Icon icon='mdi:content-save' />}
        >
          {saveLabel || defaultSaveLabel}
        </LoadingButton>

        {/* Save and Continue/Edit Button - Shows in both modes */}
        {onSaveAndEdit && (
          <LoadingButton
            loading={isLoading}
            onClick={onSaveAndEdit}
            disabled={disabled}
            variant='contained'
            startIcon={<Icon icon='mdi:content-save-edit' />}
          >
            {mode === 'create' ? saveAndEditLabel : saveAndContinueLabel}
          </LoadingButton>
        )}

        {/* Save and Add New Button - Shows only in create mode */}
        {mode === 'create' && onSaveAndAddNew && (
          <LoadingButton
            loading={isLoading}
            onClick={onSaveAndAddNew}
            disabled={disabled}
            variant='contained'
            color='success'
            startIcon={<Icon icon='mdi:plus-circle' />}
          >
            {saveAndAddNewLabel}
          </LoadingButton>
        )}
      </Box>
    </Box>
  )
}

export default FormButtons
