// ** MUI Imports
import { useTheme } from '@mui/material/styles'
import Box, { BoxProps } from '@mui/material/Box'
import CircularProgress from '@mui/material/CircularProgress'
import Image from 'next/image'
import useThemeImage from 'src/hooks/useThemeImage'

const FallbackSpinner = ({ sx }: { sx?: BoxProps['sx'] }) => {
  // ** Hook
  const theme = useTheme()
  const { imageSource } = useThemeImage({
    lightImage: '/images/logo_winlog.png',
    darkImage: '/images/logo_winlog.png'
  })

  return (
    <Box
      sx={{
        height: '100vh',
        display: 'flex',
        alignItems: 'center',
        flexDirection: 'column',
        justifyContent: 'center',
        ...sx
      }}
    >
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', flexDirection: 'column' }}>
        <Image
          src={imageSource}
          width={300}
          height={200}
          alt='logo image'
          style={{
            width: '50%',
            objectFit: 'contain'
          }}
        />
        <CircularProgress disableShrink />
      </Box>
    </Box>
  )
}

export default FallbackSpinner
