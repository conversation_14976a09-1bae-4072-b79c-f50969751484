import { useState } from 'react'
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TextField,
  IconButton,
  Typography,
  Box,
  Chip,
  useTheme,
  useMediaQuery
} from '@mui/material'
import Icon from 'src/@core/components/icon'
import { useTranslation } from 'react-i18next'

interface FilterValue {
  field: string
  value: any
  label: string
}

interface FilterModalProps {
  open: boolean
  onClose: () => void
  onApply: (filters: Record<string, any>) => void
  initialFilters?: Record<string, any>
  availableFilters: {
    field: string
    label: string
    type: 'select' | 'text' | 'date' | 'boolean'
    options?: { value: string; label: string }[]
  }[]
}

const FilterModal = ({ open, onClose, onApply, initialFilters = {}, availableFilters }: FilterModalProps) => {
  const { t } = useTranslation()
  const theme = useTheme()
  const fullScreen = useMediaQuery(theme.breakpoints.down('md'))

  const [filters, setFilters] = useState<Record<string, any>>(initialFilters)
  const [activeFilters, setActiveFilters] = useState<FilterValue[]>(
    Object.entries(initialFilters).map(([field, value]) => ({
      field,
      value,
      label: availableFilters.find(f => f.field === field)?.label || field
    }))
  )

  const handleFilterChange = (field: string, value: any) => {
    setFilters(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleRemoveFilter = (field: string) => {
    const newFilters = { ...filters }
    delete newFilters[field]
    setFilters(newFilters)
    setActiveFilters(prev => prev.filter(f => f.field !== field))
  }

  const handleApply = () => {
    const nonEmptyFilters = Object.entries(filters).reduce((acc, [key, value]) => {
      if (value !== '' && value !== null && value !== undefined && value !== 'all') {
        acc[key] = value
      }

      return acc
    }, {} as Record<string, any>)

    setActiveFilters(
      Object.entries(nonEmptyFilters).map(([field, value]) => ({
        field,
        value,
        label: availableFilters.find(f => f.field === field)?.label || field
      }))
    )

    onApply(nonEmptyFilters)
  }

  const handleClear = () => {
    setFilters({})
    setActiveFilters([])
    onApply({})
  }

  const renderFilterInput = (filter: (typeof availableFilters)[0]) => {
    switch (filter.type) {
      case 'select':
        return (
          <FormControl fullWidth size='small'>
            <InputLabel>{filter.label}</InputLabel>
            <Select
              value={filters[filter.field] || 'all'}
              label={filter.label}
              onChange={e => handleFilterChange(filter.field, e.target.value)}
            >
              <MenuItem value='all'>{t('filters.all', 'All')}</MenuItem>
              {filter.options?.map(option => (
                <MenuItem key={option.value} value={option.value}>
                  {option.label}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        )
      case 'date':
        return (
          <TextField
            fullWidth
            size='small'
            type='date'
            label={filter.label}
            value={filters[filter.field] || ''}
            onChange={e => handleFilterChange(filter.field, e.target.value)}
            InputLabelProps={{ shrink: true }}
          />
        )
      default:
        return (
          <TextField
            fullWidth
            size='small'
            label={filter.label}
            value={filters[filter.field] || ''}
            onChange={e => handleFilterChange(filter.field, e.target.value)}
          />
        )
    }
  }

  return (
    <Dialog fullScreen={fullScreen} open={open} onClose={onClose} maxWidth='md' fullWidth>
      <DialogTitle>
        <Box display='flex' justifyContent='space-between' alignItems='center'>
          {t('filters.title', 'Filters')}
          <IconButton size='small' onClick={onClose}>
            <Icon icon='tabler:x' />
          </IconButton>
        </Box>
      </DialogTitle>

      <DialogContent>
        {/* Active Filters */}
        {activeFilters.length > 0 && (
          <Box sx={{ mb: 4 }}>
            <Typography variant='subtitle2' sx={{ mb: 2 }}>
              {t('filters.active', 'Active Filters')}:
            </Typography>
            <Box display='flex' gap={1} flexWrap='wrap'>
              {activeFilters.map(filter => (
                <Chip
                  key={filter.field}
                  label={`${filter.label}: ${filter.value}`}
                  onDelete={() => handleRemoveFilter(filter.field)}
                  color='primary'
                  variant='outlined'
                  size='small'
                />
              ))}
            </Box>
          </Box>
        )}

        {/* Filter Inputs */}
        <Grid container spacing={3}>
          {availableFilters.map(filter => (
            <Grid item xs={12} sm={6} md={4} key={filter.field}>
              {renderFilterInput(filter)}
            </Grid>
          ))}
        </Grid>
      </DialogContent>

      <DialogActions>
        <Button onClick={handleClear} color='secondary'>
          {t('filters.clear', 'Clear')}
        </Button>
        <Button onClick={handleApply} variant='contained'>
          {t('filters.apply', 'Apply')}
        </Button>
      </DialogActions>
    </Dialog>
  )
}

export default FilterModal
