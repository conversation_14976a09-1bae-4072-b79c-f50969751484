import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  Button,
  DialogProps
} from '@mui/material'
import { useTranslation } from 'react-i18next'

interface ConfirmationModalProps extends Omit<DialogProps, 'onClose'> {
  title: string
  message: string
  confirmButtonText?: string
  cancelButtonText?: string
  confirmButtonColor?: 'inherit' | 'primary' | 'secondary' | 'success' | 'error' | 'info' | 'warning'
  onConfirm: () => void
  onCancel: () => void
}

const ConfirmationModal = ({
  open,
  title,
  message,
  confirmButtonText,
  cancelButtonText,
  confirmButtonColor = 'error',
  onConfirm,
  onCancel,
  ...props
}: ConfirmationModalProps) => {
  const { t } = useTranslation()

  return (
    <Dialog open={open} onClose={onCancel} {...props}>
      <DialogTitle>{title}</DialogTitle>
      <DialogContent>
        <DialogContentText>{message}</DialogContentText>
      </DialogContent>
      <DialogActions>
        <Button onClick={onCancel} color='secondary'>
          {cancelButtonText || t('common.actions.cancel')}
        </Button>
        <Button onClick={onConfirm} color={confirmButtonColor} variant='contained' autoFocus>
          {confirmButtonText || t('common.actions.confirm')}
        </Button>
      </DialogActions>
    </Dialog>
  )
}

export default ConfirmationModal
