import { ReactNode } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from 'src/hooks/useAuth'
import { Actions, Subjects } from 'src/configs/abilityConfig'
import Spinner from 'src/@core/components/spinner'
import { useAbility } from 'src/@core/context/ability-provider'

interface AbilityGuardProps {
  children: ReactNode
  action: Actions
  subject: Subjects
  fallback?: ReactNode
}

export const AbilityGuard = ({ children, action, subject, fallback = <Spinner /> }: AbilityGuardProps) => {
  const { isAuthenticated, isInitializing } = useAuth()
  const router = useRouter()
  const { ability } = useAbility()

  // Show loading state during initialization or user fetching
  if (isInitializing) {
    return fallback
  }

  // Check if user has required ability
  if (isAuthenticated && ability?.can(action, subject)) {
    return <>{children}</>
  }

  // No permission - redirect to 401
  router.push('/401')

  return fallback
}

export default AbilityGuard
