import { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useSelector } from 'react-redux'
import { selectUser } from 'src/store/auth'

export function GuestGuard({ children }: { children: React.ReactNode }) {
  const router = useRouter()
  const user = useSelector(selectUser)

  useEffect(() => {
    if (user) {
      router.push('/dashboards/analytics')
    }
  }, [user, router])

  if (user) return null

  return <>{children}</>
}
