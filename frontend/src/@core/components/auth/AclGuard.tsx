// ** React Imports
import { ReactNode } from 'react'

// ** Next Import
import { useRouter } from 'next/navigation'

// ** Types
import type { ACLObj } from 'src/configs/acl'

// ** Context Imports

// ** Config Import
import { buildAbilityFor } from 'src/configs/acl'

// ** Component Import
import NotAuthorized from 'src/pages/401'
import Spinner from 'src/@core/components/spinner'
import BlankLayout from 'src/@core/layouts/BlankLayout'

// ** Hooks
import { useAuth } from 'src/hooks/useAuth'
import { useAbility } from 'src/@core/context/ability-provider'
import { useSelector } from 'react-redux'
import { selectUser } from 'src/store/auth'

interface AclGuardProps {
  children: ReactNode
  aclAbilities: {
    action: string
    subject: string
  }
  guestGuard?: boolean
  authGuard?: boolean
}

const AclGuard = (props: AclGuardProps) => {
  const { aclAbilities, children, guestGuard = false, authGuard = true } = props
  const { ability } = useAbility()
  const user = useSelector(selectUser)
  const router = useRouter()

  // If guest guard or no auth guard is required
  if (guestGuard || (!guestGuard && !authGuard)) {
    return <>{children}</>
  }

  // User is not logged in
  if (!user) {
    return <Spinner />
  }

  // Super user has all permissions
  if (user.is_superuser) {
    return <>{children}</>
  }

  // Check if user has required abilities
  if (ability && ability.can(aclAbilities.action, aclAbilities.subject)) {
    return <>{children}</>
  }

  // Render Not Authorized component
  return (
    <BlankLayout>
      <NotAuthorized />
    </BlankLayout>
  )
}

export default AclGuard
