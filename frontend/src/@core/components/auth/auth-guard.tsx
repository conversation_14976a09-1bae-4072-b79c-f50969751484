import { useEffect } from 'react'
import { useRouter, usePathname } from 'next/navigation'
import { useAuth } from 'src/hooks/useAuth'
import { useSelector } from 'react-redux'
import { selectUser } from 'src/store/auth'

export function AuthGuard({ children }: { children: React.ReactNode }) {
  const user = useSelector(selectUser)
  const router = useRouter()
  const pathname = usePathname()

  useEffect(() => {
    if (!user && pathname !== '/login') {
      router.push(`${'/login'}?returnUrl=${encodeURIComponent(pathname)}`)
    }
  }, [user, pathname, router])

  if (!user && pathname !== '/login') return null

  return <>{children}</>
}
