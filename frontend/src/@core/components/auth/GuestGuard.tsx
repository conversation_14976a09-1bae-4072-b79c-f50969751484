// ** React Imports
import { ReactNode } from 'react'

// ** Next Import
import { useRouter } from 'next/navigation'

import Spinner from 'src/@core/components/spinner'
import { useAuth } from 'src/hooks/useAuth'

interface GuestGuardProps {
  children: ReactNode
  fallback?: ReactNode
}

const GuestGuard = ({ children, fallback = <Spinner /> }: GuestGuardProps) => {
  const auth = useAuth()
  const router = useRouter()

  // Show loading state during initialization or user fetching
  if (auth.isInitializing) {
    return <>{fallback}</>
  }

  // Redirect to dashboard if authenticated
  if (auth.isAuthenticated) {
    router.push('/dashboards/analytics')

    return <>{fallback}</>
  }

  return <>{children}</>
}

export default GuestGuard
