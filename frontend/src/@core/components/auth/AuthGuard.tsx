import { type ReactNode, useEffect, useState } from 'react'
import { useRouter, usePathname } from 'next/navigation'
import Spinner from 'src/@core/components/spinner'
import { useAuth } from 'src/hooks/useAuth'
import { useSelector } from 'react-redux'
import { selectUser } from 'src/store/auth'

interface AuthGuardProps {
  children: ReactNode
  fallback?: ReactNode
}

export const AuthGuard = ({ children, fallback = <Spinner /> }: AuthGuardProps) => {
  const [isFirstLogin, setIsFirstLogin] = useState<boolean>(false)
  const [isClient, setIsClient] = useState<boolean>(false)
  const { isAuthenticated, isInitializing } = useAuth()
  const router = useRouter()
  const pathname = usePathname()
  const user = useSelector(selectUser)

  // Handle client-side only code
  useEffect(() => {
    setIsClient(true)
    const firstLoginValue = localStorage.getItem('windlog_first_login')
    setIsFirstLogin(firstLoginValue === 'true')
  }, [])

  // Handle redirection for first login
  useEffect(() => {
    if (isAuthenticated && user && isFirstLogin && isClient) {
      router.push('/reset-password')
    }
  }, [isAuthenticated, user, isFirstLogin, router, isClient])

  // Show loading state while initializing or waiting for user data
  if (isInitializing || (isAuthenticated && !user)) {
    return <>{fallback}</>
  }

  // Redirect to login if not authenticated
  if (!isAuthenticated && pathname !== '/login' && isClient) {
    router.push(`/login?returnUrl=${encodeURIComponent(pathname)}`)

    return <>{fallback}</>
  }

  // Show children if authenticated and user data is available
  if (isAuthenticated && user) {
    return <>{children}</>
  }

  // Default fallback
  return <>{fallback}</>
}

export default AuthGuard
