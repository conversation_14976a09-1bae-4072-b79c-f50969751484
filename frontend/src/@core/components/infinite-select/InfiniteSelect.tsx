import React, { useRef, useState } from 'react'
import { useInfiniteQuery } from '@tanstack/react-query'
import debounce from 'lodash/debounce'
import { Autocomplete, TextField, CircularProgress, Box, Typography } from '@mui/material'
import CustomTextField from '../mui/text-field'

interface Option {
  id: string
  label: string
  value: string
  [key: string]: any
}

interface InfiniteSelectProps {
  value: string | null
  onChange: (value: string | null) => void
  placeholder?: string
  queryKey: string
  label?: string
  fetchFn: (params: { pageParam: number; searchQuery: string }) => Promise<{
    items: any[]
    nextPage: number | null
    totalItems: number
  }>
  getOptionLabel?: (option: any) => string
  renderOption?: (props: React.HTMLAttributes<HTMLLIElement>, option: any) => React.ReactNode
  isOptionEqualToValue?: (option: any, value: any) => boolean
  size?: 'small' | 'medium'
}

const InfiniteSelect: React.FC<InfiniteSelectProps> = ({
  value,
  onChange,
  placeholder = 'Search...',
  queryKey,
  fetchFn,
  label,
  getOptionLabel = option => option.label,
  renderOption,
  isOptionEqualToValue = (option, value) => option.value === value,
  size = 'small'
}) => {
  const [searchQuery, setSearchQuery] = useState('')
  const debouncedSearch = useRef(debounce((query: string) => setSearchQuery(query), 500)).current

  const { data, fetchNextPage, hasNextPage, isFetchingNextPage, isLoading, isError } = useInfiniteQuery({
    queryKey: [queryKey, searchQuery],
    queryFn: ({ pageParam = 0 }) => fetchFn({ pageParam, searchQuery }),
    getNextPageParam: lastPage => lastPage.nextPage,
    initialPageParam: 0
  })

  const items = data?.pages.flatMap(page => page.items) || []

  const handleScroll = (event: React.UIEvent<HTMLUListElement>) => {
    const listbox = event.currentTarget
    if (listbox.scrollTop + listbox.clientHeight >= listbox.scrollHeight - 50 && hasNextPage && !isFetchingNextPage) {
      fetchNextPage()
    }
  }

  const handleInputChange = (_: React.SyntheticEvent, value: string) => {
    if (value.length >= 3 || value === '') debouncedSearch(value)
  }

  const defaultRenderOption = (props: React.HTMLAttributes<HTMLLIElement>, option: Option) => (
    <li {...props} key={option.id}>
      <Box sx={{ display: 'flex', alignItems: 'center' }}>
        {option.imageUrl && (
          <img src={option.imageUrl} alt={option.label} style={{ width: 30, height: 30, marginRight: 8 }} />
        )}
        <div>
          <Typography variant='body1'>{option.label}</Typography>
          {option.description && (
            <Typography variant='caption' color='text.secondary'>
              {option.description}
            </Typography>
          )}
        </div>
      </Box>
    </li>
  )

  const ListboxComponent = React.forwardRef<HTMLUListElement, React.HTMLAttributes<HTMLUListElement>>((props, ref) => (
    <ul {...props} ref={ref} onScroll={handleScroll} style={{ maxHeight: '400px', overflow: 'auto' }}>
      {props.children}
      {isFetchingNextPage && (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 1 }}>
          <CircularProgress size={20} />
        </Box>
      )}
      {isError && <Box sx={{ p: 2, color: 'error.main', textAlign: 'center' }}>Error loading data</Box>}
      {!isLoading && !isFetchingNextPage && items.length === 0 && (
        <Box sx={{ p: 2, textAlign: 'center' }}>No items found</Box>
      )}
    </ul>
  ))

  return (
    <Autocomplete
      value={value}
      onChange={(_, newValue) => onChange(newValue)}
      options={items}
      loading={isLoading}
      onInputChange={handleInputChange}
      getOptionLabel={getOptionLabel}
      isOptionEqualToValue={isOptionEqualToValue}
      renderOption={(props, option) => (renderOption || defaultRenderOption)(props, option)}
      renderInput={params => (
        <CustomTextField
          {...params}
          size={size}
          label={label}
          placeholder={placeholder}
          InputProps={{
            ...params.InputProps,
            endAdornment: (
              <>
                {isLoading ? <CircularProgress color='inherit' size={20} /> : null}
                {params.InputProps.endAdornment}
              </>
            )
          }}
        />
      )}
      ListboxComponent={ListboxComponent}
      sx={{
        width: '100%'
      }}
    />
  )
}

export default InfiniteSelect
