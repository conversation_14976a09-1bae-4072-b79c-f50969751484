import React, { useState } from 'react'
import { useTranslation } from 'react-i18next'
import { Box, IconButton, Menu, MenuItem, Typography, Badge, Button } from '@mui/material'
import { Icon } from '@iconify/react'

// Dialog imports
import StartTravelDialog from 'src/views/travels/dialogs/StartTravelDialog'
import LoadTravelDialog from 'src/views/travels/dialogs/LoadTravelDialog'
import UnloadTravelDialog from 'src/views/travels/dialogs/UnloadTravelDialog'
import ReportImmobilizationDialog from 'src/views/travels/dialogs/ReportImmobilizationDialog'
import EndImmobilizationDialog from 'src/types/EndImmobilizationDialog'
import EndTravelDialog from 'src/views/travels/dialogs/EndTravelDialog'

interface TravelActionsProps {
  selectedRow?: any
}

const TravelActions = ({ selectedRow }: TravelActionsProps) => {
  const { t } = useTranslation()
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null)
  const [activeDialog, setActiveDialog] = useState<string | null>(null)

  const handleClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget)
  }

  const handleClose = () => {
    setAnchorEl(null)
  }

  const handleActionClick = (dialogName: string) => {
    setActiveDialog(dialogName)
    handleClose()
  }

  const handleDialogClose = () => {
    setActiveDialog(null)
  }

  const actions = [
    {
      name: 'start',
      label: t('travels.actions.start', 'Démarrer le voyage'),
      icon: 'tabler:player-play',
      color: 'primary',
      dialog: StartTravelDialog
    },
    {
      name: 'load',
      label: t('travels.actions.load', 'Charger'),
      icon: 'tabler:truck-loading',
      color: 'info',
      dialog: LoadTravelDialog
    },
    {
      name: 'unload',
      label: t('travels.actions.unload', 'Décharger'),
      icon: 'tabler:truck-unload',
      color: 'warning',
      dialog: UnloadTravelDialog
    },
    {
      name: 'reportImmobilization',
      label: t('travels.actions.reportImmobilization', 'Signaler une immobilisation'),
      icon: 'tabler:car-off',
      color: 'error',
      dialog: ReportImmobilizationDialog
    },
    {
      name: 'endImmobilization',
      label: t('travels.actions.endImmobilization', "Fin d'immobilisation"),
      icon: 'tabler:car-crash',
      color: 'success',
      dialog: EndImmobilizationDialog
    },

    {
      name: 'end',
      label: t('travels.actions.end', 'Terminer le voyage'),
      icon: 'tabler:flag',
      color: 'error',
      dialog: EndTravelDialog
    }
  ]

  return (
    <Box>
      <Button variant='contained' onClick={handleClick} color='info'>
        {t('common.actions.action')}
      </Button>

      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleClose}
        PaperProps={{
          sx: {
            minWidth: '180px',
            '& .MuiMenuItem-root': {
              py: 1,
              px: 1
            }
          }
        }}
      >
        {actions.map(action => (
          <MenuItem
            key={action.name}
            onClick={() => handleActionClick(action.name)}
            sx={{
              display: 'flex',
              alignItems: 'center',
              gap: 1
            }}
          >
            <Icon icon={action.icon} color={`${action.color}.main`} />
            <Typography variant='body2'>{action.label}</Typography>
          </MenuItem>
        ))}
      </Menu>

      {actions.map(action => {
        const DialogComponent = action.dialog

        if (!DialogComponent) {
          return null
        }
        console.log(selectedRow)

        return (
          <DialogComponent
            key={action.name}
            open={activeDialog === action.name}
            onClose={handleDialogClose}
            travelId={selectedRow[0]?.id as string}
          />
        )
      })}
    </Box>
  )
}

export default TravelActions
