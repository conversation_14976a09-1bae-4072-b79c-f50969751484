import React, { use<PERSON><PERSON>back, useMemo, useState } from 'react'
import { AgGridReact } from 'ag-grid-react'
import { GridApi, GridReadyEvent, ColDef, ModuleRegistry, AllCommunityModule } from 'ag-grid-community'
import { Box, CircularProgress, Pagination, TextField, Button, Typography, Card } from '@mui/material'
import { debounce } from 'lodash'
import { Icon } from '@iconify/react'
import useTableTheme from 'src/hooks/useTableTheme'
import { useTranslation } from 'react-i18next'
import ErrorComponent from 'src/@core/shared/components/error-component'
import { Can } from 'src/layouts/components/acl/Can'

// Register AG Grid modules once
ModuleRegistry.registerModules([AllCommunityModule])

export interface BaseTableProps<T, P> {
  data?: {
    results: T[]
    count: number
  }
  isLoading?: boolean
  isPending?: boolean
  isError?: boolean
  error?: any

  // Query parameters and handlers
  queryParams: Partial<P>
  setQueryParams: React.Dispatch<React.SetStateAction<Partial<P>>>

  // Table configuration
  columnDefs: ColDef[]
  defaultColDef?: ColDef

  // Optional props
  title?: string
  searchPlaceholder?: string
  searchField?: string
  createButtonText?: string
  onCreateClick?: () => void
  customFilters?: React.ReactNode
  noDataMessage?: string
  errorMessage?: string

  onExportClick?: (api: GridApi) => void

  // Additional customization
  renderCustomActions?: () => React.ReactNode
  renderCustomFilters?: () => React.ReactNode
  onGridReady?: (params: GridReadyEvent) => void
  onSelectionChanged?: (selectedRows: any[]) => void
  rowSelection?: 'single' | 'multiple'
  module: string
  model: string
}

export function BaseTable<T extends object, P extends { limit: number; offset: number; search?: string }>({
  // Data and loading states
  data,
  isLoading = false,
  isPending = false,
  isError = false,
  error = null,

  // Query parameters and handlers
  queryParams,
  setQueryParams,

  // Table configuration
  columnDefs,
  defaultColDef = {
    sortable: true,
    resizable: true,
    filter: true
  },

  // Optional props
  title,
  searchPlaceholder,
  searchField = 'search',
  createButtonText,
  onCreateClick,
  customFilters,
  noDataMessage,
  errorMessage,
  module,
  model,
  onExportClick,

  // Additional customization
  renderCustomActions,
  renderCustomFilters,
  onGridReady: externalOnGridReady,
  onSelectionChanged,
  rowSelection
}: BaseTableProps<T, P>) {
  const [gridApi, setGridApi] = useState<GridApi | null>(null)
  const [selectedRows, setSelectedRows] = useState<any[]>([])
  const { tableTheme } = useTableTheme()
  const { t } = useTranslation()

  // Use translations or fallback to default values
  const translatedSearchPlaceholder = searchPlaceholder || t('table.search')
  const translatedNoDataMessage = noDataMessage || t('table.noData')
  const translatedErrorMessage = errorMessage || t('table.error')

  // Handle search with debounce
  const handleSearch = debounce((value: string) => {
    if (value.length >= 3)
      setQueryParams(prev => ({
        ...prev,
        [searchField]: value || undefined,
        offset: 0
      }))
  }, 500)

  // Handle pagination
  const handlePageChange = (_: React.ChangeEvent<unknown>, value: number) => {
    setQueryParams(prev => ({
      ...prev,
      offset: (value - 1) * (prev.limit || 100)
    }))
  }
  const handleExport = useCallback(() => {
    if (gridApi && onExportClick) {
      onExportClick(gridApi)
    }
  }, [gridApi, onExportClick])

  // Grid ready handler
  const onGridReady = useCallback(
    (params: GridReadyEvent) => {
      setGridApi(params.api)
      if (externalOnGridReady) {
        externalOnGridReady(params)
      }
    },
    [externalOnGridReady]
  )

  // Handle row selection
  const handleSelectionChanged = useCallback(() => {
    if (gridApi) {
      const rows = gridApi.getSelectedRows()
      setSelectedRows(rows)
      if (onSelectionChanged) {
        onSelectionChanged(rows)
      }
    }
  }, [gridApi, onSelectionChanged])

  return (
    <Card>
      <Box sx={{ p: 3, width: '100%', mt: 3 }}>
        {/* Header with title and actions */}
        {(title || createButtonText || renderCustomActions) && (
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
            {title && (
              <Typography variant='h2' fontWeight={'bold'}>
                {title}
              </Typography>
            )}
            <Box sx={{ display: 'flex', gap: 2 }}>
              {renderCustomActions && renderCustomActions()}
              {onExportClick && (
                <Button variant='contained' startIcon={<Icon icon='mdi:download' />} onClick={handleExport}>
                  {t('common.export')}
                </Button>
              )}
              {createButtonText && onCreateClick && (
                <Can action='create' module={module} model={model}>
                  <Button variant='contained' startIcon={<Icon icon='mdi:plus' />} onClick={onCreateClick}>
                    {createButtonText}
                  </Button>
                </Can>
              )}
            </Box>
          </Box>
        )}

        {/* Search and filters */}
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
            <TextField
              placeholder={translatedSearchPlaceholder!}
              variant='outlined'
              size='small'
              onChange={e => handleSearch(e.target.value)}
              InputProps={{
                startAdornment: <Icon icon='mdi:magnify' fontSize={20} />
              }}
            />
            {renderCustomFilters && renderCustomFilters()}
            {customFilters}
          </Box>
        </Box>

        {isError && <ErrorComponent severity='error' message={error.message} title={translatedErrorMessage} />}

        {/* Table */}
        <Box sx={{ height: 500, width: '100%', position: 'relative' }}>
          {(isLoading || isPending) && (
            <Box
              sx={{
                position: 'absolute',
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                backgroundColor: 'rgba(255, 255, 255, 0.7)',
                zIndex: 1
              }}
            >
              <CircularProgress />
            </Box>
          )}
          <div style={{ height: '100%', width: '100%' }}>
            <AgGridReact
              rowData={data?.results}
              columnDefs={columnDefs}
              defaultColDef={defaultColDef}
              pagination={true}
              paginationPageSize={queryParams.limit}
              suppressPaginationPanel={true}
              loading={isLoading || isPending}
              enableCellTextSelection={true}
              onGridReady={onGridReady}
              theme={tableTheme}
              rowSelection={rowSelection}
              onSelectionChanged={handleSelectionChanged}
              overlayNoRowsTemplate={translatedNoDataMessage!}
              enableCharts={true}
            />
          </div>
        </Box>

        {/* Pagination */}
        {data?.count ? (
          <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 2 }}>
            <Pagination
              count={Math.ceil(data.count / (queryParams.limit || 100))}
              page={(queryParams.offset || 0) / (queryParams.limit || 100) + 1}
              onChange={handlePageChange}
              color='primary'
            />
          </Box>
        ) : null}
      </Box>
    </Card>
  )
}

export default BaseTable
