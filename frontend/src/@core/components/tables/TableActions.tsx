import { Can } from 'src/layouts/components/acl/Can'
import { Box, IconButton, Tooltip } from '@mui/material'
import { Icon } from '@iconify/react'
import Link from 'next/link'
import { useTranslation } from 'react-i18next'
import { useState } from 'react'
import ConfirmationModal from '../modals/ConfirmationModal'

interface TableActionsProps {
  id: string
  basePath: string // e.g., '/services/articles'
  onDelete?: (id: string) => void
  module: string // For permissions
  model: string // For permissions
  viewAction?: boolean
  editAction?: boolean
  deleteAction?: boolean
  transLationKey?: string
  customActions?: Array<{
    icon: string
    tooltip: string
    color?: string
    onClick: (id: string) => void
    show?: boolean
    permission?: {
      action: string
      module: string
      model: string
    }
  }>
}

const TableActions = ({
  id,
  basePath,
  onDelete,
  module,
  model,
  transLationKey,
  viewAction = true,
  editAction = true,
  deleteAction = true,
  customActions = []
}: TableActionsProps) => {
  const { t } = useTranslation()
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)

  const handleDeleteClick = () => {
    setDeleteDialogOpen(true)
  }

  const handleDeleteConfirm = () => {
    if (onDelete) {
      onDelete(id)
    }
    setDeleteDialogOpen(false)
  }

  const handleDeleteCancel = () => {
    setDeleteDialogOpen(false)
  }

  return (
    <>
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
        {viewAction && (
          <Can action='read' module={module} model={model}>
            <Tooltip title={t('common.actions.view')}>
              <IconButton size='small' color='info' component={Link} href={`${basePath}/${id}/view`}>
                <Icon icon='tabler:eye' fontSize={20} />
              </IconButton>
            </Tooltip>
          </Can>
        )}

        {editAction && (
          <Can action='update' module={module} model={model}>
            <Tooltip title={t('common.actions.edit')}>
              <IconButton size='small' color='success' component={Link} href={`${basePath}/${id}/edit`}>
                <Icon icon='tabler:edit' fontSize={20} />
              </IconButton>
            </Tooltip>
          </Can>
        )}

        {deleteAction && onDelete && (
          <Can action='delete' module={module} model={model}>
            <Tooltip title={t('common.actions.delete')}>
              <IconButton size='small' color='error' onClick={handleDeleteClick}>
                <Icon icon='tabler:trash' fontSize={20} />
              </IconButton>
            </Tooltip>
          </Can>
        )}

        {customActions.map((action, index) => {
          const ActionComponent = (
            <Tooltip key={index} title={action.tooltip}>
              <IconButton size='small' color={(action.color as any) || 'primary'} onClick={() => action.onClick(id)}>
                <Icon icon={action.icon} fontSize={20} />
              </IconButton>
            </Tooltip>
          )

          if (action.permission) {
            return (
              <Can
                key={index}
                action={action.permission.action}
                module={action.permission.module}
                model={action.permission.model}
              >
                {ActionComponent}
              </Can>
            )
          }

          return action.show !== false && ActionComponent
        })}
      </Box>

      <ConfirmationModal
        open={deleteDialogOpen}
        title={t(`tables.${transLationKey || model}.deleteDialog.title`)}
        message={t(`tables.${transLationKey || model}.deleteDialog.message`)}
        confirmButtonText={t(`tables.${transLationKey || model}.deleteDialog.confirm`) as string}
        cancelButtonText={t(`tables.${transLationKey || model}.deleteDialog.cancel`) as string}
        onConfirm={handleDeleteConfirm}
        onCancel={handleDeleteCancel}
      />
    </>
  )
}

export default TableActions
