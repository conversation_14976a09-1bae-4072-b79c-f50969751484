import React, { useEffect, useState } from 'react'
import { use<PERSON><PERSON>, Controller } from 'react-hook-form'
import { yupResolver } from '@hookform/resolvers/yup'
import * as yup from 'yup'
import { Box, Card, CardContent, CardHeader, Grid, But<PERSON> } from '@mui/material'
import CustomTextField from 'src/@core/components/mui/text-field'
import CustomAutocomplete from 'src/@core/components/mui/autocomplete'
import { useFetchPermissions } from 'src/hooks/useFetchPermission'
import { useCreateGroup, useUpdateGroup, useGroupById } from 'src/hooks/useGroups'
import toast from 'react-hot-toast'
import { useRouter } from 'next/router'
import { useQueryClient } from '@tanstack/react-query'
import LoadingButton from '@mui/lab/LoadingButton'
import { useTranslation } from 'react-i18next'
import FormButtons from '../components/form-buttons'

interface GroupFormProps {
  mode: 'create' | 'edit'
  groupId?: string
  onSuccess?: () => void
}

interface GroupFormData {
  name: string
  permissions: number[]
}

const schema = yup.object().shape({
  name: yup.string().required('Group name is required'),
  permissions: yup.array().min(1, 'At least one permission must be selected').required('Permissions are required')
})

const defaultValues: GroupFormData = {
  name: '',
  permissions: []
}

export const GroupForm = ({ mode, groupId, onSuccess }: GroupFormProps) => {
  const { t } = useTranslation()
  const [initialData, setInitialData] = useState<GroupFormData | null>(null)
  const router = useRouter()
  const queryClient = useQueryClient()

  const { data: groupData, isLoading: isLoadingGroup } = useGroupById(groupId || '')

  const { data: permissions, isLoading: isLoadingPermissions } = useFetchPermissions({
    limit: 1000,
    offset: 0
  })

  const { mutateAsync: createGroup, isPending: isCreating } = useCreateGroup({
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['groups'] })
      onSuccess?.()
    }
  })

  const { mutateAsync: updateGroup, isPending: isUpdating } = useUpdateGroup(groupId || '', {
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['groups'] })
      onSuccess?.()
    }
  })

  const {
    control,
    handleSubmit,
    reset,
    formState: { errors }
  } = useForm<GroupFormData>({
    resolver: yupResolver(schema),
    defaultValues: initialData || defaultValues
  })

  useEffect(() => {
    if (mode === 'edit' && groupData) {
      setInitialData({
        name: groupData.name,
        permissions: groupData.permissions.map((p: any) => p.id)
      })
      reset({
        name: groupData.name,
        permissions: groupData.permissions.map((p: any) => p.id)
      })
    }
  }, [groupData, mode, reset])

  const handleSave = async (formData: GroupFormData) => {
    try {
      if (mode === 'create') {
        const response = await createGroup(formData)
        toast.success(t('groups.form.messages.create_success'))

        return response
      } else {
        const response = await updateGroup(formData)
        toast.success(t('groups.form.messages.update_success'))

        return response
      }
    } catch (error) {
      console.error(error)
      toast.error(mode === 'create' ? t('groups.form.messages.create_error') : t('groups.form.messages.update_error'))
      throw error
    }
  }

  const handleSaveAndReturn = async (formData: GroupFormData) => {
    try {
      await handleSave(formData)
      router.push('/user-management/groups')
    } catch (error) {
      // Error handled in handleSave
    }
  }

  const handleSaveAndEdit = async (formData: GroupFormData) => {
    try {
      const group = await handleSave(formData)
      if (mode === 'create' && group?.id) {
        router.push(`/user-management/groups/${group.id}/edit`)
      }
    } catch (error) {
      // Error handled in handleSave
    }
  }

  const handleSaveAndAddNew = async (formData: GroupFormData) => {
    try {
      await handleSave(formData)
      if (mode === 'create') {
        reset(defaultValues)
      }
    } catch (error) {
      // Error handled in handleSave
    }
  }

  if (mode === 'edit' && isLoadingGroup) {
    return <div>Loading...</div>
  }

  const isLoading = isCreating || isUpdating || isLoadingPermissions

  return (
    <>
      <Card>
        <CardHeader title={t(mode === 'create' ? 'groups.form.title.create' : 'groups.form.title.edit')} />
        <CardContent>
          <form onSubmit={e => e.preventDefault()}>
            <Grid container>
              <Grid container spacing={5}>
                <Grid item xs={4} mb={5}>
                  <Controller
                    name='name'
                    control={control}
                    render={({ field }) => (
                      <CustomTextField
                        fullWidth
                        label={t('groups.form.fields.name')}
                        placeholder={t('groups.form.fields.name_placeholder') as string}
                        error={Boolean(errors.name)}
                        helperText={errors.name?.message}
                        {...field}
                      />
                    )}
                  />
                </Grid>
              </Grid>

              <Grid container spacing={5}>
                <Grid item xs={4}>
                  <Controller
                    name='permissions'
                    control={control}
                    render={({ field: { value, onChange } }) => (
                      <CustomAutocomplete
                        multiple
                        fullWidth
                        value={permissions?.results?.filter(permission => value.includes(permission.id)) || []}
                        options={permissions?.results || []}
                        loading={isLoadingPermissions}
                        id='permissions-select'
                        onChange={(_, newValue) => onChange(newValue.map(item => item.id))}
                        getOptionLabel={option => `${option.name} (${option.codename})`}
                        renderInput={params => (
                          <CustomTextField
                            {...params}
                            label={t('groups.form.fields.permissions')}
                            placeholder={t('groups.form.fields.permissions_placeholder') as string}
                            error={Boolean(errors.permissions)}
                            helperText={errors.permissions?.message}
                          />
                        )}
                      />
                    )}
                  />
                </Grid>
              </Grid>
            </Grid>

            <Box sx={{ mt: 5, display: 'flex', justifyContent: 'flex-end' }}>
              <FormButtons
                isLoading={isLoading}
                mode={mode}
                cancelPath='/user-management/groups'
                onSave={handleSubmit(handleSaveAndReturn)}
                onSaveAndEdit={handleSubmit(handleSaveAndEdit)}
                onSaveAndAddNew={handleSubmit(handleSaveAndAddNew)}
              />
            </Box>
          </form>
        </CardContent>
      </Card>
    </>
  )
}
