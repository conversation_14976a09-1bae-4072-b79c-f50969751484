import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useTranslation } from 'react-i18next'
import { Card, CardHeader, CardContent, Button, Box, FormControl, Grid, CircularProgress } from '@mui/material'
import CustomTextField from 'src/@core/components/mui/text-field'
import CustomAutocomplete from 'src/@core/components/mui/autocomplete'
import toast from 'react-hot-toast'
import debounce from 'lodash/debounce'

interface User {
  id: string | number
  name: string
  email: string
}

const CreateNotificationForm = () => {
  const router = useRouter()
  const { t } = useTranslation()

  // const { mutateAsync: createNotification } = useCreateNotification()

  // States
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')
  const [open, setOpen] = useState(false)
  const [users, setUsers] = useState<User[]>([])
  const [loading, setLoading] = useState(false)

  const [formData, setFormData] = useState({
    title: '',
    content: '',
    type: 'NEW',
    userId: null as string | number | null
  })

  // Fetch users with debounce
  const fetchUsers = async (search: string) => {
    setLoading(true)
    try {
      const response = await fetch(`/api/users/search?q=${search}`)
      const data = await response.json()
      setUsers(data.results)
    } catch (error) {
      console.error('Error fetching users:', error)
    } finally {
      setLoading(false)
    }
  }

  const debouncedFetch = debounce(fetchUsers, 300)

  useEffect(() => {
    if (!open) {
      setUsers([])
    }
  }, [open])

  useEffect(() => {
    if (searchTerm && open) {
      debouncedFetch(searchTerm)
    }
  }, [searchTerm, open])

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)

    try {
      // await createNotification(formData)
      toast.success(t('tables.notification.createSuccess'))
      router.push('/notifications')
    } catch (error) {
      toast.error(t('tables.notification.createError'))
    } finally {
      setIsSubmitting(false)
    }
  }

  const notificationTypes = [
    { value: 'NEW', label: t('tables.notification.new') },
    { value: 'UPDATE', label: t('tables.notification.update') }
  ]

  return (
    <Card>
      <CardHeader title={t('tables.notification.createButton')} />
      <CardContent>
        <form onSubmit={handleSubmit}>
          <Grid container spacing={5}>
            <Grid item xs={12}>
              <CustomTextField
                fullWidth
                name='title'
                label={t('tables.notification.columns.title')}
                value={formData.title}
                onChange={handleChange}
                error={false}
                required
              />
            </Grid>

            <Grid item xs={12}>
              <CustomTextField
                fullWidth
                multiline
                rows={4}
                name='content'
                label={t('tables.notification.columns.detail')}
                value={formData.content}
                onChange={handleChange}
                error={false}
                required
              />
            </Grid>

            <Grid item xs={12}>
              <CustomAutocomplete
                fullWidth
                options={notificationTypes}
                id='notification-type'
                value={notificationTypes.find(type => type.value === formData.type) || null}
                onChange={(_, newValue) => {
                  setFormData(prev => ({
                    ...prev,
                    type: newValue?.value || 'NEW'
                  }))
                }}
                getOptionLabel={option => option.label}
                renderInput={params => (
                  <CustomTextField {...params} label={t('tables.notification.columns.type')} required />
                )}
              />
            </Grid>

            <Grid item xs={12}>
              <CustomAutocomplete
                open={open}
                onOpen={() => setOpen(true)}
                onClose={() => setOpen(false)}
                loading={loading}
                options={users}
                onChange={(_, newValue) => {
                  setFormData(prev => ({
                    ...prev,
                    userId: newValue?.id || null
                  }))
                }}
                onInputChange={(_, value) => setSearchTerm(value)}
                getOptionLabel={option => option.name || ''}
                isOptionEqualToValue={(option, value) => option.id === value.id}
                renderInput={params => (
                  <CustomTextField
                    {...params}
                    label={t('tables.notification.columns.user')}
                    InputProps={{
                      ...params.InputProps,
                      endAdornment: (
                        <>
                          {loading ? <CircularProgress color='inherit' size={20} /> : null}
                          {params.InputProps.endAdornment}
                        </>
                      )
                    }}
                  />
                )}
              />
            </Grid>

            <Grid item xs={12}>
              <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 2 }}>
                <Button variant='outlined' color='secondary' onClick={() => router.push('/notifications')}>
                  {t('common.cancel')}
                </Button>
                <Button type='submit' variant='contained' disabled={isSubmitting}>
                  {t('common.save')}
                </Button>
              </Box>
            </Grid>
          </Grid>
        </form>
      </CardContent>
    </Card>
  )
}

export default CreateNotificationForm
