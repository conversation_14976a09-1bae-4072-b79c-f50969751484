import { Fragment, useEffect } from 'react'
import { useRouter } from 'next/router'
import { Card, CardContent, CardHeader, Box, CircularProgress, <PERSON><PERSON>, <PERSON><PERSON> } from '@mui/material'
import { useForm, Controller } from 'react-hook-form'
import { yupResolver } from '@hookform/resolvers/yup'
import CustomTextField from 'src/@core/components/mui/text-field'
import CustomAutocomplete from 'src/@core/components/mui/autocomplete'
import { useArticles } from 'src/hooks/_services/useArticles'
import { useCreateProduct, useEditProduct } from 'src/hooks/_services/useProduct'
import toast from 'react-hot-toast'
import { useQueryClient } from '@tanstack/react-query'
import { useTranslation } from 'react-i18next'
import FormActions from 'src/@core/components/form-buttons'
import { productSchema } from 'src/lib/schemas'
import { ProductFormData, SingleProductResponse } from 'src/types/models/_services/products'
import ErrorComponent from './components/error-component'

interface ProductFormProps {
  mode: 'create' | 'edit'
  initialData?: any
  productId?: string
  onSuccess?: () => void
}

const ProductForm = ({ mode, initialData, productId, onSuccess }: ProductFormProps) => {
  const router = useRouter()
  const queryClient = useQueryClient()
  const { t } = useTranslation()

  const {
    data: servicesData,
    isLoading: isLoadingServices,
    isError: isServicesError
  } = useArticles({
    limit: 100,
    offset: 0
  })

  const {
    control,
    handleSubmit,
    reset,
    formState: { errors }
  } = useForm<ProductFormData>({
    resolver: yupResolver(productSchema),
    defaultValues: {
      name: '',
      service_id: ''
    }
  })

  useEffect(() => {
    if (initialData) {
      reset({
        name: initialData.name,
        service_id: initialData.service_id
      })
    }
  }, [reset, initialData])

  const createProduct = useCreateProduct({
    onSuccess: () => {
      toast.success(t('product.form.create_success'))
      queryClient.invalidateQueries({ queryKey: ['products'] })
      onSuccess?.()
    },
    onError: () => {
      toast.error(t('product.form.create_error'))
    }
  })

  const editProduct = useEditProduct(productId || '', {
    onSuccess: () => {
      toast.success(t('product.form.update_success'))
      queryClient.invalidateQueries({ queryKey: ['products'] })
      onSuccess?.()
    },
    onError: () => {
      toast.error(t('product.form.update_error'))
    }
  })

  const handleSave = async (formData: ProductFormData) => {
    try {
      if (mode === 'create') {
        return await createProduct.mutateAsync({
          name: formData.name,
          service_id: formData.service_id
        })
      } else {
        return await editProduct.mutateAsync({
          name: formData.name,
          service_id: formData.service_id
        })
      }
    } catch (error) {
      // Error handled by mutation callbacks
    }
  }

  const handleSaveAndReturn = (formData: ProductFormData) => {
    handleSave(formData)
    router.push('/services/products')
  }

  const handleSaveAndEdit = async (formData: ProductFormData) => {
    const product = await handleSave(formData)
    if (mode === 'create' && product?.id) {
      router.push(`/services/products/${product.id}/update`)
    }
  }

  const handleSaveAndAddNew = async (formData: ProductFormData) => {
    handleSave(formData)
    if (mode === 'create') {
      reset()
    }
  }

  if (isServicesError) return <ErrorComponent message={t('product.form.loading_services_error') as string} />

  const isLoading = isLoadingServices || createProduct.isPending || editProduct.isPending

  return (
    <Card>
      <CardHeader title={t(`product.form.${mode === 'create' ? 'create_title' : 'edit_title'}`)} />
      <CardContent>
        <form onSubmit={e => e.preventDefault()}>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 5 }}>
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 4 }}>
              <Controller
                name='name'
                control={control}
                render={({ field }) => (
                  <CustomTextField
                    fullWidth
                    label={t('product.form.name')}
                    {...field}
                    error={Boolean(errors.name)}
                    helperText={errors.name?.message}
                    disabled={isLoading}
                  />
                )}
              />

              <Controller
                name='service_id'
                control={control}
                render={({ field: { value, onChange, ...field } }) => (
                  <CustomAutocomplete
                    {...field}
                    options={servicesData?.results || []}
                    loading={isLoadingServices}
                    id='service-autocomplete'
                    value={servicesData?.results?.find((service: any) => service.id === value) || null}
                    onChange={(_, newValue) => onChange(newValue ? newValue.id : '')}
                    getOptionLabel={option => option.label || ''}
                    isOptionEqualToValue={(option, value) => option.id === value.id}
                    renderInput={params => (
                      <CustomTextField
                        {...params}
                        label={t('product.form.service')}
                        error={Boolean(errors.service_id)}
                        helperText={errors.service_id?.message}
                        InputProps={{
                          ...params.InputProps,
                          endAdornment: (
                            <Fragment>
                              {isLoadingServices ? <CircularProgress size={20} /> : null}
                              {params.InputProps.endAdornment}
                            </Fragment>
                          )
                        }}
                      />
                    )}
                  />
                )}
              />
            </Box>

            <FormActions
              isLoading={isLoading}
              mode={mode}
              cancelPath='/services/products'
              onSave={handleSubmit(handleSaveAndReturn)}
              onSaveAndEdit={mode === 'create' ? handleSubmit(handleSaveAndEdit) : undefined}
              onSaveAndAddNew={mode === 'create' ? handleSubmit(handleSaveAndAddNew) : undefined}
            />
          </Box>
        </form>
      </CardContent>
    </Card>
  )
}

export default ProductForm
