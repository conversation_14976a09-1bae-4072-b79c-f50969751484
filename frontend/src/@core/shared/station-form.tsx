import type React from 'react'
import { use<PERSON><PERSON>, Controller } from 'react-hook-form'
import { yupResolver } from '@hookform/resolvers/yup'
import * as yup from 'yup'
import { Box, Card, CardContent, CardHeader, Grid, Switch, FormControlLabel, MenuItem } from '@mui/material'
import CustomTextField from 'src/@core/components/mui/text-field'
import FormButtons from 'src/@core/components/form-buttons'
import { useCreateStation, useUpdateStation } from 'src/hooks/useStation'
import { useTranslation } from 'react-i18next'
import { useQueryClient } from '@tanstack/react-query'
import { useRouter } from 'next/router'
import type { StationRequestPayload } from 'src/types/models/_services/stations'
import toast from 'react-hot-toast'

export const cities = [
  { value: 'dschang', label: 'Dschang' },
  { value: 'douala', label: 'Douala' },
  { value: 'yaounde', label: 'Yaoundé' },
  { value: 'bamenda', label: '<PERSON><PERSON><PERSON>' },
  { value: 'bafoussam', label: 'Ba<PERSON>uss<PERSON>' },
  { value: 'garoua', label: 'Garo<PERSON>' },
  { value: 'maroua', label: 'Maroua' },
  { value: 'ngaoundere', label: 'Ngaoundéré' },
  { value: 'nkongsamba', label: 'Nkongsamba' },
  { value: 'buea', label: 'Buea' },
  { value: 'kousseri', label: 'Kousseri' },
  { value: 'bertoua', label: 'Bertoua' },
  { value: 'tiko', label: 'Tiko' },
  { value: 'ebolowa', label: 'Ebolowa' },
  { value: 'mbouda', label: 'Mbouda' },
  { value: 'bafia', label: 'Bafia' },
  { value: 'limbe', label: 'Limbe' }
]

interface StationFormProps {
  initialData?: any
  mode?: 'create' | 'edit'
  stationId?: string
}

const stationSchema = yup.object().shape({
  station_name: yup.string().required('Station name is required'),
  station_localisation: yup.string().required('Station localisation is required'),
  email: yup.string().email('Invalid email').required('Email is required'),
  active: yup.boolean()
})

const StationForm: React.FC<StationFormProps> = ({ initialData, mode = 'create', stationId }) => {
  const { t } = useTranslation()
  const queryClient = useQueryClient()
  const router = useRouter()

  const {
    control,
    handleSubmit,
    formState: { errors },
    reset
  } = useForm<StationRequestPayload>({
    defaultValues: initialData || {
      station_name: '',
      station_localisation: '',
      email: '',
      active: true
    },
    resolver: yupResolver(stationSchema)
  })

  const {
    mutateAsync: createStation,
    isPending: isCreating,
    data: createStationRes
  } = useCreateStation({
    onSuccess: () => {
      toast.success(t('forms.stationForm.messages.create_success'))
      queryClient.invalidateQueries({ queryKey: ['stations'] })
    },
    onError: () => {
      toast.error(t('forms.stationForm.messages.create_error'))
    }
  })
  const { mutate: updateStation, isPending: isUpdating } = useUpdateStation({
    onSuccess: () => {
      toast.success(t('forms.stationForm.messages.update_success'))
      queryClient.invalidateQueries({ queryKey: ['stations'] })
    },
    onError: () => {
      toast.error(t('forms.stationForm.messages.update_error'))
    }
  })

  const onSubmit = (data: StationRequestPayload) => {
    if (mode === 'create') {
      createStation(data)
    } else {
      updateStation({ id: stationId!, data })
    }
  }

  const handleSaveAndEdit = (data: StationRequestPayload) => {
    onSubmit(data)
    if (mode === 'create' && createStationRes?.id) {
      router.push(`/fuel/stations/${createStationRes.id}/edit`)
    }
  }

  const handleSaveAndAddNew = (data: StationRequestPayload) => {
    if (mode === 'create') {
      createStation(data)
      reset()
    } else {
      updateStation({ id: stationId!, data })
    }
  }

  return (
    <Card>
      <CardHeader title={t(`forms.stationForm.${mode === 'create' ? 'create_title' : 'edit_title'}`)} />
      <CardContent>
        <form onSubmit={handleSubmit(onSubmit)}>
          <Grid container spacing={5}>
            <Grid item xs={12}>
              <Grid container spacing={5}>
                <Grid item xs={12} sm={4}>
                  <Controller
                    name='station_name'
                    control={control}
                    render={({ field }) => (
                      <CustomTextField
                        {...field}
                        fullWidth
                        label={t('forms.stationForm.fields.station_name')}
                        error={Boolean(errors.station_name)}
                        helperText={errors.station_name?.message as string}
                      />
                    )}
                  />
                </Grid>
                <Grid item xs={12} sm={4}>
                  <Controller
                    name='station_localisation'
                    control={control}
                    render={({ field }) => (
                      <CustomTextField
                        {...field}
                        fullWidth
                        select
                        label={t('forms.stationForm.fields.station_localisation')}
                        error={Boolean(errors.station_localisation)}
                        helperText={errors.station_localisation?.message as string}
                      >
                        {cities.map(city => (
                          <MenuItem key={city.value} value={city.label}>
                            {city.label}
                          </MenuItem>
                        ))}
                      </CustomTextField>
                    )}
                  />
                </Grid>
              </Grid>
            </Grid>

            <Grid item xs={12}>
              <Grid container spacing={5}>
                <Grid item xs={12} sm={4}>
                  <Controller
                    name='email'
                    control={control}
                    render={({ field }) => (
                      <CustomTextField
                        {...field}
                        fullWidth
                        label={t('forms.stationForm.fields.email')}
                        error={Boolean(errors.email)}
                        helperText={errors.email?.message as string}
                      />
                    )}
                  />
                </Grid>

                <Grid item xs={12} sm={4}>
                  <FormControlLabel
                    control={
                      <Controller name='active' control={control} render={({ field }) => <Switch {...field} />} />
                    }
                    label={t('forms.stationForm.fields.active')}
                  />
                </Grid>
              </Grid>
            </Grid>
          </Grid>
          <Box sx={{ mt: 10 }}>
            <FormButtons
              isLoading={isCreating || isUpdating}
              mode={mode}
              cancelPath='/fuel/stations'
              onSave={handleSubmit(onSubmit)}
              onSaveAndEdit={handleSubmit(handleSaveAndEdit)}
              onSaveAndAddNew={handleSubmit(handleSaveAndAddNew)}
            />
          </Box>
        </form>
      </CardContent>
    </Card>
  )
}

export default StationForm
