import React, { useEffect, useState } from 'react'
import { use<PERSON><PERSON>, Controller, useFieldArray } from 'react-hook-form'
import { yupResolver } from '@hookform/resolvers/yup'
import {
  Box,
  Card,
  CardContent,
  Grid,
  FormControlLabel,
  Switch,
  CardHeader,
  Button,
  Ty<PERSON>graphy,
  IconButton
} from '@mui/material'
import CustomTextField from 'src/@core/components/mui/text-field'
import { routeSchema } from 'src/lib/schemas'
import toast from 'react-hot-toast'
import { useTranslation } from 'react-i18next'
import { useQueryClient } from '@tanstack/react-query'
import { useRouter } from 'next/router'
import { CreateRoutePayload } from 'src/types/models/routes'
import FormButtons from 'src/@core/components/form-buttons'
import { useCreateRoute, useUpdateRoute } from 'src/hooks/helpers/useRoutes'
import AsyncInfiniteSelect from './components/infinite-select'
import { fetchInfiniteDestination, getDestinationById } from 'src/services/_service/destinations'
import { Icon } from '@iconify/react'
import { fetchInfiniteProduct } from 'src/services/_service/products'
import { getArticleById } from 'src/services/_service/article'

interface Props {
  initialData?: any
  mode?: 'create' | 'edit'
  pathId?: string
  onSuccess?: () => void
}

export default function ({ initialData, mode = 'create', pathId, onSuccess }: Props) {
  const router = useRouter()
  const queryClient = useQueryClient()
  const { t } = useTranslation()

  const {
    control,
    handleSubmit,
    formState: { errors },
    reset
  } = useForm<CreateRoutePayload>({
    defaultValues: {
      departure_id: '',
      destination_id: '',
      delay: 0,
      distance: 0,
      petrol_volume: 0,
      fees: '',
      is_active: true,
      is_city: false,
      nbr_ligne: 0,
      special_consumption: false,
      special_value: '',
      archive: false,
      pricing_list: [],
      ...initialData
    },
    resolver: yupResolver(routeSchema)
  })

  const { fields, append, remove, update } = useFieldArray({
    control,
    name: 'pricing_list'
  })
  const {
    mutateAsync: createPath,
    isPending: isCreating,
    data: createPathRes,
    isSuccess: isCreateSuccess
  } = useCreateRoute({
    onSuccess: () => {
      toast.success(t('forms.routeForm.messages.create_success'))
      queryClient.invalidateQueries({ queryKey: ['routes'] })
      onSuccess?.()
    },
    onError: () => {
      toast.error(t('forms.routeForm.messages.create_error'))
    }
  })

  const { mutateAsync: updatePath, isPending: isUpdating } = useUpdateRoute(pathId || '', {
    onSuccess: () => {
      toast.success(t('forms.routeForm.messages.update_success'))
      queryClient.invalidateQueries({ queryKey: ['routes'] })
      onSuccess?.()
    },
    onError: () => {
      toast.error(t('forms.routeForm.messages. update_error'))
    }
  })

  const onSubmit = async (data: any) => {
    try {
      if (mode === 'create') {
        createPath(data)
        if (isCreateSuccess) router.push('/settings/routes')
      } else {
        updatePath(data)
      }
    } catch (error) {
      console.error(error)
    }
  }

  const handleSaveAndAddNew = async (data: any) => {
    try {
      await onSubmit(data)
      if (mode === 'create' && isCreateSuccess) reset()
    } catch (error) {
      // Error handled in onSubmit
    }
  }

  const handleSaveAndEdit = async (data: any) => {
    try {
      const path = await onSubmit(data)
      if (mode === 'create' && createPathRes?.id && isCreateSuccess) {
        router.push(`/services/routes/${createPathRes.id}/edit`)
      }
    } catch (error) {
      // Error handled in onSubmit
    }
  }

  //   const handleDeleteConfirm = async () => {
  //     if (mode === 'edit' && rowToDelete) await deleteRoute({ id: rowToDelete })
  //   }

  //   const handleDeleteCancel = () => {
  //     setDeleteDialogOpen(false)
  //   }

  return (
    <form onSubmit={e => e.preventDefault()}>
      <Card>
        <CardHeader title={t(`forms.routeForm.${mode === 'create' ? 'create_title' : 'edit_title'}`)} />
        <CardContent>
          <Grid container spacing={5}>
            {/* Form fields in two-column grid */}
            <Grid item xs={12} sm={8}>
              <Grid container spacing={5}>
                <Grid item xs={12} sm={6}>
                  <Controller
                    name='departure_id'
                    control={control}
                    render={({ field }) => (
                      <AsyncInfiniteSelect
                        label={t('forms.routeForm.fields.departure') as string}
                        value={field.value}
                        onChange={field.onChange}
                        isRequired
                        queryKey={'destinations'}
                        fetchByIdFn={getDestinationById}
                        getOptionLabel={destination => destination.name}
                        fetchFn={fetchInfiniteDestination}
                        placeholder={'search departure..'}
                        size='small'
                        minSearchLength={2}
                        debounceMs={400}
                        prefetch
                      />
                    )}
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Controller
                    name='destination_id'
                    control={control}
                    render={({ field }) => (
                      <AsyncInfiniteSelect
                        label={t('forms.routeForm.fields.destination') as string}
                        value={field.value}
                        onChange={field.onChange}
                        isRequired
                        queryKey={'destinations'}
                        fetchByIdFn={getDestinationById}
                        getOptionLabel={destination => destination.name}
                        fetchFn={fetchInfiniteDestination}
                        placeholder={'search destination..'}
                        size='small'
                        minSearchLength={2}
                        debounceMs={400}
                        prefetch
                      />
                    )}
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Controller
                    name='delay'
                    control={control}
                    render={({ field }) => (
                      <CustomTextField
                        fullWidth
                        type='number'
                        label={t('forms.routeForm.fields.delay')}
                        error={Boolean(errors.delay)}
                        helperText={
                          errors.delay?.message ? errors.delay?.message : t('forms.routeForm.description.delay')
                        }
                        {...field}
                      />
                    )}
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Controller
                    name='distance'
                    control={control}
                    render={({ field }) => (
                      <CustomTextField
                        fullWidth
                        type='number'
                        label={t('forms.routeForm.fields.distance')}
                        error={Boolean(errors.distance)}
                        helperText={
                          errors.distance?.message
                            ? errors.distance?.message
                            : t('forms.routeForm.description.distance')
                        }
                        {...field}
                      />
                    )}
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Controller
                    name='petrol_volume'
                    control={control}
                    render={({ field }) => (
                      <CustomTextField
                        fullWidth
                        type='number'
                        label={t('forms.routeForm.fields.petrol_volume')}
                        error={Boolean(errors.petrol_volume)}
                        helperText={
                          errors.petrol_volume?.message
                            ? errors.petrol_volume?.message
                            : t('forms.routeForm.description.petrol_volume')
                        }
                        {...field}
                      />
                    )}
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Controller
                    name='fees'
                    control={control}
                    render={({ field }) => (
                      <CustomTextField
                        fullWidth
                        label={t('forms.routeForm.fields.fees')}
                        error={Boolean(errors.fees)}
                        helperText={errors.fees?.message}
                        {...field}
                      />
                    )}
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Controller
                    name='nbr_ligne'
                    control={control}
                    render={({ field }) => (
                      <CustomTextField
                        fullWidth
                        type='number'
                        label={t('forms.routeForm.fields.nbr_ligne')}
                        error={Boolean(errors.nbr_ligne)}
                        helperText={errors.nbr_ligne?.message}
                        {...field}
                      />
                    )}
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Controller
                    name='special_value'
                    control={control}
                    render={({ field }) => (
                      <CustomTextField
                        fullWidth
                        label={t('forms.routeForm.fields.special_value')}
                        error={Boolean(errors.special_value)}
                        helperText={errors.special_value?.message || t('forms.routeForm.description.special_value')}
                        {...field}
                      />
                    )}
                  />
                </Grid>
              </Grid>
            </Grid>

            {/* Switches: full-width rows */}
            <Grid item xs={12}>
              <FormControlLabel
                control={
                  <Controller
                    name='is_active'
                    control={control}
                    render={({ field }) => <Switch {...field} checked={field.value} />}
                  />
                }
                label={t('forms.routeForm.fields.is_active')}
              />
            </Grid>
            <Grid item xs={12}>
              <FormControlLabel
                control={
                  <Controller
                    name='is_city'
                    control={control}
                    render={({ field }) => <Switch {...field} checked={field.value} />}
                  />
                }
                label={t('forms.routeForm.fields.is_city')}
              />
            </Grid>
            <Grid item xs={12}>
              <FormControlLabel
                control={
                  <Controller
                    name='special_consumption'
                    control={control}
                    render={({ field }) => <Switch {...field} checked={field.value} />}
                  />
                }
                label={t('forms.routeForm.fields.special_consumption')}
              />
            </Grid>
            <Grid item xs={12}>
              <FormControlLabel
                control={
                  <Controller
                    name='archive'
                    control={control}
                    render={({ field }) => <Switch {...field} checked={field.value} />}
                  />
                }
                label={t('forms.routeForm.fields.archive')}
              />
            </Grid>
          </Grid>

          <Grid container spacing={5} sx={{ mt: 2 }}>
            <Grid item xs={12}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Typography variant='h6'>{t('forms.routeForm.fields.pricing_list')}</Typography>
                <Button
                  variant='contained'
                  startIcon={<Icon icon='mdi:plus' />}
                  onClick={() => append({ good_type_id: '', unite_price: '' })}
                >
                  {t('forms.routeForm.add_price')}
                </Button>
              </Box>
              {fields.map((field, index) => (
                <Grid container spacing={3} key={field.id} sx={{ mb: 2 }}>
                  <Grid item xs={5}>
                    <Controller
                      name={`pricing_list.${index}.good_type_id`}
                      control={control}
                      render={({ field: { value, onChange } }) => (
                        <AsyncInfiniteSelect
                          label={t('forms.routeForm.fields.good_type') as string}
                          value={value}
                          onChange={onChange}
                          queryKey='goodTypes'
                          fetchByIdFn={getArticleById}
                          getOptionLabel={option => option.label}
                          fetchFn={fetchInfiniteProduct}
                          size='small'
                          prefetch
                          minSearchLength={2}
                          debounceMs={400}
                          error={Boolean(errors.pricing_list?.[index]?.good_type_id)}
                          helperText={errors.pricing_list?.[index]?.good_type_id?.message}
                        />
                      )}
                    />
                  </Grid>
                  <Grid item xs={5}>
                    <Controller
                      name={`pricing_list.${index}.unite_price`}
                      control={control}
                      render={({ field: { value, onChange } }) => (
                        <CustomTextField
                          fullWidth
                          type='number'
                          label={t('forms.routeForm.fields.price')}
                          value={value}
                          onChange={onChange}
                          error={Boolean(errors.pricing_list?.[index]?.unite_price)}
                          helperText={errors.pricing_list?.[index]?.unite_price?.message}
                        />
                      )}
                    />
                  </Grid>
                  <Grid item xs={2}>
                    <IconButton onClick={() => remove(index)} color='error'>
                      <Icon icon='tabler:trash' />
                    </IconButton>
                  </Grid>
                </Grid>
              ))}
            </Grid>
          </Grid>
        </CardContent>
      </Card>
      <FormButtons
        mode={mode}
        cancelPath='/settings/routes'
        isLoading={isCreating || isUpdating}
        onSave={handleSubmit(onSubmit)}
        onSaveAndEdit={handleSubmit(handleSaveAndEdit)}
        onSaveAndAddNew={handleSubmit(handleSaveAndAddNew)}
      />
    </form>
  )
}
