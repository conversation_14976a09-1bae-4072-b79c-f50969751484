import { useTranslation } from 'react-i18next'
import { Card, CardContent, CardHeader, Grid, TextField } from '@mui/material'
import { useRouter } from 'next/router'
import { useQueryClient } from '@tanstack/react-query'
import toast from 'react-hot-toast'
import { yupResolver } from '@hookform/resolvers/yup'
import { Controller, useForm } from 'react-hook-form'
import * as yup from 'yup'

import FormButtons from 'src/@core/components/form-buttons'
import { GroupArticleRequestPayload } from 'src/types/models/_services/group-article'
import CustomTextField from 'src/@core/components/mui/text-field'
import { useCreateNatureOfArticle, useUpdateNatureOfArticle } from 'src/hooks/helpers/useNatureOfArticles'
import { NatureOfArticleRequestPayload } from 'src/types/models/_services/nature-of-articles'

const schema = yup.object().shape({
  name: yup.string().required('Le nom du groupe est requis')
})

const defaultValues = {
  name: ''
}

interface NatureOfArticleFormProps {
  mode: 'create' | 'edit'
  natureOfArticleId?: string
  initialData?: any
  onSuccess?: () => void
}

export const NatureOfArticleForm = ({ mode, natureOfArticleId, onSuccess, initialData }: NatureOfArticleFormProps) => {
  const { t } = useTranslation()
  const router = useRouter()
  const queryClient = useQueryClient()

  const {
    control,
    handleSubmit,
    reset,
    formState: { errors }
  } = useForm<NatureOfArticleRequestPayload>({
    resolver: yupResolver(schema),
    defaultValues: initialData || defaultValues
  })

  const {
    mutateAsync: createArticleGroup,
    isPending: isCreating,
    data: createArticleGroupRes
  } = useCreateNatureOfArticle({
    onSuccess: () => {
      toast.success(t('forms.natureOfArticleForm.messages.create_success'))
      queryClient.invalidateQueries({ queryKey: ['articleGroups'] })
      onSuccess?.()
    },
    onError: () => {
      toast.error(t('forms.natureOfArticleForm.messages.create_error'))
    }
  })

  const {
    mutateAsync: updateArticleGroup,
    isPending: isUpdating,
    data: updateArticleGroupRes
  } = useUpdateNatureOfArticle(natureOfArticleId || '', {
    onSuccess: () => {
      toast.success(t('forms.natureOfArticleForm.messages.update_success'))
      queryClient.invalidateQueries({ queryKey: ['articleGroups'] })
      onSuccess?.()
    },
    onError: () => {
      toast.error(t('forms.natureOfArticleForm.messages.update_error'))
    }
  })

  const handleSave = async (formData: GroupArticleRequestPayload) => {
    try {
      if (mode === 'create') {
        await createArticleGroup(formData)
      } else {
        await updateArticleGroup(formData)
      }
    } catch (error) {
      // Error is handled by mutation callbacks
    }
  }

  const handleSaveAndReturn = async (formData: GroupArticleRequestPayload) => {
    try {
      await handleSave(formData)
      router.push('/services/nature-of-articles')
    } catch (error) {
      // Error handled by mutation callbacks
    }
  }

  const handleSaveAndEdit = async (formData: GroupArticleRequestPayload) => {
    try {
      await handleSave(formData)
      if (mode === 'create' && createArticleGroupRes?.id) {
        router.push(`/services/nature-of-articles/${createArticleGroupRes.id}/edit`)
      }
    } catch (error) {
      // Error handled in handleSave
    }
  }

  const handleSaveAndAddNew = async (formData: GroupArticleRequestPayload) => {
    try {
      await handleSave(formData)
      reset()
    } catch (error) {
      // Error handled by mutation callbacks
    }
  }

  const isLoading = isCreating || isUpdating

  return (
    <Card>
      <CardHeader title={t(`forms.natureOfArticleForm.${mode === 'create' ? 'create_title' : 'edit_title'}`)} />
      <CardContent>
        <form onSubmit={handleSubmit(handleSaveAndReturn)}>
          <Grid container spacing={5}>
            <Grid item xs={12} sm={4}>
              <Controller
                name='name'
                control={control}
                render={({ field }) => (
                  <CustomTextField
                    {...field}
                    fullWidth
                    label={t('forms.natureOfArticleForm.fields.name')}
                    error={Boolean(errors.name)}
                    helperText={errors.name?.message}
                  />
                )}
              />
            </Grid>
          </Grid>
          <FormButtons
            isLoading={isLoading}
            mode={mode}
            cancelPath='/services/nature-of-articles'
            onSave={handleSubmit(handleSaveAndReturn)}
            onSaveAndEdit={handleSubmit(handleSaveAndEdit)}
            onSaveAndAddNew={handleSubmit(handleSaveAndAddNew)}
          />
        </form>
      </CardContent>
    </Card>
  )
}
