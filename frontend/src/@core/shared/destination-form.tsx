import React, { useEffect, useState } from 'react'
import { useForm, Controller } from 'react-hook-form'
import { yupResolver } from '@hookform/resolvers/yup'
import * as yup from 'yup'
import {
  Box,
  Card,
  CardContent,
  CardHeader,
  Grid,
  Button,
  FormHelperText,
  Switch,
  FormControlLabel,
  RadioGroup,
  Radio,
  FormControl,
  FormLabel
} from '@mui/material'
import CustomTextField from 'src/@core/components/mui/text-field'
import { useCreateDestination, useDestinationById, useUpdateDestination } from 'src/hooks/_services/useDestinations'
import toast from 'react-hot-toast'
import { DestinationFormData, DestinationRequestPayload } from 'src/types/models/_services/destinations'
import LoadingButton from '@mui/lab/LoadingButton'
import CustomAutocomplete from 'src/@core/components/mui/autocomplete'
import { useCountries } from 'src/hooks/_services/useCountries'
import { useRouter } from 'next/router'
import { useQueryClient } from '@tanstack/react-query'
import { destinationSchema } from 'src/lib/schemas'

interface DestinationFormProps {
  mode: 'create' | 'edit'
  destinationId?: string
  onSuccess?: () => void
}

export const DestinationForm = ({ mode, destinationId, onSuccess }: DestinationFormProps) => {
  const [initialData, setInitialData] = useState<DestinationFormData | null>(null)
  const router = useRouter()
  const queryClient = useQueryClient()

  const { data: destinationData, isLoading: isLoadingDestination } = useDestinationById(destinationId || '')

  const { data: countriesData } = useCountries({
    limit: 100,
    offset: 0
  })

  const { mutateAsync: createDestination, isPending: isCreating } = useCreateDestination({
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['destinations'] })
      onSuccess && onSuccess()
    }
  })

  const { mutateAsync: updateDestination, isPending: isUpdating } = useUpdateDestination(destinationId || '', {
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['destinations'] })
      onSuccess && onSuccess()
    }
  })

  const {
    control,
    handleSubmit,
    reset,
    formState: { errors }
  } = useForm<DestinationFormData>({
    resolver: yupResolver(destinationSchema),
    defaultValues: initialData || {
      name: '',
      country: '',
      lat: '',
      long: '',
      type: 'NATIONAL',
      is_active: true,
      is_archived: false
    }
  })

  useEffect(() => {
    if (mode === 'edit' && destinationData) {
      setInitialData({
        name: destinationData.name,
        country: destinationData.country || '',
        lat: destinationData.lat || '',
        long: destinationData.long || '',
        type: 'NATIONAL',
        is_active: destinationData.is_active,
        is_archived: false
      })
      reset({
        name: destinationData.name,
        country: destinationData.country || '',
        lat: destinationData.lat || '',
        long: destinationData.long || '',
        type: 'NATIONAL',
        is_active: destinationData.is_active,
        is_archived: false
      })
    }
  }, [destinationData, mode, reset])

  const handleSave = async (formData: DestinationFormData) => {
    try {
      const payload: DestinationRequestPayload = {
        name: formData.name,
        country: formData.country,
        lat: parseFloat(formData.lat),
        long: parseFloat(formData.long),
        is_active: formData.is_active,
        type: formData.type
      }

      if (mode === 'create') {
        const response = await createDestination(payload)
        toast.success('Destination créée avec succès')

        return response
      } else {
        const response = await updateDestination(payload)
        toast.success('Destination modifiée avec succès')

        return response
      }
    } catch (error) {
      console.error(error)
      toast.error('Une erreur est survenue')
      throw error
    }
  }

  const handleSaveAndReturn = async (formData: DestinationFormData) => {
    try {
      await handleSave(formData)
      router.push('/services/destinations')
    } catch (error) {
      // Error handled in handleSave
    }
  }

  const handleSaveAndEdit = async (formData: DestinationFormData) => {
    try {
      const destination = await handleSave(formData)
      if (mode === 'create' && destination?.id) {
        router.push(`/services/destinations/${destination.id}/update`)
      }
    } catch (error) {
      // Error handled in handleSave
    }
  }

  const handleSaveAndAddNew = async (formData: DestinationFormData) => {
    try {
      await handleSave(formData)
      if (mode === 'create') {
        reset({
          name: '',
          country: '',
          lat: '',
          long: '',
          type: 'NATIONAL',
          is_active: true,
          is_archived: false
        })
      }
    } catch (error) {
      // Error handled in handleSave
    }
  }

  if (mode === 'edit' && isLoadingDestination) {
    return <div>Chargement...</div>
  }

  return (
    <Card>
      <CardHeader title={mode === 'create' ? 'Créer une destination' : 'Modifier une destination'} />
      <CardContent>
        <form onSubmit={e => e.preventDefault()}>
          <Grid container spacing={5}>
            <Grid item xs={12}>
              <Controller
                name='name'
                control={control}
                render={({ field }) => (
                  <CustomTextField
                    fullWidth
                    label='Nom'
                    placeholder='Entrez le nom de la destination'
                    error={Boolean(errors.name)}
                    helperText={errors.name?.message}
                    {...field}
                  />
                )}
              />
            </Grid>

            <Grid item xs={12}>
              <Controller
                name='country'
                control={control}
                render={({ field: { value, onChange } }) => (
                  <CustomAutocomplete
                    fullWidth
                    options={countriesData?.results || []}
                    getOptionLabel={option => option.name}
                    renderInput={params => (
                      <CustomTextField
                        {...params}
                        label='Pays'
                        error={Boolean(errors.country)}
                        helperText={errors.country?.message}
                      />
                    )}
                    value={countriesData?.results.find((country: any) => country.name === value) || null}
                    onChange={(_, newValue) => {
                      onChange(newValue ? newValue.name : '')
                    }}
                  />
                )}
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <Controller
                name='long'
                control={control}
                render={({ field }) => (
                  <CustomTextField
                    fullWidth
                    type='number'
                    label='Longitude'
                    placeholder='0.0'
                    error={Boolean(errors.long)}
                    helperText={errors.long?.message}
                    {...field}
                    onChange={e => field.onChange(parseFloat(e.target.value))}
                  />
                )}
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <Controller
                name='lat'
                control={control}
                render={({ field }) => (
                  <CustomTextField
                    fullWidth
                    type='number'
                    label='Latitude'
                    placeholder='0.0'
                    error={Boolean(errors.lat)}
                    helperText={errors.lat?.message}
                    {...field}
                    onChange={e => field.onChange(parseFloat(e.target.value))}
                  />
                )}
              />
            </Grid>

            <Grid item xs={12}>
              <FormControl>
                <FormLabel>Type de destination</FormLabel>
                <Controller
                  name='type'
                  control={control}
                  render={({ field }) => (
                    <RadioGroup row {...field}>
                      <FormControlLabel value='NATIONAL' control={<Radio />} label='Nationale' />
                      <FormControlLabel value='INTERNATIONAL' control={<Radio />} label='Internationale' />
                    </RadioGroup>
                  )}
                />
                {errors.type && <FormHelperText error>{errors.type.message}</FormHelperText>}
              </FormControl>
            </Grid>

            <Grid item xs={12}>
              <FormControl>
                <Controller
                  name='is_active'
                  control={control}
                  render={({ field: { value, onChange } }) => (
                    <FormControlLabel control={<Switch checked={value} onChange={onChange} />} label='Activé' />
                  )}
                />
              </FormControl>
            </Grid>

            <Grid item xs={12}>
              <FormControl>
                <Controller
                  name='is_archived'
                  control={control}
                  render={({ field: { value, onChange } }) => (
                    <FormControlLabel control={<Switch checked={value} onChange={onChange} />} label='Archivé.e' />
                  )}
                />
              </FormControl>
            </Grid>

            <Grid item xs={12}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                <Button variant='outlined' color='secondary' onClick={() => onSuccess && onSuccess()}>
                  Annuler
                </Button>
                <Box sx={{ display: 'flex', gap: 2 }}>
                  <LoadingButton
                    loading={isCreating || isUpdating}
                    onClick={handleSubmit(handleSaveAndReturn)}
                    variant='contained'
                  >
                    Enregistrer et Retourner
                  </LoadingButton>
                  <LoadingButton
                    loading={isCreating || isUpdating}
                    onClick={handleSubmit(handleSaveAndEdit)}
                    variant='contained'
                  >
                    Enregistrer et Éditer
                  </LoadingButton>
                  {mode === 'create' && (
                    <LoadingButton loading={isCreating} onClick={handleSubmit(handleSaveAndAddNew)} variant='contained'>
                      Enregistrer et Ajouter Nouveau
                    </LoadingButton>
                  )}
                </Box>
              </Box>
            </Grid>
          </Grid>
        </form>
      </CardContent>
    </Card>
  )
}
