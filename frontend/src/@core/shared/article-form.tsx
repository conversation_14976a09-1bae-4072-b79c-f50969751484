import { Fragment } from 'react'
import { useRouter } from 'next/router'
import { Card, CardContent, CardHeader, Box, CircularProgress, <PERSON><PERSON>, <PERSON><PERSON> } from '@mui/material'
import { useForm, Controller } from 'react-hook-form'
import { yupResolver } from '@hookform/resolvers/yup'
import * as yup from 'yup'
import CustomTextField from 'src/@core/components/mui/text-field'
import CustomAutocomplete from 'src/@core/components/mui/autocomplete'
import Icon from 'src/@core/components/icon'
import toast from 'react-hot-toast'
import { useQueryClient } from '@tanstack/react-query'
import { useCreateArticle, useUpdateArticle } from 'src/hooks/_services/useArticles'
import { useOperationTypes } from 'src/hooks/_services/useOperationTypes'
import { useTranslation } from 'react-i18next'
import { useArticles } from 'src/hooks/_services/useArticles'
import { useDocumentTypes } from 'src/hooks/helpers/useDocumentType'
import { useGroupArticles } from 'src/hooks/helpers/useGroupArticles'
import { ArticleFormData } from 'src/types/models/_services/article'
import { articleSchema } from 'src/lib/schemas'
import FormButtons from '../components/form-buttons'

interface ArticleFormProps {
  mode: 'create' | 'edit'
  initialData?: ArticleFormData
  articleId?: string
  onSuccess?: () => void
}

const ArticleForm = ({ mode, initialData, articleId, onSuccess }: ArticleFormProps) => {
  const router = useRouter()
  const queryClient = useQueryClient()
  const { t } = useTranslation()

  const measureUnits = [
    { value: 'M3', label: t('article.form.units.cubicMeters') },
    { value: 'L', label: t('article.form.units.liters') },
    { value: 'KG', label: t('article.form.units.kilograms') },
    { value: 'T', label: t('article.form.units.tonnes') },
    { value: 'U', label: t('article.form.units.units') }
  ]

  const {
    control,
    handleSubmit,
    formState: { errors },
    reset
  } = useForm<ArticleFormData>({
    resolver: yupResolver(articleSchema),
    defaultValues: initialData || {
      code: '',
      label: '',
      related_documents: [],
      related_articles: [],
      operation_type: '',
      measure_unit: 'M3',
      group: 0
    }
  })

  const { data: articles, isLoading: isLoadingArticles } = useArticles({
    limit: 100,
    offset: 0
  })

  const { data: operationTypes, isLoading: isLoadingOperationTypes } = useOperationTypes({
    limit: 100,
    offset: 0
  })
  const {
    data: documentType,
    isLoading: loadingDocumentType,
    isError,
    isPending
  } = useDocumentTypes({
    limit: 100,
    offset: 0
  })
  const { data: articleGroup, isLoading: isLoadingGroups } = useGroupArticles({
    limit: 100,
    offset: 0
  })

  const createArticle = useCreateArticle({
    onSuccess: () => {
      toast.success(t('article.form.create_success'))
      queryClient.invalidateQueries({ queryKey: ['articles'] })
      onSuccess?.()
    },
    onError: () => {
      toast.error(t('article.form.create_error'))
    }
  })

  const updateArticle = useUpdateArticle({
    onSuccess: () => {
      toast.success(t('article.form.update_success'))
      queryClient.invalidateQueries({ queryKey: ['articles'] })
      onSuccess?.()
    },
    onError: () => {
      toast.error(t('article.form.update_error'))
    }
  })

  const handleSave = async (formData: ArticleFormData) => {
    if (mode === 'create') {
      return await createArticle.mutateAsync(formData)
    } else if (articleId) {
      return await updateArticle.mutateAsync({ id: articleId, data: formData })
    }
  }

  const handleSaveAndReturn = async (formData: ArticleFormData) => {
    try {
      await handleSave(formData)
      router.push('/services/articles')
    } catch (error) {
      // Error handled by mutation callbacks
    }
  }

  const handleSaveAndEdit = async (formData: ArticleFormData) => {
    try {
      const response = await handleSave(formData)
      if (mode === 'create' && response?.id) {
        router.push(`/services/articles/${response.id}/edit`)
      }
    } catch (error) {
      // Error handled by mutation callbacks
    }
  }

  const handleSaveAndAddNew = async (formData: ArticleFormData) => {
    try {
      await handleSave(formData)
      if (mode === 'create') {
        reset()
      }
    } catch (error) {}
  }

  const isLoading = isLoadingArticles || isLoadingOperationTypes || createArticle.isPending || updateArticle.isPending

  return (
    <Card>
      <CardHeader title={t(`article.form.${mode === 'create' ? 'create_title' : 'edit_title'}`)} />
      <CardContent>
        <form onSubmit={e => e.preventDefault()}>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 4 }}>
            <Controller
              name='code'
              control={control}
              render={({ field }) => (
                <CustomTextField
                  fullWidth
                  label={t('article.form.code')}
                  required
                  {...field}
                  error={Boolean(errors.code)}
                  helperText={errors.code?.message}
                  disabled={isLoading}
                />
              )}
            />

            <Controller
              name='label'
              control={control}
              render={({ field }) => (
                <CustomTextField
                  fullWidth
                  label={t('article.form.label')}
                  {...field}
                  required
                  error={Boolean(errors.label)}
                  helperText={errors.label?.message}
                  disabled={isLoading}
                />
              )}
            />

            <Controller
              name='related_articles'
              control={control}
              render={({ field: { value, onChange, ...field } }) => (
                <CustomAutocomplete
                  {...field}
                  multiple
                  options={articles?.results || []}
                  loading={isLoadingArticles}
                  value={articles?.results?.filter((article: any) => value?.includes(article.id)) || []}
                  onChange={(_, newValue) => onChange(newValue.map((item: any) => item.id))}
                  getOptionLabel={option => option.label || ''}
                  isOptionEqualToValue={(option, value) => option.id === value.id}
                  renderInput={params => (
                    <CustomTextField
                      {...params}
                      label={t('article.form.related_articles')}
                      error={Boolean(errors.related_articles)}
                      helperText={errors.related_articles?.message}
                    />
                  )}
                />
              )}
            />

            <Controller
              name='related_documents'
              control={control}
              render={({ field: { value, onChange, ...field } }) => (
                <CustomAutocomplete
                  {...field}
                  multiple
                  options={documentType?.results || []}
                  loading={loadingDocumentType}
                  value={documentType?.results?.filter((doc: any) => value?.includes(doc.id)) || []}
                  onChange={(_, newValue) => onChange(newValue.map((item: any) => item.id))}
                  getOptionLabel={option => option.name || ''}
                  isOptionEqualToValue={(option, value) => option.id === value.id}
                  renderInput={params => (
                    <CustomTextField
                      {...params}
                      label={t('article.form.related_documents')}
                      error={Boolean(errors.related_documents)}
                      helperText={errors.related_documents?.message}
                    />
                  )}
                />
              )}
            />

            <Controller
              name='operation_type'
              control={control}
              render={({ field: { value, onChange, ...field } }) => (
                <CustomAutocomplete
                  {...field}
                  options={operationTypes || []}
                  loading={isLoadingOperationTypes}
                  value={operationTypes?.find((type: any) => type.value === value) || null}
                  onChange={(_, newValue) => onChange(newValue ? newValue.value : '')}
                  getOptionLabel={option => option.label || ''}
                  isOptionEqualToValue={(option, value) => option.value === value.value}
                  renderInput={params => (
                    <CustomTextField
                      {...params}
                      label={t('article.form.operation_type')}
                      required
                      error={Boolean(errors.operation_type)}
                      helperText={errors.operation_type?.message}
                    />
                  )}
                />
              )}
            />

            <Controller
              name='measure_unit'
              control={control}
              render={({ field: { value, onChange, ...field } }) => (
                <CustomAutocomplete
                  {...field}
                  options={measureUnits}
                  value={measureUnits.find(unit => unit.value === value) || null}
                  onChange={(_, newValue) => onChange(newValue ? newValue.value : '')}
                  getOptionLabel={option => option.label}
                  isOptionEqualToValue={(option: any, value) => option.value === value}
                  renderInput={params => (
                    <CustomTextField
                      {...params}
                      label={t('article.form.measure_unit')}
                      error={Boolean(errors.measure_unit)}
                      helperText={errors.measure_unit?.message}
                    />
                  )}
                />
              )}
            />

            <Controller
              name='group'
              control={control}
              render={({ field: { value, onChange, ...field } }) => (
                <CustomAutocomplete
                  {...field}
                  options={articleGroup?.results || []}
                  loading={isLoadingGroups}
                  value={articleGroup?.results?.find((group: any) => group.id === value) || null}
                  onChange={(_, newValue) => onChange(newValue ? newValue.id : '')}
                  getOptionLabel={option => option.name || ''}
                  isOptionEqualToValue={(option, value) => option.id === value.id}
                  renderInput={params => (
                    <CustomTextField
                      {...params}
                      label={t('article.form.group')}
                      error={Boolean(errors.group)}
                      helperText={errors.group?.message}
                    />
                  )}
                />
              )}
            />

            <FormButtons
              isLoading={isLoading}
              mode={mode}
              cancelPath='/services/articles'
              onSave={handleSubmit(handleSaveAndReturn)}
              onSaveAndEdit={handleSubmit(handleSaveAndEdit)}
              onSaveAndAddNew={mode === 'create' ? handleSubmit(handleSaveAndAddNew) : undefined}
            />
          </Box>
        </form>
      </CardContent>
    </Card>
  )
}

export default ArticleForm
