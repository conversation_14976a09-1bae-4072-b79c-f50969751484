import { Alert, AlertTitle, AlertProps } from '@mui/material'

interface CustomAlertProps extends Omit<AlertProps, 'severity'> {
  title?: string
  message: string
  severity: 'success' | 'error' | 'warning' | 'info'
  variant?: 'standard' | 'filled' | 'outlined'
}

const CustomAlert = ({
  title,
  message,
  severity,
  variant = 'standard',
  sx,
  ...props
}: CustomAlertProps) => {
  return (
    <Alert 
      severity={severity}
      variant={variant}
      sx={{ mb: 4, ...sx }}
      {...props}
    >
      {title && <AlertTitle>{title}</AlertTitle>}
      {message}
    </Alert>
  )
}

export default CustomAlert