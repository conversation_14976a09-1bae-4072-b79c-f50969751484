import type React from 'react'
import { useState } from 'react'
import { Button, Menu, MenuItem, Typography } from '@mui/material'
import { Icon } from '@iconify/react'

interface CustomDropdownProps {
  options: {
    label: string
    icon: string
    onClick: () => void
    disabled: boolean
  }[]
  placeholder?: string
  disabled?: boolean

  buttonVariant?: 'text' | 'outlined' | 'contained'
  buttonColor?: 'primary' | 'secondary' | 'error' | 'info' | 'success' | 'warning' | 'inherit'
}

const CustomDropdown: React.FC<CustomDropdownProps> = ({
  options,
  placeholder = 'Select an option',
  disabled = false,
  buttonVariant = 'outlined',
  buttonColor = 'primary'
}) => {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null)
  const [selectedOption, setSelectedOption] = useState<string | null>(null)
  const open = Boolean(anchorEl)

  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget)
  }

  const handleClose = () => {
    setAnchorEl(null)
  }

  const handleMenuItemClick = (option: (typeof options)[0]) => {
    if (!option.disabled) {
      setSelectedOption(option.label)
      handleClose()
      option.onClick()
    }
  }

  return (
    <div>
      <Button
        variant={buttonVariant}
        color={buttonColor}
        disabled={disabled}
        onClick={handleClick}
        endIcon={<Icon icon='tabler:chevron-down' />}
        sx={{ minWidth: 200, justifyContent: 'space-between' }}
      >
        {selectedOption || placeholder}
      </Button>
      <Menu
        anchorEl={anchorEl}
        open={open}
        onClose={handleClose}
        MenuListProps={{
          'aria-labelledby': 'dropdown-button'
        }}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'left'
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'left'
        }}
      >
        {options.map(option => (
          <MenuItem
            key={option.label}
            disabled={option.disabled}
            onClick={() => handleMenuItemClick(option)}
            sx={{
              minWidth: 200,
              display: 'flex',
              alignItems: 'center',
              gap: 1,
              backgroundColor: selectedOption === option.label ? 'rgba(0, 0, 0, 0.04)' : 'transparent',
              '&:hover': {
                backgroundColor: 'rgba(0, 0, 0, 0.08)'
              }
            }}
          >
            <Icon icon={option.icon} style={{ fontSize: '1.25rem' }} />
            <Typography>{option.label}</Typography>
          </MenuItem>
        ))}
      </Menu>
    </div>
  )
}

export default CustomDropdown
