import { Grid, <PERSON>ert, <PERSON><PERSON>Title } from '@mui/material'

interface ErrorComponentProps {
  title?: string | null
  message?: string
  severity?: 'error' | 'warning' | 'info' | 'success'
}

const ErrorComponent = ({
  title,
  message = 'An error occurred. Please try again later.',
  severity = 'error'
}: ErrorComponentProps) => {
  return (
    <Grid container spacing={6}>
      <Grid item xs={12}>
        <Alert severity={severity}>
          {title && <AlertTitle>{title}</AlertTitle>}
          {message}
        </Alert>
      </Grid>
    </Grid>
  )
}

export default ErrorComponent
