import React, { useEffect } from 'react'
import {
  Autocomplete,
  Chip,
  CircularProgress,
  Box,
  Typography,
  AutocompleteProps,
  TextFieldProps,
  debounce
} from '@mui/material'
import { Icon } from '@iconify/react'
import CustomTextField from 'src/@core/components/mui/text-field'

export interface GenericMultiSelectProps<T> {
  options: T[]
  value: T[]
  onChange: (newValue: T[]) => void
  getOptionLabel: (option: T) => string
  isOptionEqualToValue: (option: T, value: T) => boolean

  label?: string
  placeholder?: string
  loading?: boolean
  hasMore?: boolean
  loadMore?: () => any

  renderOption?: (
    props: React.HTMLAttributes<HTMLLIElement>,
    option: T,
    state: { selected: boolean }
  ) => React.ReactNode
  renderTags?: (value: T[], getTagProps: (params: { index: number }) => {}) => React.ReactNode

  getOptionDescription?: (option: T) => string
  getOptionKey?: (option: T) => string | number

  sx?: any
  textFieldProps?: Partial<TextFieldProps>
  autocompleteProps?: Partial<Omit<AutocompleteProps<T, true, false, false>, 'renderInput' | 'options' | 'multiple'>>

  error?: boolean
  helperText?: React.ReactNode
  required?: boolean
  name?: string

  onSearchChange?: (search: string) => void
}

export default function GenericMultiSelect<T>({
  options,
  value,
  onChange,
  getOptionLabel,
  isOptionEqualToValue,
  label,
  placeholder = 'Select items',
  loading = false,
  hasMore = false,
  loadMore,
  renderOption,
  renderTags,
  getOptionDescription,
  getOptionKey = (option: any) => option?.id || Math.random().toString(36),
  sx,
  textFieldProps,
  autocompleteProps,
  error,
  helperText,
  required,
  name,
  onSearchChange
}: GenericMultiSelectProps<T>) {
  const [open, setOpen] = React.useState(false)
  const [inputValue, setInputValue] = React.useState('')
  const loadingRef = React.useRef(null)

  // Debounced search handler
  const debouncedSearchHandler = React.useMemo(
    () =>
      debounce((value: string) => {
        if (onSearchChange) {
          onSearchChange(value)
        }
      }, 300),
    [onSearchChange]
  )

  // Handle input value change for search
  const handleInputChange = (_event: React.SyntheticEvent, newInputValue: string) => {
    setInputValue(newInputValue)
    debouncedSearchHandler(newInputValue)
  }

  // Set up intersection observer for infinite scrolling
  useEffect(() => {
    if (!loadMore || !hasMore) return

    const observer = new IntersectionObserver(
      entries => {
        const first = entries[0]
        if (first.isIntersecting && hasMore && !loading && open) {
          loadMore()
        }
      },
      { threshold: 1.0 }
    )

    const currentRef = loadingRef.current
    if (currentRef) {
      observer.observe(currentRef)
    }

    return () => {
      if (currentRef) {
        observer.unobserve(currentRef)
      }
    }
  }, [loadingRef, hasMore, loading, open, loadMore])

  // Default render option function
  const defaultRenderOption = (
    props: React.HTMLAttributes<HTMLLIElement>,
    option: T,
    { selected }: { selected: boolean }
  ) => (
    <li {...props} key={getOptionKey(option)}>
      <Box
        component='span'
        sx={{
          width: 20,
          height: 20,
          mr: 1,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center'
        }}
      >
        {selected ? <Icon icon='tabler:check' /> : null}
      </Box>
      <Box sx={{ flexGrow: 1 }}>
        <Typography variant='body1'>{getOptionLabel(option)}</Typography>
        {getOptionDescription && (
          <Typography variant='body2' color='text.secondary'>
            {getOptionDescription(option)}
          </Typography>
        )}
      </Box>
    </li>
  )

  const defaultRenderTags = (value: T[], getTagProps: (params: { index: number }) => {}) =>
    value.map((option, index) => (
      <Chip key={getOptionKey(option)} label={getOptionLabel(option)} size='small' {...getTagProps({ index })} />
    ))

  return (
    <Box sx={{ width: '100%', ...sx }}>
      <Autocomplete
        multiple
        id={name ? `${name}-select` : 'generic-multi-select'}
        open={open}
        onOpen={() => setOpen(true)}
        onClose={() => setOpen(false)}
        options={options}
        loading={loading}
        getOptionLabel={getOptionLabel}
        isOptionEqualToValue={isOptionEqualToValue}
        value={value}
        onChange={(_, newValue) => onChange(newValue)}
        inputValue={inputValue}
        onInputChange={handleInputChange}
        renderTags={renderTags || defaultRenderTags}
        renderOption={renderOption || defaultRenderOption}
        renderInput={params => (
          <CustomTextField
            {...params}
            {...textFieldProps}
            name={name}
            label={label}
            placeholder={placeholder}
            error={error}
            helperText={helperText}
            required={required}
            InputProps={{
              ...params.InputProps,
              endAdornment: (
                <React.Fragment>
                  {loading ? <CircularProgress color='inherit' size={20} /> : null}
                  {params.InputProps.endAdornment}
                </React.Fragment>
              )
            }}
          />
        )}
        ListboxProps={{
          onScroll: event => {
            if (!loadMore || !hasMore) return

            const listboxNode = event.currentTarget
            if (
              listboxNode.scrollTop + listboxNode.clientHeight >= listboxNode.scrollHeight - 50 &&
              hasMore &&
              !loading
            ) {
              loadMore()
            }
          },
          ...autocompleteProps?.ListboxProps
        }}
        {...autocompleteProps}
      />

      {/* Invisible element for intersection observer */}
      {open && hasMore && loadMore && <Box ref={loadingRef} sx={{ height: 1, width: '100%' }} />}
    </Box>
  )
}
