import React, { useState, forwardRef, useImper<PERSON><PERSON><PERSON><PERSON>, useEffect } from 'react'
import {
  TextField,
  List,
  ListItem,
  Button,
  Box,
  Typography,
  Paper,
  Chip,
  Avatar,
  ListItemText,
  ListItemAvatar,
  FormControl,
  FormLabel,
  FormHelperText,
  CircularProgress
} from '@mui/material'
import { useInfiniteQuery } from '@tanstack/react-query'

// Generic type for any data item
export interface DataItem {
  id: string | number
  [key: string]: any
}

export interface DualListboxProps<T extends DataItem> {
  label?: string
  helperText?: string
  error?: boolean
  required?: boolean

  data?: T[]
  loading?: boolean
  value?: T[]
  queryKey?: string
  queryFn?: (params: { pageParam: number }) => Promise<{ items: T[]; nextPage: number | undefined }>

  // Display customization
  getItemLabel: (item: T) => string
  getItemId?: (item: T) => string | number
  getItemImage?: (item: T) => string
  getItemSecondary?: (item: T) => string
  chipLabel?: (item: T) => React.ReactNode
  filterFn?: (item: T, filter: string) => boolean

  // Callbacks
  onChange?: (selected: T[]) => void
  onSearch?: (filter: string) => void
}

export interface DualListboxRef<T extends DataItem> {
  getSelected: () => T[]
  setSelected: (items: T[]) => void
  reset: () => void
}

function GenericDualListbox<T extends DataItem>(
  {
    // Basic props
    label = 'Select Items',
    helperText,
    error = false,
    required = false,

    // Data props
    data,
    loading,
    value,

    // React Query props
    queryKey,
    queryFn,

    // Display customization
    getItemLabel,
    getItemId = item => item.id,
    getItemImage,
    getItemSecondary,
    chipLabel,
    filterFn = (item, filter) => getItemLabel(item).toLowerCase().includes(filter.toLowerCase()),

    // Callbacks
    onChange,
    onSearch
  }: DualListboxProps<T>,
  ref: React.Ref<DualListboxRef<T>>
) {
  const [selectedItems, setSelectedItems] = useState<T[]>(value || [])
  const [filter, setFilter] = useState('')

  // Update internal state when value prop changes (for controlled component)
  useEffect(() => {
    if (value) {
      setSelectedItems(value)
    }
  }, [value])

  // React Query for infinite scrolling (if queryKey and queryFn are provided)
  const queryEnabled = !!queryKey && !!queryFn
  const {
    data: queryData,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    status
  } = useInfiniteQuery({
    queryKey: queryKey ? [queryKey] : ['disabled-query'],
    queryFn: queryFn!,
    getNextPageParam: lastPage => lastPage.nextPage,
    initialPageParam: 0,
    enabled: queryEnabled
  })

  // Expose methods to parent component
  useImperativeHandle(ref, () => ({
    getSelected: () => selectedItems,
    setSelected: (items: T[]) => setSelectedItems(items),
    reset: () => setSelectedItems([])
  }))

  // Update parent component when selection changes
  useEffect(() => {
    onChange?.(selectedItems)
  }, [selectedItems, onChange])

  // Handle search filter changes
  useEffect(() => {
    onSearch?.(filter)
  }, [filter, onSearch])

  // Get all available items
  const allItems = React.useMemo(() => {
    if (data) return data
    if (!queryData) return []

    return queryData.pages.flatMap(page => page.items)
  }, [data, queryData])

  // Filter available items based on search and already selected
  const availableItems = React.useMemo(() => {
    return allItems.filter(
      item => !selectedItems.some(si => getItemId(si) === getItemId(item)) && filterFn(item, filter)
    )
  }, [allItems, selectedItems, filter, filterFn, getItemId])

  const handleAdd = (item: T) => {
    setSelectedItems(prev => [...prev, item])
  }

  const handleRemove = (item: T) => {
    setSelectedItems(prev => prev.filter(i => getItemId(i) !== getItemId(item)))
  }

  const handleAddAll = () => {
    setSelectedItems(prev => [...prev, ...availableItems])
  }

  const handleScroll = (e: React.UIEvent<HTMLDivElement>) => {
    if (!queryEnabled) return

    const { scrollTop, scrollHeight, clientHeight } = e.currentTarget

    // Load more when user scrolls to bottom (with a small buffer)
    if (scrollHeight - scrollTop <= clientHeight + 30 && hasNextPage && !isFetchingNextPage) {
      fetchNextPage()
    }
  }

  // Determine if we're in a loading state
  const isLoading = loading || (queryEnabled && status === 'pending')

  return (
    <FormControl fullWidth error={error} required={required} size='small'>
      {/* {label && (
        <FormLabel component='legend' sx={{ fontSize: '0.875rem' }}>
          {label}
        </FormLabel>
      )} */}
      <Box
        sx={{
          display: 'flex',
          flexDirection: { xs: 'column', sm: 'row' },
          gap: 1,
          mt: 0.5,
          mb: helperText ? 0 : 1
        }}
      >
        {/* Available Items */}
        <Paper
          sx={{
            width: { xs: '100%', sm: '50%' },
            p: 1,
            border: error ? '1px solid #d32f2f' : '1px solid #e0e0e0'
          }}
          variant='outlined'
        >
          <TextField
            value={filter}
            onChange={e => setFilter(e.target.value)}
            placeholder='Search...'
            fullWidth
            margin='dense'
            size='small'
            variant='outlined'
            sx={{ mb: 0.5 }}
          />
          <List
            component='div'
            sx={{
              height: 100,
              width: 350,
              overflow: 'auto',
              p: 0
            }}
            onScroll={(e: React.UIEvent<HTMLDivElement>) => handleScroll(e)}
            dense
          >
            {isLoading ? (
              <Box sx={{ display: 'flex', justifyContent: 'center', p: 1 }}>
                <CircularProgress size={20} />
              </Box>
            ) : status === 'error' ? (
              <Box sx={{ p: 1, textAlign: 'center' }}>
                <Typography variant='caption' color='error'>
                  Error loading data
                </Typography>
              </Box>
            ) : availableItems.length === 0 ? (
              <Box sx={{ p: 1 }}>
                <Typography variant='caption' color='text.secondary'>
                  {filter ? `No results for "${filter}"` : 'No items available'}
                </Typography>
              </Box>
            ) : (
              availableItems.map(item => (
                <ListItem
                  key={getItemId(item).toString()}
                  onClick={() => handleAdd(item)}
                  sx={{
                    cursor: 'pointer',
                    '&:hover': {
                      backgroundColor: 'rgba(0, 0, 0, 0.04)'
                    },
                    py: 0.25,
                    px: 1
                  }}
                >
                  <ListItemText
                    primary={
                      <Typography variant='caption' sx={{ display: 'block' }}>
                        {getItemLabel(item)}
                      </Typography>
                    }
                    secondary={
                      getItemSecondary && (
                        <Typography variant='caption' color='text.secondary' sx={{ display: 'block' }}>
                          {getItemSecondary(item)}
                        </Typography>
                      )
                    }
                    sx={{ m: 0 }}
                  />
                </ListItem>
              ))
            )}
            {isFetchingNextPage && (
              <Box sx={{ display: 'flex', justifyContent: 'center', p: 0.5 }}>
                <CircularProgress size={16} />
              </Box>
            )}
          </List>
        </Paper>

        {/* Selected Items */}
        <Paper
          sx={{
            width: { xs: '100%', sm: '50%' },
            p: 1,
            border: error ? '1px solid #d32f2f' : '1px solid #e0e0e0'
          }}
          variant='outlined'
        >
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'space-between',
              flexWrap: 'nowrap',
              alignItems: 'center',
              mb: 0.5
            }}
          >
            <Typography variant='caption' sx={{ fontWeight: 500 }}>
              Selected
            </Typography>
            <Box>
              <Button
                onClick={handleAddAll}
                color='primary'
                size='small'
                sx={{ minWidth: 'auto', p: '0px 4px', mr: 0.5, fontSize: '0.7rem' }}
                disabled={availableItems.length === 0}
              >
                Add All
              </Button>
              <Button
                onClick={() => setSelectedItems([])}
                color='error'
                size='small'
                sx={{ minWidth: 'auto', p: '0px 4px', fontSize: '0.7rem' }}
                disabled={selectedItems.length === 0}
              >
                Clear
              </Button>
            </Box>
          </Box>

          {/* Chips for selected items */}
          <Box
            sx={{
              p: 0.5,
              minHeight: '40px',
              maxHeight: '60px',
              overflow: 'auto',
              mb: 0.5
            }}
          >
            {selectedItems.length > 0 ? (
              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                {selectedItems.map(item => (
                  <Chip
                    key={getItemId(item).toString()}
                    avatar={getItemImage && <Avatar src={getItemImage(item)} sx={{ width: 16, height: 16 }} />}
                    label={chipLabel ? chipLabel(item) : getItemLabel(item)}
                    onDelete={() => handleRemove(item)}
                    color='primary'
                    size='small'
                    sx={{ m: 0.25, height: 20, '& .MuiChip-label': { px: 1, fontSize: '0.65rem' } }}
                  />
                ))}
              </Box>
            ) : (
              <Typography
                variant='caption'
                sx={{ color: 'text.secondary', display: 'block', textAlign: 'center', p: 0.5 }}
              >
                No items selected
              </Typography>
            )}
          </Box>

          {/* List of selected items */}
          {/* <List
            sx={{
              height: 100,
              overflow: 'auto',
              p: 0
            }}
            dense
          >
            {selectedItems.length > 0 ? (
              selectedItems.map(item => (
                <ListItem
                  key={getItemId(item).toString()}
                  onClick={() => handleRemove(item)}
                  sx={{
                    cursor: 'pointer',
                    '&:hover': {
                      backgroundColor: 'rgba(0, 0, 0, 0.04)'
                    },
                    py: 0.25,
                    px: 1
                  }}
                >
                  <ListItemText
                    primary={
                      <Typography variant='caption' sx={{ display: 'block' }}>
                        {getItemLabel(item)}
                      </Typography>
                    }
                    secondary={
                      getItemSecondary && (
                        <Typography variant='caption' color='text.secondary' sx={{ display: 'block' }}>
                          {getItemSecondary(item)}
                        </Typography>
                      )
                    }
                    sx={{ m: 0 }}
                  />
                </ListItem>
              ))
            ) : (
              <Box sx={{ p: 1, textAlign: 'center' }}>
                <Typography variant='caption' sx={{ color: 'text.secondary' }}>
                  No items selected
                </Typography>
              </Box>
            )}
          </List> */}
        </Paper>
      </Box>
      {helperText && <FormHelperText sx={{ mt: 0 }}>{helperText}</FormHelperText>}
    </FormControl>
  )
}

export default forwardRef(GenericDualListbox) as <T extends DataItem>(
  props: DualListboxProps<T> & { ref?: React.Ref<DualListboxRef<T>> }
) => React.ReactElement
