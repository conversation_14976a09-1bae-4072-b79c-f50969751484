import React from 'react'
import dynamic from 'next/dynamic'
import { useTheme } from '@mui/material/styles'
import { useSettings } from 'src/@core/hooks/useSettings'
import { Box, CircularProgress, Typography } from '@mui/material'

// Dynamically import the CSVImporter with SSR disabled
const CSVImporterComponent = dynamic(() => import('csv-import-react').then(mod => ({ default: mod.CSVImporter })), {
  ssr: false,
  loading: () => (
    <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', p: 4 }}>
      <CircularProgress size={40} />
      <Typography sx={{ mt: 2 }}>Loading CSV Importer...</Typography>
    </Box>
  )
})

interface ColumnTemplate {
  name: string
  key?: string
  required?: boolean
  description?: string
  suggested_mappings?: string[]
  data_type?: string
  [key: string]: any
}

interface ImportCsvProps {
  isOpen: boolean
  onClose: () => void
  onComplete: (data: any) => void
  columns: ColumnTemplate[]
  darkMode?: boolean
  templateMapper?: (column: ColumnTemplate) => Record<string, any>
}

export default function ImportCsv({
  isOpen,
  onClose,
  onComplete,
  columns,
  darkMode,
  templateMapper = column => ({
    name: column.name,
    key: column.key,
    required: column.required,
    description: column.description,
    suggested_mappings: column.suggested_mappings,
    data_type: column.data_type
  })
}: ImportCsvProps) {
  const theme = useTheme()
  const { settings } = useSettings()

  // Determine dark mode based on theme settings
  const isDarkMode = React.useMemo(() => {
    if (darkMode !== undefined) return darkMode

    // Check theme mode
    if (settings.mode === 'dark') return true
    if (settings.mode === 'light') return false
    if (settings.mode === 'semi-dark') {
      // In semi-dark mode, use the system theme
      return theme.palette.mode === 'dark'
    }

    return theme.palette.mode === 'dark'
  }, [darkMode, settings.mode, theme.palette.mode])

  return isOpen ? (
    <CSVImporterComponent
      modalIsOpen={isOpen}
      modalOnCloseTriggered={onClose}
      darkMode={isDarkMode}
      onComplete={onComplete}
      template={{
        columns: columns.map(templateMapper)
      }}
    />
  ) : null
}
