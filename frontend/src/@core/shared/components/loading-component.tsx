import { Box, CircularProgress, Typography, useTheme } from '@mui/material'

interface LoadingProps {
  message?: string
}

import useThemeImage from 'src/hooks/useThemeImage'

const LoadingComponent = ({ message = 'Loading...' }: LoadingProps) => {
  const theme = useTheme()

  const { imageSource } = useThemeImage({
    lightImage: '/images/logo_winlog.png',
    darkImage: '/images/logo_winlog.png'
  })

  return (
    <Box
      sx={{
        minHeight: '100vh',
        display: 'flex',
        alignItems: 'center',
        flexDirection: 'column',
        justifyContent: 'center'
      }}
    >
      <Box sx={{ mt: 6, display: 'flex', alignItems: 'center', flexDirection: 'column' }}>
        <CircularProgress sx={{ mb: 4 }} />
        <Typography>{message}</Typography>
      </Box>
    </Box>
  )
}

export default LoadingComponent
