import React, { useRef, useState, useCallback, useEffect, useMemo } from 'react'
import { useInfiniteQuery, useQuery } from '@tanstack/react-query'
import debounce from 'lodash/debounce'
import { Autocomplete, CircularProgress, Box, Typography, type AutocompleteProps } from '@mui/material'
import CustomTextField from 'src/@core/components/mui/text-field'

interface AsyncInfiniteSelectProps<T = any, Multiple extends boolean | undefined = false> {
  value: string | null
  onChange: (value: string | null) => void
  placeholder?: string
  queryKey: string | string[]
  label?: string
  fetchFn: (params: { pageParam: number; searchQuery: string }) => Promise<{
    items: T[]
    nextPage: number | null
    totalItems: number
    [key: string]: any
  }>
  isRequired?: boolean
  fetchByIdFn?: (id: string) => Promise<T>
  getOptionLabel?: (option: T) => string
  renderOption?: (props: React.HTMLAttributes<HTMLLIElement>, option: T) => React.ReactNode
  isOptionEqualToValue?: (option: T, value: string | T) => boolean
  getOptionValue?: (option: T) => string
  size?: 'small' | 'medium'
  minSearchLength?: number
  debounceMs?: number
  noOptionsText?: string
  prefetch?: boolean
  loadingText?: string
  error?: boolean
  helperText?: React.ReactNode
  autocompleteProps?: Partial<AutocompleteProps<T, Multiple, false, false>>
}

function AsyncInfiniteSelect<T extends { id: string | number } | { [key: string]: any }>({
  value,
  onChange,
  placeholder = 'Search...',
  queryKey,
  fetchFn,
  isRequired = false,
  fetchByIdFn,
  label,
  getOptionLabel = (option: any) => option.title || option.name || option.label || String(option.id),
  renderOption,
  isOptionEqualToValue = (option: any, value: any) => {
    // Handle both string values and object values
    if (typeof value === 'object' && value !== null) {
      return String(option.id) === String(value.id)
    }

    return String(option.id) === value || String(option.value) === value
  },
  getOptionValue = (option: any) => String(option.id || option.value),
  size = 'small',
  minSearchLength = 3,
  debounceMs = 500,
  noOptionsText = 'No items found',
  loadingText = 'Loading...',
  error = false,
  helperText,
  prefetch = false,
  autocompleteProps = {}
}: AsyncInfiniteSelectProps<T>) {
  const [searchQuery, setSearchQuery] = useState('')
  const [inputValue, setInputValue] = useState('')
  const [selectedOption, setSelectedOption] = useState<T | null>(null)
  const [initialValueFetched, setInitialValueFetched] = useState(false)
  const [open, setOpen] = useState(false)

  const debouncedSearch = useRef(debounce((query: string) => setSearchQuery(query), debounceMs)).current

  const queryKeyArray = Array.isArray(queryKey) ? queryKey : [queryKey]

  // Fetch initial value by ID if provided and not already in the items list
  const { data: initialValueData, isLoading: isLoadingInitialValue } = useQuery({
    queryKey: [...queryKeyArray, 'initialValue', value],
    queryFn: () => (fetchByIdFn ? fetchByIdFn(value as string) : Promise.resolve(null)),
    enabled: !!value && !!fetchByIdFn && !initialValueFetched && !selectedOption,
    staleTime: Number.POSITIVE_INFINITY // Don't refetch this data unnecessarily
  })

  // Main query for search results
  const {
    data,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    isLoading,
    isError,
    error: queryError
  } = useInfiniteQuery({
    queryKey: [...queryKeyArray, 'search', searchQuery],
    queryFn: ({ pageParam = 0 }) => fetchFn({ pageParam, searchQuery }),
    getNextPageParam: lastPage => lastPage.nextPage,
    initialPageParam: 0,
    enabled: (open || prefetch) && (searchQuery === '' || searchQuery.length >= minSearchLength)
  })

  const items = useMemo(() => data?.pages.flatMap(page => page.items) || [], [data?.pages])

  // useEffect(() => {
  //   if (prefetch && hasNextPage && !isFetchingNextPage && items.length > 0) {
  //     fetchNextPage()
  //   }
  // }, [prefetch, hasNextPage, isFetchingNextPage, items.length, fetchNextPage])

  const handleScroll = useCallback(
    (event: React.UIEvent<HTMLUListElement>) => {
      const listbox = event.currentTarget
      if (listbox.scrollTop + listbox.clientHeight >= listbox.scrollHeight - 50 && hasNextPage && !isFetchingNextPage) {
        fetchNextPage()
      }
    },
    [fetchNextPage, hasNextPage, isFetchingNextPage]
  )

  const handleInputChange = useCallback(
    (_: React.SyntheticEvent, value: string) => {
      setInputValue(value)
      if (value.length >= minSearchLength || value === '') {
        debouncedSearch(value)
      }
    },
    [debouncedSearch, minSearchLength]
  )

  // Set selected option from items or initial value data
  useEffect(() => {
    if (value) {
      // First check if the value is in the loaded items
      if (items.length > 0) {
        const option = items.find((item: any) => isOptionEqualToValue(item, value))
        if (option) {
          setSelectedOption(option)
          setInitialValueFetched(true)

          return
        }
      }

      // If not found in items but we have initialValueData, use that
      if (initialValueData && !initialValueFetched) {
        setSelectedOption(initialValueData)
        setInitialValueFetched(true)
      }
    } else {
      setSelectedOption(null)
      setInitialValueFetched(false)
    }
  }, [value, items, initialValueData, isOptionEqualToValue, initialValueFetched])

  const defaultRenderOption = useCallback(
    (props: React.HTMLAttributes<HTMLLIElement>, option: any) => (
      <li {...props} key={getOptionValue(option)}>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <div>
            <Typography variant='body1'>{getOptionLabel(option)}</Typography>
            {option.description && (
              <Typography variant='caption' color='text.secondary'>
                {option.description}
              </Typography>
            )}
          </div>
        </Box>
      </li>
    ),
    [getOptionLabel, getOptionValue]
  )

  // Using React.forwardRef correctly with ListboxComponent
  const ListboxComponent = useMemo(() => {
    return React.forwardRef<HTMLUListElement, React.HTMLAttributes<HTMLUListElement>>((props, ref) => (
      <ul {...props} ref={ref} onScroll={handleScroll} style={{ maxHeight: '400px', overflow: 'auto' }}>
        {props.children}
        {isFetchingNextPage && (
          <Box sx={{ display: 'flex', justifyContent: 'center', p: 1 }}>
            <CircularProgress size={20} />
          </Box>
        )}
        {isError && (
          <Box sx={{ p: 2, color: 'error.main', textAlign: 'center' }}>
            Error loading data: {queryError instanceof Error ? queryError.message : 'Unknown error'}
          </Box>
        )}
        {!isLoading && !isFetchingNextPage && items.length === 0 && searchQuery !== '' && (
          <Box sx={{ p: 2, textAlign: 'center' }}>{noOptionsText}</Box>
        )}
      </ul>
    ))
  }, [handleScroll, isFetchingNextPage, isError, isLoading, items.length, noOptionsText, searchQuery, queryError])

  return (
    <Autocomplete
      value={selectedOption}
      onChange={(_, newValue) => {
        if (newValue === null) {
          onChange(null)
        } else {
          onChange(getOptionValue(newValue))
        }
      }}
      inputValue={inputValue}
      onInputChange={handleInputChange}
      options={items}
      loading={isLoading || isLoadingInitialValue}
      getOptionLabel={getOptionLabel}
      isOptionEqualToValue={(option, value) => {
        if (value === null || option === null) return false

        return isOptionEqualToValue(option, getOptionValue(value))
      }}
      renderOption={renderOption || defaultRenderOption}
      renderInput={params => (
        <CustomTextField
          {...params}
          size={size}
          label={label}
          placeholder={placeholder}
          required={isRequired}
          error={error}
          helperText={helperText}
          InputProps={{
            ...params.InputProps,
            endAdornment: (
              <>
                {isLoading || isLoadingInitialValue ? <CircularProgress color='inherit' size={20} /> : null}
                {params.InputProps.endAdornment}
              </>
            )
          }}
        />
      )}
      ListboxComponent={ListboxComponent}
      loadingText={loadingText}
      noOptionsText={noOptionsText}
      filterOptions={x => x}
      open={open}
      onOpen={() => setOpen(true)}
      onClose={() => setOpen(false)}
      sx={{
        width: '100%'
      }}
      {...autocompleteProps}
    />
  )
}

export default AsyncInfiniteSelect
