import { Alert, AlertTitle } from '@mui/material'

interface SuccessAlertProps {
  title?: string
  message?: string
}

const SuccessAlert = ({ title = 'Success', message = 'Operation completed successfully.' }: SuccessAlertProps) => {
  return (
    <Alert severity='success' sx={{ mb: 4 }}>
      {title && <AlertTitle>{title}</AlertTitle>}
      {message}
    </Alert>
  )
}

export default SuccessAlert
