import { useTranslation } from 'react-i18next'
import { Card, CardContent, CardHeader, Grid, TextField } from '@mui/material'
import { useRouter } from 'next/router'
import { useQueryClient } from '@tanstack/react-query'
import { useEffect, useState } from 'react'
import toast from 'react-hot-toast'
import { yupResolver } from '@hookform/resolvers/yup'
import { Controller, useForm } from 'react-hook-form'
import * as yup from 'yup'

import FormButtons from 'src/@core/components/form-buttons'
import { useUpdateGroupArticle } from 'src/hooks/helpers/useGroupArticles'
import { useCreateGroupArticle } from 'src/hooks/_services/useGroupArticles'
import { GroupArticleRequestPayload } from 'src/types/models/_services/group-article'
import CustomTextField from 'src/@core/components/mui/text-field'

const schema = yup.object().shape({
  name: yup.string().required('Le nom du groupe est requis')
})

const defaultValues = {
  name: ''
}

interface ArticleGroupFormProps {
  mode: 'create' | 'edit'
  articleGroupId?: string
  initialData?: any
  onSuccess?: () => void
}

export const ArticleGroupForm = ({ mode, articleGroupId, onSuccess, initialData }: ArticleGroupFormProps) => {
  const { t } = useTranslation()
  const router = useRouter()
  const queryClient = useQueryClient()

  const {
    control,
    handleSubmit,
    reset,
    formState: { errors }
  } = useForm<GroupArticleRequestPayload>({
    resolver: yupResolver(schema),
    defaultValues: initialData || defaultValues
  })

  const {
    mutateAsync: createArticleGroup,
    isPending: isCreating,
    data: createArticleGroupRes
  } = useCreateGroupArticle({
    onSuccess: () => {
      toast.success(t('forms.articleGroupForm.messages.create_success'))
      queryClient.invalidateQueries({ queryKey: ['articleGroups'] })
      onSuccess?.()
    },
    onError: () => {
      toast.error(t('forms.articleGroupForm.messages.create_error'))
    }
  })

  const {
    mutateAsync: updateArticleGroup,
    isPending: isUpdating,
    data: updateArticleGroupRes
  } = useUpdateGroupArticle(articleGroupId || '', {
    onSuccess: () => {
      toast.success(t('forms.articleGroupForm.messages.update_success'))
      queryClient.invalidateQueries({ queryKey: ['articleGroups'] })
      onSuccess?.()
    },
    onError: () => {
      toast.error(t('forms.articleGroupForm.messages.update_error'))
    }
  })

  const handleSave = async (formData: GroupArticleRequestPayload) => {
    try {
      if (mode === 'create') {
        return await createArticleGroup(formData)
      } else {
        return await updateArticleGroup(formData)
      }
    } catch (error) {
      // Error is handled by mutation callbacks
    }
  }

  const handleSaveAndReturn = async (formData: GroupArticleRequestPayload) => {
    try {
      await handleSave(formData)
      router.push('/services/article-groups')
    } catch (error) {
      // Error handled by mutation callbacks
    }
  }

  const handleSaveAndEdit = async (formData: GroupArticleRequestPayload) => {
    try {
      const articleGroup = await handleSave(formData)
      if (mode === 'create' && articleGroup?.id) {
        router.push(`/services/article-groups/${articleGroup.id}/edit`)
      }
    } catch (error) {
      // Error handled in handleSave
    }
  }

  const handleSaveAndAddNew = async (formData: GroupArticleRequestPayload) => {
    try {
      await handleSave(formData)
      reset()
    } catch (error) {
      // Error handled by mutation callbacks
    }
  }

  const isLoading = isCreating || isUpdating

  return (
    <Card>
      <CardHeader title={t(`forms.articleGroupForm.${mode === 'create' ? 'create_title' : 'edit_title'}`)} />
      <CardContent>
        <form onSubmit={handleSubmit(handleSaveAndReturn)}>
          <Grid container spacing={5}>
            <Grid item xs={12} sm={4}>
              <Controller
                name='name'
                control={control}
                render={({ field }) => (
                  <CustomTextField
                    {...field}
                    fullWidth
                    label={t('forms.articleGroupForm.fields.name')}
                    error={Boolean(errors.name)}
                    helperText={errors.name?.message}
                  />
                )}
              />
            </Grid>
          </Grid>
          <FormButtons
            isLoading={isLoading}
            mode={mode}
            cancelPath='/services/article-groups'
            onSave={handleSubmit(handleSaveAndReturn)}
            onSaveAndEdit={handleSubmit(handleSaveAndEdit)}
            onSaveAndAddNew={handleSubmit(handleSaveAndAddNew)}
          />
        </form>
      </CardContent>
    </Card>
  )
}
