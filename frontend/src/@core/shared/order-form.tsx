import { useEffect, useMemo, useState } from 'react'
import {
  Card,
  CardHeader,
  CardContent,
  Grid,
  Box,
  FormControlLabel,
  Switch,
  MenuItem,
  Typography,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  FormHelperText,
  FormControl,
  Select,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  Button
} from '@mui/material'
import { useTranslation } from 'react-i18next'
import { useForm, Controller } from 'react-hook-form'
import { yupResolver } from '@hookform/resolvers/yup'
import { useRouter } from 'next/router'
import { useQueryClient } from '@tanstack/react-query'
import FormButtons from '../components/form-buttons'
import OrderOperationsForm from './order-operations-form'
import CustomTextField from 'src/@core/components/mui/text-field'
import { fetchInfiniteCustomers, getCustomerById } from 'src/services/customer'
import { Icon } from '@iconify/react'
import { Order, OrderFormValues } from 'src/types/models/_services/orders'
import { orderSchema } from 'src/lib/schemas'
import AsyncInfiniteSelect from './components/infinite-select'
import {
  useCreateOrder,
  useGenerateOrderInvoice,
  useRequestValidation,
  useUpdateOrder,
  useUpdateValidationStatus
} from 'src/hooks/_services/useOrders'
import ErrorComponent from './components/error-component'
import toast from 'react-hot-toast'
import { IsValidator } from './components/is-validator'
import { useSelector } from 'react-redux'
import { selectUser } from 'src/store/auth'
import { LoadingButton } from '@mui/lab'
import { useCheckAuthorValidation } from 'src/hooks/useCheckAuthorValidation'

interface OrderFormProps {
  mode?: 'create' | 'edit'
  initialData?: Order
  orderId?: string
  refetch?: () => void
  onSuccess?: () => void
}

const prestationOptions = [
  { value: 'CITERNE', label: 'Citerne' },
  { value: 'PLATEAU', label: 'Plateau' },
  { value: 'BENNE', label: 'Benne' }
]

const validationOptions = [
  { value: 'EN ATTENTE', label: 'En attente' },
  { value: 'VALIDEE', label: 'Validé' },
  { value: 'REJETEE', label: 'Rejeté' }
]

function OrderForm({ mode = 'create', initialData, orderId, onSuccess, refetch }: OrderFormProps) {
  const user = useSelector(selectUser)
  const { author, isValidator } = useCheckAuthorValidation(orderId || '')
  const { t } = useTranslation()
  const queryClient = useQueryClient()
  const router = useRouter()
  const [submitError, setSubmitError] = useState<string | null>(null)
  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false)

  const defaultValues = {
    customer: '',
    prestation: user?.type_of_operation || '',
    is_city: false,
    operations: [],
    rejection_reason: '',
    validation_status: 'EN ATTENTE'
  }

  const {
    control,
    handleSubmit,
    formState: { errors },
    reset,
    watch
  } = useForm<OrderFormValues>({
    resolver: yupResolver(orderSchema),
    defaultValues
  })

  const customer = watch('customer')
  const validationStatus = watch('validation_status')
  const { mutate: requestValidation, isPending: isRequestingValidation } = useRequestValidation({
    onSuccess: () => {
      toast.success(t('forms.orderForm.messages.validation_request_sent'))
      queryClient.invalidateQueries({ queryKey: ['orders'] })
      setConfirmDialogOpen(false)
    },
    onError: (error: any) => {
      toast.error(t('forms.orderForm.messages.validation_request_error'))
      setConfirmDialogOpen(false)
    }
  })

  const { mutateAsync: createOrder, isPending: isCreating } = useCreateOrder({
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['orders'] })
      toast.success(t('forms.orderForm.messages.create_success'))
      onSuccess?.()
    },
    onError: (error: any) => {
      toast.error(t('forms.orderForm.messages.create_error'))
      setSubmitError(error.message)
    }
  })

  const { mutateAsync: updateOrder, isPending: isUpdating } = useUpdateOrder({
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['orders'] })
      toast.success(t('forms.orderForm.messages.update_success'))
      onSuccess?.()
    },
    onError: (error: any) => {
      toast.error(t('forms.orderForm.messages.update_error'))
      setSubmitError(error.message)
    }
  })

  const { mutateAsync: generateOrderMissionExpense, isPending: isGeneratingInvoice } = useGenerateOrderInvoice({
    onSuccess: data => {
      queryClient.invalidateQueries({ queryKey: ['orders'] })
      toast.success(t('forms.orderForm.messages.invoice_generated'))
      console.log(data)
    },
    onError: (error: any) => {
      toast.error(t('forms.orderForm.messages.invoice_error'))
    }
  })

  const { mutateAsync: updateValidationStatus, isPending: isUpdatingValidation } = useUpdateValidationStatus({
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['orders'] })
      toast.success('Validation status updated successfully')
    },
    onError: (error: any) => {
      toast.error(error.response.data.message)
    }
  })

  const formatedInitialData = useMemo(
    () => ({
      id: initialData?.id,
      customer: initialData?.customer.id,
      prestation: initialData?.prestation,
      is_city: initialData?.is_city,
      operations: initialData?.lines.map(l => ({
        id: l.id,
        tractor_id: l.tractor ? l.tractor.id : null,
        trailer_id: l.trailer ? l.trailer.id : null,
        driver_id: l.driver?.id,
        agence_id: l.agence.id,
        bc_number: l.bc_number,
        be_number: l.be_number,
        direction: l.direction,
        path_id: l.path.id,
        petrol_volume: l.petrol_volume.toString(),
        price: l.price,
        product_id: l.product.toString(),
        qty: l.qty,
        road_fees: l.road_fees,
        unloaded_quantity: '',
        validation: l.validation,
        nature_product_id: l.nature_product ? l.nature_product : null
      }))
    }),
    [initialData]
  )

  useEffect(() => {
    if (mode === 'edit' && initialData) {
      reset(formatedInitialData)
    }
  }, [mode, initialData, reset, formatedInitialData])

  const handleCreate = async (data: OrderFormValues) => {
    try {
      if (mode === 'create') {
        await createOrder(data)
      } else {
        await updateOrder({ id: orderId!, data })
      }
      router.push('/services/orders')
    } catch (error) {}
  }

  const handleCreateAndUpdate = async (data: OrderFormValues) => {
    try {
      if (mode === 'create') {
        await createOrder(data)
      } else {
        await updateOrder({ id: orderId!, data })
      }
      router.push(`/services/orders/${orderId}/edit`)
    } catch (error) {}
  }

  const handleCreateAndSave = async (data: OrderFormValues) => {
    try {
      if (mode === 'create') {
        await createOrder(data)
      } else {
        await updateOrder({ id: orderId!, data })
      }
      reset()
    } catch (error) {}
  }

  const handleUpdateValidationStatus = async (data: OrderFormValues) => {
    try {
      await updateValidationStatus({ id: orderId!, data })
    } catch (error) {}
  }

  const handleRequestValidation = () => {
    setConfirmDialogOpen(true)
  }

  const handleConfirmRequestValidation = async () => {
    try {
      await requestValidation({ id: orderId! })
    } catch (error) {}
  }

  const handleCancelRequestValidation = () => {
    setConfirmDialogOpen(false)
  }

  return (
    <Box sx={{ display: 'flex', justifyContent: 'center' }}>
      <Card sx={{ width: '100%' }}>
        <CardHeader
          title={t(`forms.orderForm.title`)}
          action={
            <Box sx={{ display: 'flex', gap: 2 }}>
              {mode === 'edit' && !initialData?.validate && (
                <Box sx={{ display: 'flex', gap: 2 }}>
                  {(author || isValidator) && (
                    <LoadingButton
                      loading={isRequestingValidation}
                      type='button'
                      variant='contained'
                      color='primary'
                      disabled={isValidator || initialData?.validate}
                      onClick={handleRequestValidation}
                    >
                      Request Validation
                    </LoadingButton>
                  )}
                </Box>
              )}

              {mode === 'edit' && initialData?.validate && (
                <LoadingButton
                  type='button'
                  variant='outlined'
                  color='primary'
                  loading={isGeneratingInvoice}
                  disabled={isGeneratingInvoice}
                  startIcon={<Icon icon='mdi:file' />}
                  onClick={() => generateOrderMissionExpense({ for_order: orderId! })}
                >
                  Genarate DA de frais de mission
                </LoadingButton>
              )}

              {mode === 'edit' && (
                <LoadingButton
                  type='button'
                  variant='outlined'
                  color='primary'
                  loading={isCreating || isUpdating}
                  disabled={isCreating || isUpdating}
                  startIcon={<Icon icon='mdi:refresh' />}
                  onClick={() => refetch?.()}
                >
                  Refresh
                </LoadingButton>
              )}
            </Box>
          }
        />

        <CardContent>
          <form onSubmit={handleSubmit(handleCreate)}>
            {submitError && <ErrorComponent message={submitError} />}

            <Grid container spacing={5}>
              <Grid item xs={4}>
                <Controller
                  name='customer'
                  control={control}
                  render={({ field, fieldState: { error } }) => (
                    <Box>
                      <AsyncInfiniteSelect
                        fetchByIdFn={getCustomerById}
                        getOptionLabel={customer => customer.name}
                        getOptionValue={customer => String(customer.id)}
                        isOptionEqualToValue={(option, value) => String(option.id) === value}
                        label={t('forms.orderForm.fields.customer') as string}
                        value={field.value}
                        onChange={field.onChange}
                        isRequired
                        queryKey={'customers'}
                        fetchFn={fetchInfiniteCustomers}
                        placeholder={'search client..'}
                        size='small'
                        minSearchLength={2}
                        debounceMs={400}
                        prefetch
                        error={Boolean(error)}
                      />
                      {error && <FormHelperText error>{error.message}</FormHelperText>}
                    </Box>
                  )}
                />
              </Grid>

              <Grid item xs={4}>
                <Controller
                  name='prestation'
                  control={control}
                  render={({ field, fieldState: { error } }) => (
                    <CustomTextField
                      select
                      fullWidth
                      label={t('forms.orderForm.fields.prestation')}
                      error={Boolean(error)}
                      helperText={error?.message}
                      disabled={user?.type_of_operation ? true : user?.is_superuser ? false : true}
                      SelectProps={{
                        native: false
                      }}
                      {...field}
                    >
                      {prestationOptions.map(option => (
                        <MenuItem key={option.value} value={option.value}>
                          {t(option.label)}
                        </MenuItem>
                      ))}
                    </CustomTextField>
                  )}
                />
              </Grid>

              <Grid item xs={12}>
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                  <FormControlLabel
                    control={
                      <Controller
                        name='is_city'
                        control={control}
                        render={({ field }) => <Switch checked={field.value} onChange={field.onChange} />}
                      />
                    }
                    label={t('forms.orderForm.fields.is_city')}
                  />
                  {mode === 'edit' && (
                    <Box sx={{ display: 'flex', gap: 1 }}>
                      <Typography style={{ fontWeight: 'bold' }}>{t('forms.orderForm.fields.validated')}:</Typography>
                      <Icon
                        icon={initialData?.validate ? 'mdi:check-circle' : 'mdi:close-circle'}
                        style={{
                          color: initialData?.validate ? '#56CA00' : '#F44336',
                          fontSize: '20px'
                        }}
                      />
                    </Box>
                  )}
                </Box>
              </Grid>

              <Grid item xs={12}>
                <Controller
                  name='operations'
                  control={control}
                  render={({ field, fieldState: { error } }) => (
                    <OrderOperationsForm
                      value={field.value}
                      onChange={field.onChange}
                      error={error}
                      mode={mode}
                      control={control}
                      client={customer}
                      meta={{
                        author,
                        isValidator,
                        isValidate: initialData?.validate as boolean
                      }}
                    />
                  )}
                />
              </Grid>
            </Grid>

            <Box sx={{ mt: 5, display: 'flex', justifyContent: 'flex-end' }}>
              {(mode === 'edit' && isValidator) || initialData?.validate ? null : (
                <FormButtons
                  isLoading={isCreating || isUpdating}
                  mode={mode}
                  onSaveAndAddNew={handleSubmit(handleCreateAndSave)}
                  onSaveAndEdit={handleSubmit(handleCreateAndUpdate)}
                  disabled={!customer}
                  cancelPath='/services/orders'
                  onSave={handleSubmit(handleCreate)}
                />
              )}
            </Box>
            <Box sx={{ mt: 5 }}>
              <Box>
                <Typography variant='h5' sx={{ mb: 2, fontWeight: 'bold' }}>
                  Validé par:
                </Typography>
                <List sx={{ py: 0 }}>
                  {initialData?.validation?.map((validator: any) => (
                    <ListItem key={validator.id} sx={{ py: 0.5, px: 0 }}>
                      <ListItemIcon sx={{ minWidth: 32 }}>
                        <Icon icon='tabler:user-check' fontSize={20} />
                      </ListItemIcon>
                      <ListItemText primary={`${validator.first_name} ${validator.last_name}`} sx={{ m: 0 }} />
                    </ListItem>
                  ))}
                  {(!initialData?.validation || initialData.validation.length === 0) && (
                    <ListItem sx={{ py: 0.5, px: 0 }}>
                      <ListItemText
                        primary='Aucune validation'
                        sx={{
                          m: 0,
                          color: 'text.secondary',
                          fontStyle: 'italic'
                        }}
                      />
                    </ListItem>
                  )}
                </List>
              </Box>
            </Box>
            <IsValidator orderId={orderId!}>
              <Card>
                <CardHeader title='Validation' titleTypographyProps={{ variant: 'h6' }} />
                <CardContent sx={{ p: 4 }}>
                  <Grid container spacing={4}>
                    <Grid item xs={12} md={4}>
                      <Typography variant='subtitle2' sx={{ mb: 2, fontWeight: 600 }}>
                        Statut de validation
                      </Typography>
                      <Controller
                        name='validation_status'
                        control={control}
                        defaultValue='EN ATTENTE'
                        render={({ field, fieldState: { error } }) => (
                          <FormControl fullWidth error={Boolean(error)}>
                            <CustomTextField
                              select
                              fullWidth
                              SelectProps={{
                                native: false
                              }}
                              {...field}
                            >
                              {validationOptions.map(option => (
                                <MenuItem key={option.value} value={option.value}>
                                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                    <Icon
                                      icon={
                                        option.value === validationOptions[1].value
                                          ? 'mdi:check-circle'
                                          : option.value === validationOptions[2].value
                                          ? 'mdi:close-circle'
                                          : 'mdi:clock-outline'
                                      }
                                      color={
                                        option.value === validationOptions[1].value
                                          ? '#56CA00'
                                          : option.value === validationOptions[2].value
                                          ? '#F44336'
                                          : '#FFB400'
                                      }
                                      fontSize={20}
                                      style={{ marginRight: '8px' }}
                                    />
                                    <Box>
                                      <Typography variant='body2'>{option.label}</Typography>
                                    </Box>
                                  </Box>
                                </MenuItem>
                              ))}
                            </CustomTextField>
                            {error && <FormHelperText>{error.message}</FormHelperText>}
                          </FormControl>
                        )}
                      />
                    </Grid>

                    {validationStatus === validationOptions[2].value && (
                      <Grid item xs={12} md={4}>
                        <Typography variant='subtitle2' sx={{ mb: 2, fontWeight: 600 }}>
                          Commentaire de validation
                        </Typography>
                        <Controller
                          name='rejection_reason'
                          control={control}
                          render={({ field, fieldState: { error } }) => (
                            <>
                              <CustomTextField
                                {...field}
                                multiline
                                rows={3}
                                fullWidth
                                error={Boolean(error)}
                                helperText={error?.message}
                                sx={{
                                  '& .MuiOutlinedInput-root': {
                                    alignItems: 'flex-start',
                                    '& .MuiInputAdornment-root': {
                                      mt: 2
                                    }
                                  }
                                }}
                              />
                            </>
                          )}
                        />
                      </Grid>
                    )}
                  </Grid>

                  {(validationStatus === validationOptions[2].value ||
                    validationStatus === validationOptions[1].value) && (
                    <Box sx={{ mt: 4, display: 'flex', justifyContent: 'flex-end' }}>
                      <LoadingButton
                        variant='contained'
                        onClick={handleSubmit(handleUpdateValidationStatus)}
                        color={validationStatus === validationOptions[1].value ? 'success' : 'error'}
                        startIcon={
                          <Icon
                            icon={
                              validationStatus === validationOptions[1].value ? 'mdi:check-circle' : 'mdi:close-circle'
                            }
                          />
                        }
                        loading={isUpdatingValidation}
                        sx={{ px: 5 }}
                      >
                        {validationStatus === validationOptions[1].value ? 'Approuver' : 'Rejeter '}
                      </LoadingButton>
                    </Box>
                  )}
                </CardContent>
              </Card>
            </IsValidator>
          </form>
        </CardContent>
      </Card>

      {/* Confirmation Dialog */}
      <Dialog
        open={confirmDialogOpen}
        onClose={handleCancelRequestValidation}
        aria-labelledby='alert-dialog-title'
        aria-describedby='alert-dialog-description'
      >
        <DialogTitle id='alert-dialog-title'>
          {t('forms.orderForm.requestValidation.title', 'Request Validation')}
        </DialogTitle>
        <DialogContent>
          <DialogContentText id='alert-dialog-description'>
            {t(
              'forms.orderForm.requestValidation.message',
              'Are you sure you want to request validation for this order?'
            )}
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCancelRequestValidation} color='secondary' disabled={isRequestingValidation}>
            {t('common.cancel', 'Cancel')}
          </Button>
          <Button
            onClick={handleConfirmRequestValidation}
            variant='contained'
            color='primary'
            autoFocus
            disabled={isRequestingValidation}
          >
            {t('common.confirm', 'Confirm')}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  )
}

export default OrderForm
