import { useTranslation } from 'react-i18next'
import { useQueryClient } from '@tanstack/react-query'
import { toast } from 'react-hot-toast'
import { useRouter } from 'next/router'
import {
  Box,
  Card,
  CardContent,
  CardHeader,
  FormControlLabel,
  Grid,
  IconButton,
  InputAdornment,
  MenuItem,
  Switch,
  Typography
} from '@mui/material'
import { useCreateUser, useUpdateUser, useUserSupervisors } from 'src/hooks/user-management/useUsers'
import CustomTextField from '../components/mui/text-field'
import { useForm, Controller } from 'react-hook-form'
import FormButtons from '../components/form-buttons'
import { usersSchema } from 'src/lib/schemas'
import { yupResolver } from '@hookform/resolvers/yup'
import { SingleUserResponse, UserRequestPayload } from 'src/types/models/user-management/users'
import { useEffect, useState } from 'react'
import { Icon } from '@iconify/react'
import AsyncInfiniteSelect from './components/infinite-select'
import { fetchInfiniteUserDepartments, getUserDepartmentById } from 'src/services/user-department'
import GenericMultiSelect from './components/multiselect-input'
import { useInfiniteGroups } from 'src/hooks/useGroups'
import { useInfiniteUserDepartments } from 'src/hooks/_services/useUserDepartment'
import { useInfiniteUserPermissions } from 'src/hooks/user-management/useUserPermission'
import moment from 'moment'
import CustomAutocomplete from '../components/mui/autocomplete'
import { LinkStyled } from 'src/pages/reset-password'

interface UserFormProps {
  mode: 'create' | 'edit'
  userId?: string
  initialData?: SingleUserResponse
}
const containerShape = [
  { label: 'Citerne', value: 'CITERNE' },
  { label: 'Plateaux', value: 'PLATEAU' },
  { label: 'Bennes', value: 'BENNE' },
  { label: 'Kangoo', value: 'KANGOO' },
  { label: 'Pickup', value: 'PIK-UP' },
  { label: 'Bennes et Plateaux', value: 'BENNE_PLATEAU' },
  { label: "Fourgonnette d'intervention", value: 'FOURGONNETTE_INTERVENTION' }
]

const languages = [
  { label: 'Français', value: 'fr' },
  { label: 'Anglais', value: 'en' }
]

export default function UserForm({ mode = 'create', userId, initialData }: UserFormProps) {
  const { t } = useTranslation()
  const router = useRouter()
  const queryClient = useQueryClient()
  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)

  const {
    data: groups,
    isLoading: isLoadingGroups,
    fetchNextPage: fetchNextPageGroups,
    hasNextPage: hasMoreGroups
  } = useInfiniteGroups({
    limit: 100,
    offset: 0
  })

  const {
    data: userDepartments,
    isLoading: isLoadingUserDepartments,
    fetchNextPage: fetchNextPageUserDepartments,
    hasNextPage: hasMoreUserDepartments
  } = useInfiniteUserDepartments({
    limit: 100,
    offset: 0
  })

  const {
    data: permissions,
    isLoading: isLoadingPermissions,
    fetchNextPage: fetchNextPagePermissions,
    hasNextPage: hasMorePermissions
  } = useInfiniteUserPermissions({
    limit: 100,
    offset: 0
  })

  const { data: supervisors, isLoading: isLoadingSupervisors } = useUserSupervisors({
    limit: 100,
    offset: 0
  })

  const {
    control,
    handleSubmit,
    reset,
    formState: { errors }
  } = useForm<UserRequestPayload>({
    resolver: yupResolver(usersSchema),
    defaultValues: {
      language: initialData?.language,
      is_active: true,
      is_staff: false,
      is_superuser: false,
      groups: [],
      user_permissions: [],
      supervisor_id: '',
      type_of_operation: '',
      other_phone: '',
      main_department_id: '',
      annex_departments_ids: []
    }
  })

  useEffect(() => {
    if (initialData) {
      reset({
        username: initialData.username,
        email: initialData.email,
        first_name: initialData.first_name,
        last_name: initialData.last_name,
        is_active: initialData.is_active,
        is_staff: initialData.is_staff,
        is_superuser: initialData.is_superuser,
        language: initialData.language,
        groups: initialData.groups,
        user_permissions: initialData.user_permissions,
        supervisor_id: initialData?.supervisor?.id,
        type_of_operation: initialData?.type_of_operation as any,
        other_phone: initialData?.other_phone as any,
        main_department_id: initialData?.main_department?.id as any,
        annex_departments_ids: initialData.annex_departments
      })
    }
  }, [initialData, reset])

  const {
    mutateAsync: createUser,
    isPending: isCreating,
    data: createUserRes
  } = useCreateUser({
    onSuccess: () => {
      toast.success(t('forms.usersForm.messages.create_success'))
      queryClient.invalidateQueries({ queryKey: ['users'] })
    },
    onError: () => {
      toast.error(t('forms.usersForm.messages.create_error'))
    }
  })

  const {
    mutateAsync: updateUser,
    isPending: isUpdating,
    data: updateUserRes
  } = useUpdateUser({
    onSuccess: () => {
      toast.success(t('forms.usersForm.messages.update_success'))
      queryClient.invalidateQueries({ queryKey: ['users'] })
    },
    onError: () => {
      toast.error(t('forms.usersForm.messages.update_error'))
    }
  })

  const isLoading = isCreating || isUpdating

  const onSubmit = async (data: any) => {
    try {
      if (mode === 'create') {
        await createUser(data)
      } else {
        await updateUser({ id: userId!, data })
      }
    } catch (error) {
      console.error(error)
    }
  }

  const handleSaveAndAddNew = async (data: any) => {
    try {
      await createUser(data)
      reset()
    } catch (error) {
      console.error(error)
    }
  }
  const handleSaveAndEdit = async (data: any) => {
    try {
      if (mode === 'create') {
        await createUser(data)
        router.push(`/user-management/users/${createUserRes?.id}/edit`)
      } else {
        await updateUser({ id: userId!, data })
      }
    } catch (error) {
      console.error(error)
    }
  }

  return (
    <Card>
      <CardHeader title={t(`forms.usersForm.${mode === 'create' ? 'create_title' : 'edit_title'}`)} />
      <CardContent>
        <form onSubmit={e => e.preventDefault()}>
          <Typography variant='subtitle1' sx={{ mb: 3 }}>
            {mode === 'create' ? t('forms.usersForm.descriptions.title') : "Modifier les informations de l'utilisateur"}
          </Typography>

          <Grid container spacing={4}>
            {/* Basic Information */}
            <Grid item xs={12}>
              <Typography variant='h6' sx={{ mb: 2 }}>
                {t('forms.usersForm.sections.basic_info')}
              </Typography>
              <Grid container spacing={3}>
                <Grid item xs={12} md={4}>
                  <Controller
                    name='username'
                    control={control}
                    rules={{ required: true }}
                    render={({ field }) => (
                      <CustomTextField
                        {...field}
                        fullWidth
                        label={t('forms.usersForm.fields.username')}
                        placeholder='John.Doe'
                        error={Boolean(errors.username)}
                        helperText={t('forms.usersForm.descriptions.username')}
                      />
                    )}
                  />
                </Grid>
                <Grid item xs={12} md={4}>
                  <Controller
                    name='email'
                    control={control}
                    render={({ field }) => (
                      <CustomTextField
                        {...field}
                        fullWidth
                        label={t('forms.usersForm.fields.email')}
                        placeholder='<EMAIL>'
                        error={Boolean(errors.email)}
                        helperText={errors.email ? 'Valid email is required' : ''}
                      />
                    )}
                  />
                </Grid>
              </Grid>
            </Grid>

            <Grid item xs={12}>
              <Grid container xs={12} spacing={3}>
                <Grid item xs={12} md={4}>
                  <Controller
                    name='first_name'
                    control={control}
                    render={({ field }) => (
                      <CustomTextField
                        {...field}
                        fullWidth
                        label={t('forms.usersForm.fields.firstName')}
                        placeholder='John'
                      />
                    )}
                  />
                </Grid>
                <Grid item xs={12} md={4}>
                  <Controller
                    name='last_name'
                    control={control}
                    render={({ field }) => (
                      <CustomTextField
                        {...field}
                        fullWidth
                        label={t('forms.usersForm.fields.lastName')}
                        placeholder='Doe'
                      />
                    )}
                  />
                </Grid>
              </Grid>
            </Grid>

            {/* Password Section */}
            {mode === 'create' ? (
              <Grid item xs={12}>
                <Typography variant='h6' sx={{ mb: 2 }}>
                  {t('forms.usersForm.sections.password')}
                </Typography>
                <Grid container spacing={3}>
                  <Grid item xs={12} md={4}>
                    <Controller
                      name='password'
                      control={control}
                      render={({ field }) => (
                        <CustomTextField
                          {...field}
                          fullWidth
                          type={showPassword ? 'text' : 'password'}
                          label={t('forms.usersForm.sections.password')}
                          error={Boolean(errors.password)}
                          helperText={errors.password ? 'Password must be at least 8 characters' : ''}
                          InputProps={{
                            endAdornment: (
                              <InputAdornment position='end'>
                                <IconButton
                                  edge='end'
                                  onClick={() => setShowPassword(!showPassword)}
                                  onMouseDown={e => e.preventDefault()}
                                  aria-label='toggle password visibility'
                                >
                                  <Icon fontSize='1.25rem' icon={showPassword ? 'tabler:eye' : 'tabler:eye-off'} />
                                </IconButton>
                              </InputAdornment>
                            )
                          }}
                        />
                      )}
                    />

                    <Box sx={{ pl: 2, borderLeft: '3px solid', borderColor: 'primary.main', mt: 1 }}>
                      <Typography variant='body2' sx={{ mb: 1 }}>
                        {t('forms.usersForm.descriptions.password.1')}
                      </Typography>
                      <Typography variant='body2' sx={{ mb: 1 }}>
                        {t('forms.usersForm.descriptions.password.2')}
                      </Typography>
                      <Typography variant='body2' sx={{ mb: 1 }}>
                        {t('forms.usersForm.descriptions.password.3')}
                      </Typography>
                      <Typography variant='body2'>{t('forms.usersForm.descriptions.password.4')}</Typography>
                    </Box>
                  </Grid>
                  <Grid item xs={12} md={4}>
                    <Controller
                      name='confirm_password'
                      control={control}
                      render={({ field }) => (
                        <CustomTextField
                          {...field}
                          fullWidth
                          type={showConfirmPassword ? 'text' : 'password'}
                          label={t('forms.usersForm.fields.confirmPassword')}
                          error={Boolean(errors.confirm_password)}
                          helperText={
                            errors.confirm_password
                              ? errors.confirm_password.message
                              : t('forms.usersForm.descriptions.confirmPassword')
                          }
                          InputProps={{
                            endAdornment: (
                              <InputAdornment position='end'>
                                <IconButton
                                  edge='end'
                                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                                  onMouseDown={e => e.preventDefault()}
                                  aria-label='toggle password visibility'
                                >
                                  <Icon
                                    fontSize='1.25rem'
                                    icon={showConfirmPassword ? 'tabler:eye' : 'tabler:eye-off'}
                                  />
                                </IconButton>
                              </InputAdornment>
                            )
                          }}
                        />
                      )}
                    />
                  </Grid>
                </Grid>
              </Grid>
            ) : (
              <Grid item>
                <Typography variant='h6' sx={{ mb: 2 }}>
                  {t('forms.usersForm.sections.password')}
                </Typography>
                <Typography variant='body2'>{t('forms.usersForm.descriptions.updatePassword')}</Typography>
                <Typography>
                  <Typography component={LinkStyled} href={`/user-management/users/${userId}/password`}>
                    <Icon fontSize='1.25rem' icon='tabler:chevron-right' />
                    <span>{t('forms.changePasswordForm.title')}</span>
                  </Typography>
                </Typography>
              </Grid>
            )}
            {/* Language Section */}

            {mode === 'edit' && (
              <Grid item xs={12}>
                <Typography variant='h6' sx={{ mb: 2 }}>
                  {t('forms.usersForm.sections.accountDetails')}
                </Typography>

                <Box>
                  <Typography color={'primary.main'}>{t('forms.usersForm.fields.joinedDate')}</Typography>
                  <Typography>
                    {initialData?.date_joined ? moment(initialData?.date_joined).format('LLL') : ''}
                  </Typography>
                </Box>

                <Box>
                  <Typography color={'primary.main'}>{t('forms.usersForm.fields.lastConnection')}</Typography>
                  <Typography>
                    {initialData?.last_login ? moment(initialData?.last_login).format('LLL') : ''}
                  </Typography>
                </Box>
              </Grid>
            )}

            {mode === 'edit' && (
              <>
                <Grid item xs={12}>
                  <Grid container spacing={3}>
                    <Grid item xs={12} md={4}>
                      <Controller
                        name='language'
                        control={control}
                        render={({ field }) => (
                          <CustomTextField
                            {...field}
                            select
                            fullWidth
                            label={t('forms.usersForm.fields.language')}
                            error={Boolean(errors.language)}
                            helperText={errors.language?.message}
                          >
                            {languages.map(option => (
                              <MenuItem key={option.value} value={option.value}>
                                {option.label}
                              </MenuItem>
                            ))}
                          </CustomTextField>
                        )}
                      />
                    </Grid>

                    <Grid item xs={12} md={4}>
                      <Controller
                        name='supervisor_id'
                        control={control}
                        render={({ field: { value, onChange } }) => (
                          <CustomAutocomplete
                            multiple
                            options={supervisors || []}
                            loading={isLoadingSupervisors}
                            value={supervisors?.filter((article: any) => value?.includes(article.id)) || []}
                            onChange={(_, newValue) => onChange(newValue.map((item: any) => item.id))}
                            getOptionLabel={option => `${option.first_name} ${option.last_name}`}
                            isOptionEqualToValue={(option, value) => option.id === value.id}
                            renderInput={params => (
                              <CustomTextField
                                {...params}
                                label={t('forms.usersForm.fields.supervisor')}
                                error={Boolean(errors.supervisor_id)}
                                helperText={errors.supervisor_id?.message}
                              />
                            )}
                          />
                        )}
                      />
                    </Grid>
                  </Grid>
                </Grid>
                {/* Permissions Section */}
                <Grid item xs={12}>
                  <Grid container spacing={3}>
                    <Grid item xs={12} md={4}>
                      <Controller
                        name='type_of_operation'
                        control={control}
                        render={({ field }) => (
                          <CustomTextField
                            {...field}
                            select
                            fullWidth
                            label={t('forms.usersForm.fields.typeOfOperation')}
                            error={Boolean(errors.type_of_operation)}
                            helperText={errors.type_of_operation?.message}
                          >
                            {containerShape.map(option => (
                              <MenuItem key={option.value} value={option.value}>
                                {option.label}
                              </MenuItem>
                            ))}
                          </CustomTextField>
                        )}
                      />
                    </Grid>
                    <Grid item xs={12} md={4}>
                      <Controller
                        name='user_permissions'
                        control={control}
                        render={({ field }) => (
                          <GenericMultiSelect
                            options={permissions?.pages.flatMap(page => page.results || []) || []}
                            value={field.value}
                            getOptionLabel={permission =>
                              `${permission?.content_type?.app_label} | ${permission?.content_type.model} | ${permission.name} `
                            }
                            isOptionEqualToValue={(option, value) => option.id === value.id}
                            onChange={field.onChange}
                            loading={isLoadingPermissions}
                            error={Boolean(errors.user_permissions)}
                            helperText={errors.user_permissions?.message as string}
                            hasMore={!!hasMorePermissions}
                            loadMore={() => fetchNextPagePermissions()}
                            label={t('forms.usersForm.fields.permissions') as string}
                          />
                        )}
                      />
                    </Grid>
                  </Grid>
                </Grid>

                {/* Groups Section */}
                <Grid item xs={12}>
                  <Grid container spacing={3}>
                    <Grid item xs={12} md={4}>
                      <Controller
                        name='main_department_id'
                        control={control}
                        render={({ field }) => (
                          <AsyncInfiniteSelect
                            fetchByIdFn={getUserDepartmentById}
                            getOptionLabel={department => department.name}
                            getOptionValue={department => String(department.id)}
                            isOptionEqualToValue={(option, value) => String(option.id) === value}
                            label={t('forms.usersForm.fields.mainDepartment') as string}
                            value={field.value || null}
                            onChange={field.onChange}
                            isRequired
                            queryKey={'user-departments'}
                            fetchFn={fetchInfiniteUserDepartments}
                            placeholder={'search department..'}
                            size='small'
                            minSearchLength={2}
                            debounceMs={400}
                            prefetch
                          />
                        )}
                      />
                    </Grid>
                    <Grid item xs={12} md={4}>
                      <Controller
                        name='groups'
                        control={control}
                        render={({ field }) => (
                          <GenericMultiSelect
                            options={groups?.pages.flatMap(page => page.results || []) || []}
                            value={field.value}
                            getOptionLabel={group => {
                              return group.name
                            }}
                            isOptionEqualToValue={(option, value) => option.id === value.id}
                            onChange={field.onChange}
                            loading={isLoadingGroups}
                            hasMore={!!hasMoreGroups}
                            loadMore={() => fetchNextPageGroups()}
                            label={t('forms.usersForm.fields.groups') as string}
                          />
                        )}
                      />
                    </Grid>
                  </Grid>
                </Grid>

                {/* Annex Departments Section */}
                <Grid item xs={12}>
                  <Grid container spacing={3}>
                    <Grid item xs={12} md={4}>
                      <Controller
                        name='annex_departments_ids'
                        control={control}
                        render={({ field }) => (
                          <GenericMultiSelect
                            options={userDepartments?.pages.flatMap(page => page.results || []) || []}
                            value={field.value}
                            getOptionLabel={group => group.name}
                            isOptionEqualToValue={(option, value) => option.id === value.id}
                            onChange={field.onChange}
                            loading={isLoadingUserDepartments}
                            hasMore={!!hasMoreUserDepartments}
                            loadMore={() => fetchNextPageUserDepartments()}
                            label={t('forms.usersForm.fields.annexDepartments') as string}
                          />
                        )}
                      />
                    </Grid>
                  </Grid>
                </Grid>

                {/* Permissions Section */}
                <Grid item xs={12}>
                  <Typography variant='h6' sx={{ mb: 2 }}>
                    {t('forms.usersForm.sections.permissions')}
                  </Typography>
                  <Grid container spacing={3}>
                    <Grid item xs={12} md={6}>
                      <Box sx={{ mt: 2 }}>
                        <Controller
                          name='is_active'
                          control={control}
                          render={({ field: { value, onChange } }) => (
                            <FormControlLabel
                              control={<Switch checked={value} onChange={onChange} />}
                              label={t('forms.usersForm.fields.isActive')}
                            />
                          )}
                        />
                        <Typography variant='body2' display='block' sx={{ ml: 4, mb: 2 }}>
                          {t('forms.usersForm.descriptions.active')}
                        </Typography>

                        <Controller
                          name='is_staff'
                          control={control}
                          render={({ field: { value, onChange } }) => (
                            <FormControlLabel
                              control={<Switch checked={value} onChange={onChange} />}
                              label={t('forms.usersForm.fields.groupStatus')}
                            />
                          )}
                        />
                        <Typography variant='body2' display='block' sx={{ ml: 4, mb: 2 }}>
                          {t('forms.usersForm.descriptions.groupStatus')}
                        </Typography>

                        <Controller
                          name='is_superuser'
                          control={control}
                          render={({ field: { value, onChange } }) => (
                            <FormControlLabel
                              control={<Switch checked={value} onChange={onChange} />}
                              label={t('forms.usersForm.fields.isSuperuser')}
                            />
                          )}
                        />
                        <Typography variant='body2' display='block' sx={{ ml: 4 }}>
                          {t('forms.usersForm.descriptions.isSuperuser')}
                        </Typography>
                      </Box>
                    </Grid>
                  </Grid>
                </Grid>
              </>
            )}
          </Grid>

          <Box sx={{ mt: 5, display: 'flex', justifyContent: 'flex-end' }}>
            <FormButtons
              isLoading={isCreating || isUpdating}
              disabled={isLoading}
              mode={mode}
              cancelPath='/user-management/users'
              onSave={handleSubmit(onSubmit)}
              onSaveAndAddNew={handleSubmit(handleSaveAndAddNew)}
              onSaveAndEdit={handleSubmit(handleSaveAndEdit)}
            />
          </Box>
        </form>
      </CardContent>
    </Card>
  )
}
