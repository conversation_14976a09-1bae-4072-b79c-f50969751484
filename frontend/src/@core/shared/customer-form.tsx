import React from 'react'
import { useForm, Controller } from 'react-hook-form'
import { yupResolver } from '@hookform/resolvers/yup'
import { Box, Card, CardContent, CardHeader, Grid } from '@mui/material'
import CustomTextField from 'src/@core/components/mui/text-field'
import FormActions from 'src/@core/components/form-buttons'
import { useCreateCustomer, useUpdateCustomer } from 'src/hooks/useCustomer'
import { customerSchema } from 'src/lib/schemas'
import toast from 'react-hot-toast'
import { useTranslation } from 'react-i18next'
import { useQueryClient } from '@tanstack/react-query'
import { useRouter } from 'next/router'

interface CustomerFormProps {
  initialData?: any
  mode?: 'create' | 'edit'
  customerId?: string
}

const CustomerForm: React.FC<CustomerFormProps> = ({ initialData, mode = 'create', customerId }) => {
  const { t } = useTranslation()
  const queryClient = useQueryClient()
  const router = useRouter()

  const {
    control,
    handleSubmit,
    formState: { errors },
    reset,
    watch,
    setValue
  } = useForm({
    defaultValues: initialData || {
      sap_uid: '',
      name: '',
      citerne_price_diff: '',
      other_info: '',
      address: '',
      phone1: '',
      phone2: '',
      groupcode: '',
      zipcode: '',
      mailaddres: '',
      cmpprivate: '',
      mailzipcod: '',
      addid: '',
      currency: '',
      cardtype: '',
      lictradnum: ''
    },
    resolver: yupResolver(customerSchema)
  })

  const {
    mutate: createCustomer,
    isPending: isCreating,
    data: createCustomerRes
  } = useCreateCustomer({
    onSuccess: () => {
      toast.success(t('forms.customerForm.messages.create_success'))
      queryClient.invalidateQueries({ queryKey: ['customers'] })
    },
    onError: () => {
      toast.error(t('forms.customerForm.messages.create_error'))
    }
  })
  const { mutate: updateCustomer, isPending: isUpdating } = useUpdateCustomer({
    onSuccess: () => {
      toast.success(t('forms.customerForm.messages.update_success'))
      queryClient.invalidateQueries({ queryKey: ['customers'] })
    },
    onError: () => {
      toast.error(t('forms.customerForm.messages.update_error'))
    }
  })

  const onSubmit = (data: any) => {
    if (mode === 'create') {
      createCustomer(data)
    } else {
      updateCustomer({ id: customerId, ...data })
    }
  }
  const handleSaveAndReturn = (data: any) => {
    onSubmit(data)
    router.push(' /user-management/customers')
  }

  const handleSaveAndEdit = (data: any) => {
    onSubmit(data)
    if (mode === 'create' && createCustomerRes?.id) {
      router.push(`/user-management/customers/${createCustomerRes.id}/edit`)
    }
  }

  const handleSaveAndAddNew = (data: any) => {
    onSubmit(data)
    reset()
  }

  return (
    <Card>
      <CardHeader title={t(`forms.customerForm.${mode === 'create' ? 'create_title' : 'edit_title'}`)} />
      <CardContent>
        <form onSubmit={handleSubmit(handleSaveAndReturn)}>
          <Grid container spacing={5}>
            <Grid item xs={12} sm={4}>
              <Controller
                name='sap_uid'
                control={control}
                render={({ field }) => (
                  <CustomTextField
                    {...field}
                    fullWidth
                    label={t('forms.customerForm.fields.sap_uid')}
                    error={Boolean(errors.sap_uid)}
                    helperText={errors.sap_uid?.message as string}
                  />
                )}
              />
            </Grid>

            <Grid item xs={12} sm={4}>
              <Controller
                name='citerne_price_diff'
                control={control}
                render={({ field }) => (
                  <CustomTextField
                    {...field}
                    fullWidth
                    label={t('forms.customerForm.fields.citerne_price_diff')}
                    error={Boolean(errors.citerne_price_diff)}
                    helperText={errors.citerne_price_diff?.message as string}
                  />
                )}
              />
            </Grid>
            <Grid item xs={12} sm={4}>
              <Controller
                name='name'
                control={control}
                render={({ field }) => (
                  <CustomTextField
                    {...field}
                    fullWidth
                    label={t('forms.customerForm.fields.name')}
                    error={Boolean(errors.name)}
                    helperText={errors.name?.message as string}
                  />
                )}
              />
            </Grid>
            <Grid item xs={12} sm={4}>
              <Controller
                name='address'
                control={control}
                render={({ field }) => (
                  <CustomTextField
                    {...field}
                    fullWidth
                    label={t('forms.customerForm.fields.address')}
                    error={Boolean(errors.address)}
                    helperText={errors.address?.message as string}
                  />
                )}
              />
            </Grid>
            <Grid item xs={12} sm={4}>
              <Controller
                name='phone1'
                control={control}
                render={({ field }) => (
                  <CustomTextField
                    {...field}
                    fullWidth
                    label={t('forms.customerForm.fields.phone1')}
                    error={Boolean(errors.phone1)}
                    helperText={errors.phone1?.message as string}
                  />
                )}
              />
            </Grid>
            <Grid item xs={12} sm={4}>
              <Controller
                name='phone2'
                control={control}
                render={({ field }) => (
                  <CustomTextField
                    {...field}
                    fullWidth
                    label={t('forms.customerForm.fields.phone2')}
                    error={Boolean(errors.phone2)}
                    helperText={errors.phone2?.message as string}
                  />
                )}
              />
            </Grid>
            <Grid item xs={12} sm={4}>
              <Controller
                name='groupcode'
                control={control}
                render={({ field }) => (
                  <CustomTextField
                    {...field}
                    fullWidth
                    label={t('forms.customerForm.fields.groupcode')}
                    error={Boolean(errors.groupcode)}
                    helperText={errors.groupcode?.message as string}
                  />
                )}
              />
            </Grid>
            <Grid item xs={12} sm={4}>
              <Controller
                name='zipcode'
                control={control}
                render={({ field }) => (
                  <CustomTextField
                    {...field}
                    fullWidth
                    label={t('forms.customerForm.fields.zipcode')}
                    error={Boolean(errors.zipcode)}
                    helperText={errors.zipcode?.message as string}
                  />
                )}
              />
            </Grid>
            <Grid item xs={12} sm={4}>
              <Controller
                name='mailaddres'
                control={control}
                render={({ field }) => (
                  <CustomTextField
                    {...field}
                    fullWidth
                    label={t('forms.customerForm.fields.mailaddres')}
                    error={Boolean(errors.mailaddres)}
                    helperText={errors.mailaddres?.message as string}
                  />
                )}
              />
            </Grid>
            <Grid item xs={12} sm={4}>
              <Controller
                name='cmpprivate'
                control={control}
                render={({ field }) => (
                  <CustomTextField
                    {...field}
                    fullWidth
                    label={t('forms.customerForm.fields.cmpprivate')}
                    error={Boolean(errors.cmpprivate)}
                    helperText={errors.cmpprivate?.message as string}
                  />
                )}
              />
            </Grid>
            <Grid item xs={12} sm={4}>
              <Controller
                name='mailzipcod'
                control={control}
                render={({ field }) => (
                  <CustomTextField
                    {...field}
                    fullWidth
                    label={t('forms.customerForm.fields.mailzipcod')}
                    error={Boolean(errors.mailzipcod)}
                    helperText={errors.mailzipcod?.message as string}
                  />
                )}
              />
            </Grid>
            <Grid item xs={12} sm={4}>
              <Controller
                name='addid'
                control={control}
                render={({ field }) => (
                  <CustomTextField
                    {...field}
                    fullWidth
                    label={t('forms.customerForm.fields.addid')}
                    error={Boolean(errors.addid)}
                    helperText={errors.addid?.message as string}
                  />
                )}
              />
            </Grid>
            <Grid item xs={12} sm={4}>
              <Controller
                name='currency'
                control={control}
                render={({ field }) => (
                  <CustomTextField
                    {...field}
                    fullWidth
                    label={t('forms.customerForm.fields.currency')}
                    error={Boolean(errors.currency)}
                    helperText={errors.currency?.message as string}
                  />
                )}
              />
            </Grid>
            <Grid item xs={12} sm={4}>
              <Controller
                name='cardtype'
                control={control}
                render={({ field }) => (
                  <CustomTextField
                    {...field}
                    fullWidth
                    label={t('forms.customerForm.fields.cardtype')}
                    error={Boolean(errors.cardtype)}
                    helperText={errors.cardtype?.message as string}
                  />
                )}
              />
            </Grid>
            <Grid item xs={12} sm={4}>
              <Controller
                name='lictradnum'
                control={control}
                render={({ field }) => (
                  <CustomTextField
                    {...field}
                    fullWidth
                    label={t('forms.customerForm.fields.lictradnum')}
                    error={Boolean(errors.lictradnum)}
                    helperText={errors.lictradnum?.message as string}
                  />
                )}
              />
            </Grid>
            {/* Add more form fields here */}
          </Grid>
          <Box sx={{ mt: 5 }}>
            <FormActions
              cancelPath={'/user-management/customers'}
              mode={mode}
              onSave={handleSubmit(handleSaveAndReturn)}
              onSaveAndEdit={handleSubmit(handleSaveAndEdit)}
              onSaveAndAddNew={handleSubmit(handleSaveAndAddNew)}
            />
          </Box>
        </form>
      </CardContent>
    </Card>
  )
}

export default CustomerForm
