import { useState } from 'react'
import { Box, IconButton, FormHelperText, Select, MenuItem, TextField, Button, Typography, Card } from '@mui/material'
import Icon from 'src/@core/components/icon'
import { useTranslation } from 'react-i18next'
import { fetchInfiniteTractors, fetchInfiniteTrailers, getVehicleById } from 'src/services/vehicles'
import CustomTextField from '../components/mui/text-field'
import { fetchInfiniteProduct, getProductById } from 'src/services/_service/products'
import { fetchInfiniteNatureOfArticles, getNatureOfArticleById } from 'src/services/_service/nature-of-articles'
import { fetchInfiniteDriver, getDriverById } from 'src/services/drivers'
import { fetchInfiniteAgency, getAgencyById } from 'src/services/agencies'
import { OrderOperationLine } from 'src/types/models/_services/orders'
import AsyncInfiniteSelect from './components/infinite-select'
import { Vehicle } from 'src/types/models/vehicles'
import { fetchInfinitePath, getPathById } from 'src/services/_service/path'
import { useDeleteInvoiceItem } from 'src/hooks/_services/useInvoiceItems'
import toast from 'react-hot-toast'
import ConfirmationModal from 'src/@core/components/modals/ConfirmationModal'
import { selectUser } from 'src/store/auth'
import { useSelector } from 'react-redux'
import { getArticleById } from 'src/services/_service/article'

interface OrderOperationsFormProps {
  value: OrderOperationLine[]
  onChange: (operations: any[]) => void
  error?: any
  mode?: 'create' | 'edit'
  control?: any
  client?: string
  meta?: {
    author: boolean
    isValidator: boolean
    isValidate: boolean
  }
}

const directionOptions = [
  {
    label: 'Normal',
    value: 'NORMAL'
  },
  {
    label: 'Contraire',
    value: 'REVERSE'
  }
]

const OrderOperationsForm = ({
  value,
  onChange,
  error,
  mode = 'create',
  control,
  meta,
  client
}: OrderOperationsFormProps) => {
  const { t } = useTranslation()
  const user = useSelector(selectUser)

  const { mutateAsync: deleteInvoiceLine } = useDeleteInvoiceItem({
    onSuccess() {
      toast.success('order operation removed with success')
    },
    onError() {
      toast.error('Error occured removing operation line')
    }
  })

  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [operationToDelete, setOperationToDelete] = useState<{ index: number; id?: string | null }>({ index: -1 })

  const handleAddOperation = () => {
    onChange([...value, { direction: 'NORMAL', validation: 'EN ATTENTE' }])
  }

  const handleDeleteClick = (index: number, operationId?: string) => {
    setOperationToDelete({ index, id: operationId })
    setDeleteDialogOpen(true)
  }
  const remove = (index: number) => {
    const newOperations = [...value]
    newOperations.splice(index, 1)
    onChange(newOperations)
  }

  const handleDeleteConfirm = async () => {
    const { index, id } = operationToDelete
    if (index !== -1) {
      if (mode === 'edit' && id) await deleteInvoiceLine({ id })
      remove(index)
      setDeleteDialogOpen(false)
      setOperationToDelete({ index: -1 })
    }
  }

  const handleDeleteCancel = () => {
    setDeleteDialogOpen(false)
    setOperationToDelete({ index: -1 })
  }

  const handleOperationChange = (index: number, field: keyof OrderOperationLine, newValue: any) => {
    const newOperations = [...value]
    newOperations[index] = {
      ...newOperations[index],
      [field]: typeof newValue === 'object' ? newValue?.value || '' : newValue
    }
    onChange(newOperations)
  }

  const getFieldError = (index: number, field: string): string | undefined => {
    if (!error?.[index]?.[field]) return undefined

    return error[index][field].message
  }

  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', gap: 4 }}>
      {value.map((operation, index) => (
        <Card key={index} sx={{ p: 4 }}>
          <Box
            sx={{
              display: 'grid',
              gap: 4,
              gridTemplateColumns: {
                xs: '1fr',
                md: '1fr 1fr',
                lg: '1fr 1fr 1fr 1fr'
              }
            }}
          >
            <Box>
              <AsyncInfiniteSelect
                value={operation.tractor_id}
                fetchByIdFn={getVehicleById}
                label={t('forms.orderForm.operationForm.tractor') as string}
                placeholder={'search tractor'}
                getOptionLabel={vehicle => vehicle.number_plate}
                getOptionValue={vehicle => String(vehicle.id)}
                isRequired
                prefetch
                isOptionEqualToValue={(option, value) => String(option.id) === value}
                onChange={newValue => handleOperationChange(index, 'tractor_id', newValue)}
                queryKey={'tractors'}
                fetchFn={fetchInfiniteTractors}
                size='small'
                error={!!getFieldError(index, 'tractor_id')}
              />
              {getFieldError(index, 'tractor_id') && (
                <FormHelperText error>{getFieldError(index, 'tractor_id')}</FormHelperText>
              )}
            </Box>

            <Box>
              <AsyncInfiniteSelect
                fetchByIdFn={getVehicleById}
                value={operation.trailer_id}
                label={t('forms.orderForm.operationForm.trailer') as string}
                getOptionLabel={v => v.number_plate}
                getOptionValue={v => String(v.id)}
                isOptionEqualToValue={(option, value) => String(option.id) === value}
                onChange={newValue => handleOperationChange(index, 'trailer_id', newValue)}
                queryKey={'trailers'}
                fetchFn={fetchInfiniteTrailers}
                placeholder={'search trailer'}
                size='small'
                prefetch
              />
            </Box>

            <Box>
              <AsyncInfiniteSelect
                value={operation.path_id}
                fetchByIdFn={getPathById}
                label={t('forms.orderForm.operationForm.route') as string}
                onChange={newValue => handleOperationChange(index, 'path_id', newValue)}
                queryKey={'routes'}
                fetchFn={fetchInfinitePath}
                isRequired
                placeholder={'search path'}
                getOptionLabel={path => path.name}
                getOptionValue={path => String(path.id)}
                isOptionEqualToValue={(option, value) => String(option.id) === value}
                size='small'
                prefetch
                error={!!getFieldError(index, 'path_id')}
              />
              {getFieldError(index, 'path_id') && (
                <FormHelperText error>{getFieldError(index, 'path_id')}</FormHelperText>
              )}
            </Box>
            <Box>
              <AsyncInfiniteSelect
                value={operation.driver_id}
                fetchByIdFn={getDriverById}
                label={t('forms.orderForm.operationForm.driver') as string}
                onChange={newValue => handleOperationChange(index, 'driver_id', newValue)}
                queryKey={'driver'}
                fetchFn={fetchInfiniteDriver}
                isRequired
                placeholder={''}
                getOptionLabel={driver => `${driver.first_name} ${driver.last_name}`}
                getOptionValue={driver => String(driver.id)}
                isOptionEqualToValue={(option, value) => String(option.id) === value}
                size='small'
                prefetch
                error={!!getFieldError(index, 'driver_id')}
              />
              {getFieldError(index, 'driver_id') && (
                <FormHelperText error>{getFieldError(index, 'driver_id')}</FormHelperText>
              )}
            </Box>
            <Box>
              <Typography variant='body2' sx={{ mb: 1, display: 'block' }}>
                {t('forms.orderForm.operationForm.direction')}
              </Typography>
              <Select
                size='small'
                fullWidth
                value={operation.direction}
                onChange={e => handleOperationChange(index, 'direction', e.target.value)}
              >
                {directionOptions.map(option => (
                  <MenuItem key={option.value} value={option.value}>
                    {t(option.label)}
                  </MenuItem>
                ))}
              </Select>
            </Box>

            <Box>
              {/* <Typography variant='caption' sx={{ mb: 1, display: 'block' }}>
                {t('forms.orderForm.operationForm.be_number')}
              </Typography> */}
              <CustomTextField
                label={t('forms.orderForm.operationForm.be_number')}
                size='small'
                fullWidth
                value={operation.be_number}
                onChange={e => handleOperationChange(index, 'be_number', e.target.value)}
              />
            </Box>

            <Box>
              {/* <Typography variant='caption' sx={{ mb: 1, display: 'block' }}>
                {t('forms.orderForm.operationForm.bc_number')}
              </Typography> */}
              <CustomTextField
                label={t('forms.orderForm.operationForm.bc_number')}
                size='small'
                fullWidth
                value={operation.bc_number}
                onChange={e => handleOperationChange(index, 'bc_number', e.target.value)}
              />
            </Box>

            <Box>
              {/* <Typography variant='caption' sx={{ mb: 1, display: 'block' }}>
                {t('forms.orderForm.operationForm.product')}
              </Typography> */}
              <AsyncInfiniteSelect
                value={operation.product_id}
                fetchByIdFn={getArticleById}
                label={t('forms.orderForm.operationForm.product') as string}
                isRequired
                onChange={newValue => handleOperationChange(index, 'product_id', newValue)}
                queryKey={'product'}
                fetchFn={fetchInfiniteProduct}
                prefetch
                placeholder={'search product'}
                getOptionLabel={article => article.label}
                getOptionValue={artticle => String(artticle.id)}
                isOptionEqualToValue={(option, value) => String(option.id) === value}
                size='small'
                error={!!getFieldError(index, 'product_id')}
              />
              {getFieldError(index, 'product_id') && (
                <FormHelperText error>{getFieldError(index, 'product_id')}</FormHelperText>
              )}
            </Box>

            {user?.type_of_operation === 'BENNES' && (
              <Box>
                <AsyncInfiniteSelect
                  label={t('forms.orderForm.operationForm.product_nature') as string}
                  value={operation.nature_product_id}
                  onChange={newValue => handleOperationChange(index, 'nature_product_id', newValue)}
                  fetchByIdFn={getNatureOfArticleById}
                  queryKey={'product_nature'}
                  fetchFn={fetchInfiniteNatureOfArticles}
                  prefetch
                  placeholder={'search type of product'}
                  getOptionLabel={product => product.name}
                  getOptionValue={product => String(product.id)}
                  isOptionEqualToValue={(option, value) => String(option.id) === value}
                  size='small'
                />
              </Box>
            )}

            <Box>
              <AsyncInfiniteSelect
                value={operation.agence_id}
                isRequired
                fetchByIdFn={getAgencyById}
                label={t('forms.orderForm.operationForm.client_agency') as string}
                onChange={newValue => handleOperationChange(index, 'agence_id', newValue)}
                queryKey={'agencies'}
                fetchFn={({ pageParam, searchQuery }) => {
                  return fetchInfiniteAgency({ pageParam, searchQuery, company: client })
                }}
                placeholder={'search  Agency'}
                getOptionLabel={agency => agency.name}
                prefetch
                getOptionValue={agency => String(agency.id)}
                isOptionEqualToValue={(option, value) => String(option.id) === value}
                size='small'
              />
            </Box>

            <Box>
              <CustomTextField
                size='small'
                label={t('forms.orderForm.operationForm.quantity')}
                fullWidth
                required
                type='number'
                value={operation.qty}
                onChange={e => handleOperationChange(index, 'qty', e.target.value)}
                error={!!getFieldError(index, 'qty')}
                helperText={getFieldError(index, 'qty')}
              />
            </Box>

            <Box>
              <CustomTextField
                size='small'
                label={t('forms.orderForm.operationForm.road_fees')}
                fullWidth
                type='number'
                value={operation.road_fees}
                onChange={e => handleOperationChange(index, 'road_fees', e.target.value)}
              />
            </Box>

            <Box>
              <CustomTextField
                size='small'
                label={t('forms.orderForm.operationForm.fuel_volume')}
                required
                fullWidth
                type='number'
                value={operation.petrol_volume}
                onChange={e => handleOperationChange(index, 'petrol_volume', e.target.value)}
                error={!!getFieldError(index, 'petrol_volume')}
                helperText={getFieldError(index, 'petrol_volume')}
              />
            </Box>

            <Box>
              <CustomTextField
                label={t('forms.orderForm.operationForm.unit_price')}
                size='small'
                fullWidth
                type='number'
                disabled={user?.type_of_operation === 'BENNE' || user?.type_of_operation === 'CITERNE' ? true : false}
                value={operation.price}
                onChange={e => handleOperationChange(index, 'price', e.target.value)}
              />
            </Box>

            <Box>
              <Typography sx={{ mb: 1, display: 'block' }}>
                {t('forms.orderForm.operationForm.unloaded_quantity')}
              </Typography>
              <Typography style={{ fontWeight: 'bold' }}>0.0</Typography>
            </Box>
            <Box>
              <Typography sx={{ mb: 1, display: 'block' }}>{t('forms.orderForm.operationForm.validation')}</Typography>
              <Typography style={{ fontWeight: 'bold' }}>{operation.validation}</Typography>
            </Box>

            {!meta?.isValidate && (
              <Box sx={{ display: 'flex', justifyContent: 'flex-end', alignItems: 'flex-end' }}>
                {mode === 'edit' ? (
                  <IconButton color='error' onClick={() => handleDeleteClick(index, operation.id)}>
                    <Icon icon='tabler:trash' />
                  </IconButton>
                ) : (
                  <IconButton color='error' onClick={() => remove(index)}>
                    <Icon icon='tabler:trash' />
                  </IconButton>
                )}
              </Box>
            )}
          </Box>
        </Card>
      ))}

      {mode === 'edit' ? (
        meta?.isValidator || meta?.isValidate ? null : (
          <Box sx={{ mt: 6 }}>
            <Button variant='contained' color='primary' onClick={handleAddOperation}>
              <Icon icon='mdi:plus' /> {t('forms.orderForm.addOperation')}
            </Button>
          </Box>
        )
      ) : (
        client && (
          <Box sx={{ mt: 6 }}>
            <Button variant='contained' color='primary' onClick={handleAddOperation}>
              <Icon icon='mdi:plus' /> {t('forms.orderForm.addOperation')}
            </Button>
          </Box>
        )
      )}

      {mode === 'edit' && (
        <ConfirmationModal
          open={deleteDialogOpen}
          title={t('forms.orderForm.deleteDialog.title')}
          message={t('forms.orderForm.deleteDialog.message')}
          confirmButtonText={t('forms.orderForm.deleteDialog.confirm') as string}
          cancelButtonText={t('forms.orderForm.deleteDialog.cancel') as string}
          onConfirm={handleDeleteConfirm}
          onCancel={handleDeleteCancel}
        />
      )}
    </Box>
  )
}

export default OrderOperationsForm
