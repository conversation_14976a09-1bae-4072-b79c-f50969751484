import { Box, Typography } from '@mui/material'

interface DetailRowProps {
  label: string
  value: string | number | null | undefined
}

export const DetailRow = ({ label, value }: DetailRowProps) => {
  return (
    <Box sx={{ py: 2, display: 'flex' }}>
      <Typography sx={{ mr: 2, fontWeight: 500, width: 200 }}>{label}:</Typography>
      <Typography sx={{ color: 'text.secondary' }}>{value ?? 'N/A'}</Typography>
    </Box>
  )
}
