import {
  Box,
  Card,
  CardContent,
  Checkbox,
  FormControlLabel,
  FormGroup,
  FormHelperText,
  FormLabel,
  Grid,
  MenuItem
} from '@mui/material'
import { useForm, Controller } from 'react-hook-form'
import { yupResolver } from '@hookform/resolvers/yup'
import { useRouter } from 'next/router'
import CustomTextField from 'src/@core/components/mui/text-field'
import FormActions from 'src/@core/components/form-buttons'
import { VehicleRequest } from 'src/types/models/vehicles'
import { useCreateVehicle, useUpdateVehicle } from 'src/hooks/useVehicle'
import { useQueryClient } from '@tanstack/react-query'
import { vehicleSchema } from 'src/lib/schemas'

interface VehicleFormProps {
  initialData?: any
  mode?: 'create' | 'edit'
  vehicleId?: string
}

const vehicleTypes = [
  { value: 'REMORQUE', label: 'REMORQUE' },
  { value: 'CAMION', label: 'CAMION' },
  { value: 'TRACTEUR', label: 'TRACTEUR' },
  { value: 'PICKUP', label: 'PICKUP' },
  { value: 'HEAD_OFFICE', label: 'HEAD_OFFICE' }
]
const containerShape = [
  { label: 'Citerne', value: 'CITERNE' },
  { label: 'Plateaux', value: 'PLATEAU' },
  { label: 'Bennes', value: 'BENNE' },
  { label: 'Kangoo', value: 'KANGOO' },
  { label: 'Pickup', value: 'PIK-UP' },
  { label: 'Bennes et Plateaux', value: 'BENNE_PLATEAU' },
  { label: "Fourgonnette d'intervention", value: 'FOURGONNETTE_INTERVENTION' }
]
export default function VehicleForm({ initialData, mode = 'create', vehicleId }: VehicleFormProps) {
  const router = useRouter()
  const queryClient = useQueryClient()

  const {
    mutateAsync: createVehicle,
    data: createVehicleRes,
    isPending: pendingCreate,
    isSuccess: successCreate
  } = useCreateVehicle({})
  const {
    mutateAsync: updateVehicle,
    isPending: pendingUpdate,
    isSuccess: sucessUpdate
  } = useUpdateVehicle({
    onSuccess() {
      queryClient.invalidateQueries({ queryKey: ['vehicles'] })
    }
  })

  const {
    control,
    handleSubmit,
    reset,
    formState: { errors }
  } = useForm<VehicleRequest>({
    resolver: yupResolver(vehicleSchema),
    defaultValues: initialData || {
      code: '',
      number_plate: '',
      consumption_card_number: '',
      vehicle_type: 'TRACTEUR',
      container: '',
      kilometer_old: 0,
      kilometer_new: 0,
      petrol_consumption: '',
      long: '',
      lat: '',
      is_active: false
    }
  })

  const handleSaveAndReturn = async (formData: VehicleRequest) => {
    try {
      if (mode === 'create') {
        await createVehicle({ ...formData })
        router.push('/vehicles')
      } else {
        await updateVehicle({ data: formData, id: vehicleId })
        router.push('/vehicles')
      }
    } catch (error) {}
  }

  const handleSaveAndEdit = async (formData: VehicleRequest) => {
    try {
      await createVehicle({ ...formData })
      router.push('/vehicles')
      if (mode === 'create' && createVehicleRes?.id) {
        router.push(`/vehicles/${createVehicleRes.id}/edit`)
      }
    } catch (error) {
      // Error handled by mutation callbacks
    }
  }

  const handleSaveAndAddNew = async (formData: VehicleRequest) => {
    try {
      await createVehicle({ ...formData })
      if (mode === 'create') {
        reset()
      }
    } catch (error) {
      // Error handled by mutation callbacks
    }
  }

  return (
    <Card>
      <CardContent>
        <form>
          <Grid container spacing={5}>
            <Grid item xs={12} sm={6}>
              <Controller
                name='code'
                control={control}
                render={({ field }) => (
                  <CustomTextField
                    fullWidth
                    label='SAP Code'
                    {...field}
                    error={Boolean(errors.code)}
                    helperText={errors.code?.message}
                  />
                )}
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <Controller
                name='number_plate'
                control={control}
                render={({ field }) => (
                  <CustomTextField
                    fullWidth
                    label='Number Plate'
                    {...field}
                    error={Boolean(errors.number_plate)}
                    helperText={errors.number_plate?.message}
                  />
                )}
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <Controller
                name='vehicle_type'
                control={control}
                render={({ field }) => (
                  <CustomTextField
                    select
                    fullWidth
                    label='Vehicle Type'
                    {...field}
                    error={Boolean(errors.vehicle_type)}
                    helperText={errors.vehicle_type?.message}
                  >
                    {vehicleTypes.map(type => (
                      <MenuItem key={type.value} value={type.value}>
                        {type.label}
                      </MenuItem>
                    ))}
                  </CustomTextField>
                )}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <Controller
                name='container'
                control={control}
                render={({ field }) => (
                  <CustomTextField
                    select
                    fullWidth
                    label='Container'
                    {...field}
                    error={Boolean(errors.container)}
                    helperText={errors.container?.message}
                  >
                    {containerShape.map(option => (
                      <MenuItem key={option.value} value={option.value}>
                        {option.label}
                      </MenuItem>
                    ))}
                  </CustomTextField>
                )}
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <Controller
                name='kilometer_old'
                control={control}
                render={({ field }) => (
                  <CustomTextField
                    fullWidth
                    type='number'
                    label='Kilometer Old'
                    {...field}
                    onChange={e => field.onChange(Number(e.target.value))}
                    error={Boolean(errors.kilometer_old)}
                    helperText={errors.kilometer_old?.message}
                    InputProps={{
                      // readOnly: mode === 'edit' ? true : false
                      readOnly: true
                    }}
                  />
                )}
              />
            </Grid>

            {/* {mode === 'edit' && (
              <Grid item xs={12} sm={6}>
                <Controller
                  name='kilometer_new'
                  control={control}
                  render={({ field }) => (
                    <CustomTextField
                      fullWidth
                      type='number'
                      label='Kilometer New'
                      {...field}
                      onChange={e => field.onChange(Number(e.target.value))}
                      error={Boolean(errors.kilometer_new)}
                      helperText={errors.kilometer_new?.message}
                      InputProps={{
                        readOnly: true
                      }}
                      sx={{
                        '& .Mui-disabled': {
                          '-webkit-text-fill-color': 'rgba(58, 53, 65, 0.87)'
                        }
                      }}
                    />
                  )}
                />
              </Grid>
            )} */}

            <Grid item xs={12} sm={6}>
              <Controller
                name='consumption_card_number'
                control={control}
                render={({ field }) => (
                  <CustomTextField
                    fullWidth
                    label='Consumption Card Number'
                    {...field}
                    error={Boolean(errors.consumption_card_number)}
                    helperText={errors.consumption_card_number?.message}
                  />
                )}
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <Controller
                name='petrol_consumption'
                control={control}
                render={({ field }) => (
                  <CustomTextField
                    fullWidth
                    label='Petrol Consumption'
                    {...field}
                    error={Boolean(errors.petrol_consumption)}
                    helperText={errors.petrol_consumption?.message}
                  />
                )}
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <Controller
                name='long'
                control={control}
                render={({ field }) => (
                  <CustomTextField
                    fullWidth
                    label='Longitude'
                    {...field}
                    error={Boolean(errors.long)}
                    helperText={errors.long?.message}
                  />
                )}
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <Controller
                name='lat'
                control={control}
                render={({ field }) => (
                  <CustomTextField
                    fullWidth
                    label='Latitude'
                    {...field}
                    error={Boolean(errors.lat)}
                    helperText={errors.lat?.message}
                  />
                )}
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <Controller
                name='is_active'
                control={control}
                render={({ field }) => (
                  <Box>
                    <FormLabel component='legend'>Status</FormLabel>
                    <FormGroup>
                      <FormControlLabel
                        control={<Checkbox checked={field.value} onChange={e => field.onChange(e.target.checked)} />}
                        label='Active'
                      />
                    </FormGroup>
                    {errors.is_active && <FormHelperText error>{errors.is_active.message}</FormHelperText>}
                  </Box>
                )}
              />
            </Grid>

            <Grid item xs={12}>
              <FormActions
                isLoading={pendingCreate || pendingUpdate}
                mode={mode}
                cancelPath='/vehicles'
                onSave={handleSubmit(handleSaveAndReturn)}
                onSaveAndEdit={mode === 'create' ? handleSubmit(handleSaveAndEdit) : undefined}
                onSaveAndAddNew={mode === 'create' ? handleSubmit(handleSaveAndAddNew) : undefined}
              />
            </Grid>
          </Grid>
        </form>
      </CardContent>
    </Card>
  )
}
