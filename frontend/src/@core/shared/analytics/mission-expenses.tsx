import { Grid, Box, Typography, Card, CardContent, Avatar } from '@mui/material'
import React from 'react'
import CardStatsWithAreaChart from 'src/@core/components/card-statistics/card-stats-with-area-chart'
import KeenSliderWrapper from 'src/@core/styles/libs/keen-slider'
import ApexChartWrapper from 'src/@core/styles/libs/react-apexcharts'
import { useMissionExpensesDashboardStats, useMissionExpensesStats } from 'src/hooks/useMissionExpenses'

import AnalyticsWebsiteAnalyticsSlider, {
  SwiperData
} from 'src/views/dashboards/analytics/AnalyticsWebsiteAnalyticsSlider'

export default function MissionExpensesAnalytics() {
  const { data: basicStats, isLoading, isPending, isError, error } = useMissionExpensesStats()

  const {
    data: expeseStats,
    isLoading: isBasicLoading,
    isPending: isBasicPending,
    isError: isBasicError,
    error: basicError
  } = useMissionExpensesDashboardStats()

  if (isLoading || isPending) {
    return (
      <Box className='flex items-center justify-center w-full h-64'>
        <Typography variant='h6' className='text-gray-500'>
          Loading...
        </Typography>
      </Box>
    )
  }

  if (isBasicError) {
    return (
      <Box className='flex items-center justify-center w-full h-64'>
        <Typography variant='h6' className='text-gray-500'>
          {basicError.message}
        </Typography>
      </Box>
    )
  }

  const swiperData: SwiperData[] = [
    {
      title: 'Mission Expenses',
      img: '/images/cards/graphic-illustration-1.png',
      details: {
        'Unvalidated Expenses': basicStats.unvalidate_me_count.toString()
      }
    }
  ]

  return (
    <ApexChartWrapper>
      <KeenSliderWrapper>
        <Grid container spacing={6}>
          <Grid item xs={12} lg={6}>
            <AnalyticsWebsiteAnalyticsSlider
              data={swiperData}
              title={'Mission Expenses Analytics'}
              description={'Total 28.5% Conversion Rate'}
            />
          </Grid>
          {/* <Grid item xs={12} sm={6} lg={3}>
            <AnalyticsOrderVisits />
          </Grid> */}
          {/* <Grid item xs={12} sm={6} lg={3}>
            <CardStatsWithAreaChart
              stats='97.5k'
              chartColor='success'
              avatarColor='success'
              title='Revenue Generated'
              avatarIcon='tabler:credit-card'
              chartSeries={[{ data: [6, 35, 25, 61, 32, 84, 70] }]}
            />
          </Grid> */}
          {/* <Grid item xs={12} md={6}>
            <AnalyticsEarningReports />
          </Grid> */}
          {/* <Grid item xs={12} md={6}>
            <AnalyticsSupportTracker />
          </Grid> */}
          {/* <Grid item xs={12} md={6} lg={4}>
            <AnalyticsSalesByCountries />
          </Grid> */}
          {/* <Grid item xs={12} md={6} lg={4}>
            <AnalyticsTotalEarning />
          </Grid>
          <Grid item xs={12} md={6} lg={4}>
            <AnalyticsMonthlyCampaignState />
          </Grid> */}
          {/* <Grid item xs={12} md={6} lg={4}>
            <AnalyticsSourceVisits />
          </Grid> */}
          {/* <Grid item xs={12} lg={8}>
            <AnalyticsProject />
          </Grid> */}
        </Grid>
      </KeenSliderWrapper>
    </ApexChartWrapper>
  )
}
