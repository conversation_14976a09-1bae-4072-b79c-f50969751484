import { Grid, Box, Typography, Card, CardContent, Avatar } from '@mui/material'
import React from 'react'
import CardStatsWithAreaChart from 'src/@core/components/card-statistics/card-stats-with-area-chart'
import KeenSliderWrapper from 'src/@core/styles/libs/keen-slider'
import ApexChartWrapper from 'src/@core/styles/libs/react-apexcharts'
import { useOrdersDashboardStats } from 'src/hooks/_services/useOrders'
import { useInvoiceStats } from 'src/hooks/useInvoice'
import AnalyticsEarningReports from 'src/views/dashboards/analytics/AnalyticsEarningReports'
import AnalyticsMonthlyCampaignState from 'src/views/dashboards/analytics/AnalyticsMonthlyCampaignState'
import AnalyticsOrderVisits from 'src/views/dashboards/analytics/AnalyticsOrderVisits'
import AnalyticsSalesByCountries from 'src/views/dashboards/analytics/AnalyticsSalesByCountries'
import AnalyticsSourceVisits from 'src/views/dashboards/analytics/AnalyticsSourceVisits'
import AnalyticsSupportTracker from 'src/views/dashboards/analytics/AnalyticsSupportTracker'
import AnalyticsTotalEarning from 'src/views/dashboards/analytics/AnalyticsTotalEarning'
import AnalyticsWebsiteAnalyticsSlider, {
  SwiperData
} from 'src/views/dashboards/analytics/AnalyticsWebsiteAnalyticsSlider'

export default function InvoiceAnalytics() {
  const { data: invoiceStats, isLoading, isPending, isError, error } = useOrdersDashboardStats()

  const {
    data: basicStats,
    isLoading: isBasicLoading,
    isPending: isBasicPending,
    isError: isBasicError,
    error: basicError
  } = useInvoiceStats()

  if (isLoading || isPending) {
    return (
      <Box className='flex items-center justify-center w-full h-64'>
        <Typography variant='h6' className='text-gray-500'>
          Loading...
        </Typography>
      </Box>
    )
  }

  if (isError) {
    return (
      <Box className='flex items-center justify-center w-full h-64'>
        <Typography variant='h6' className='text-gray-500'>
          {error.message}
        </Typography>
      </Box>
    )
  }

  const swiperData: SwiperData[] = [
    {
      title: 'Invoices Analytics',
      img: '/images/cards/graphic-illustration-1.png',
      details: {
        'This Week': basicStats?.this_week.toString(),
        'Last Week': basicStats?.last_week.toString(),
        'This Month': basicStats?.this_month.toString(),
        'Last Month': basicStats?.last_month.toString()
      }
    }
  ]

  console.log(invoiceStats)

  return (
    <ApexChartWrapper>
      <KeenSliderWrapper>
        <Grid container spacing={6}>
          <Grid item xs={12} lg={6}>
            <AnalyticsWebsiteAnalyticsSlider
              data={swiperData}
              title={'Invoice Analytics'}
              description={'Total 28.5% Conversion Rate'}
            />
          </Grid>
          {/* <Grid item xs={12} sm={6} lg={3}>
            <AnalyticsOrderVisits />
          </Grid> */}
          {/* <Grid item xs={12} sm={6} lg={3}>
            <CardStatsWithAreaChart
              stats='97.5k'
              chartColor='success'
              avatarColor='success'
              title='Revenue Generated'
              avatarIcon='tabler:credit-card'
              chartSeries={[{ data: [6, 35, 25, 61, 32, 84, 70] }]}
            />
          </Grid> */}
          {/* <Grid item xs={12} md={6}>
            <AnalyticsEarningReports />
          </Grid> */}
          {/* <Grid item xs={12} md={6}>
            <AnalyticsSupportTracker />
          </Grid> */}

          {/* <Grid item xs={12} md={6} lg={4}>
            <AnalyticsTotalEarning />
          </Grid> */}
        </Grid>
      </KeenSliderWrapper>
    </ApexChartWrapper>
  )
}
