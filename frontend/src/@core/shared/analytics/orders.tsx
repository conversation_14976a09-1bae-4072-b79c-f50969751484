import { Grid, Box, Typography, Card, CardContent, Avatar } from '@mui/material'
import React from 'react'
import CardStatsWithAreaChart from 'src/@core/components/card-statistics/card-stats-with-area-chart'
import IconifyIcon from 'src/@core/components/icon'
import KeenSliderWrapper from 'src/@core/styles/libs/keen-slider'
import ApexChartWrapper from 'src/@core/styles/libs/react-apexcharts'
import { useOrdersDashboardStats, useOrderStats } from 'src/hooks/_services/useOrders'
import AnalyticsEarningReports from 'src/views/dashboards/analytics/AnalyticsEarningReports'
import AnalyticsMonthlyCampaignState from 'src/views/dashboards/analytics/AnalyticsMonthlyCampaignState'
import AnalyticsOrderVisits from 'src/views/dashboards/analytics/AnalyticsOrderVisits'
import AnalyticsSourceVisits from 'src/views/dashboards/analytics/AnalyticsSourceVisits'
import AnalyticsSupportTracker from 'src/views/dashboards/analytics/AnalyticsSupportTracker'
import AnalyticsTotalEarning from 'src/views/dashboards/analytics/AnalyticsTotalEarning'
import AnalyticsWebsiteAnalyticsSlider, {
  SwiperData
} from 'src/views/dashboards/analytics/AnalyticsWebsiteAnalyticsSlider'

export default function OrdersAnalytics() {
  const {
    data: basicStats,
    isLoading: isBasicLoading,
    isPending: isBasicPending,
    isError: isBasicError,
    error: basicError
  } = useOrderStats()

  const {
    data: orderStats,
    isLoading: isOrderLoading,
    isPending: isOrderPending,
    isError: isOrderError,
    error: orderError
  } = useOrdersDashboardStats()

  if (isBasicLoading || isBasicPending) {
    return (
      <Box className='flex items-center justify-center w-full h-64'>
        <Typography variant='h6' className='text-gray-500'>
          Loading...
        </Typography>
      </Box>
    )
  }

  if (isBasicError) {
    return (
      <Box className='p-4 mt-4 bg-red-50 rounded-lg'>
        <Typography variant='h6' className='text-red-500'>
          Error: {basicError instanceof Error ? basicError.message : 'An error occurred'}
        </Typography>
      </Box>
    )
  }

  if (!basicStats) {
    return (
      <Box className='p-4 mt-4 bg-gray-50 rounded-lg'>
        <Typography variant='h6' className='text-gray-500'>
          No data available
        </Typography>
      </Box>
    )
  }

  const swiperData: SwiperData[] = [
    {
      title: 'Orders',
      img: '/images/cards/graphic-illustration-1.png',
      details: {
        'Validated Orders': basicStats?.validate_orders_count.toString(),
        'Unvalidated Orders': basicStats?.unvalidate_orders_count.toString(),
        'Pending Orders': basicStats?.pending_orders_count.toString()
      }
    },
    {
      title: 'Orders Without DA',
      img: '/images/cards/graphic-illustration-2.png',
      details: {
        'Orders Without DA': basicStats?.without_da_orders_count.toString()
      }
    },
    {
      title: 'Daily Orders',
      img: '/images/cards/graphic-illustration-3.png',
      details: {
        'Daily Orders': basicStats?.daily_orders_count.toString()
      }
    }
  ]

  const lastWeekEarningsPercent =
    (parseInt(orderStats?.weekly_comparison.last_week.earnings || '0') * 100) / parseInt(orderStats?.earnings || '0')
  const currentWeekEarnings =
    (parseInt(orderStats?.weekly_comparison.current_week.earnings || '0') * 100) / parseInt(orderStats?.earnings || '0')
  const earningsPercentChange =
    currentWeekEarnings && lastWeekEarningsPercent
      ? ((currentWeekEarnings - lastWeekEarningsPercent) / lastWeekEarningsPercent) * 100
      : 0

  return (
    <ApexChartWrapper>
      <KeenSliderWrapper>
        <Grid container spacing={6}>
          <Grid item xs={12} lg={6}>
            <AnalyticsWebsiteAnalyticsSlider
              data={swiperData}
              title={' Orders Analytics'}
              description={'Total 28.5% Conversion Rate'}
            />
          </Grid>
          <Grid item xs={12} sm={6} lg={3}>
            <AnalyticsOrderVisits
              title={'Weekly Earning Comparism'}
              total={orderStats?.weekly_comparison.current_week.earnings || '0'}
              firstComparismTitle={'Current'}
              firstComparismTitlepercent={lastWeekEarningsPercent.toString() + '%'}
              secondComparismTitle={'Last'}
              secondComparismTitlepercent={currentWeekEarnings.toString() + '%'}
              firstComparismValue={orderStats?.weekly_comparison.current_week.earnings || '0'}
              secondComparismValue={orderStats?.weekly_comparison.last_week.earnings || '0'}
            />
          </Grid>
          <Grid item xs={12} sm={6} lg={3}>
            <CardStatsWithAreaChart
              stats={orderStats?.profit || '0'}
              chartColor='success'
              avatarColor='success'
              title='Profit Generated'
              avatarIcon='tabler:credit-card'
              chartSeries={[
                {
                  data: [
                    parseInt(orderStats?.daily_comparison.Monday.current_week.earnings || '0'),
                    parseInt(orderStats?.daily_comparison.Tuesday.current_week.earnings || '0'),
                    parseInt(orderStats?.daily_comparison.Wednesday.current_week.earnings || '0'),
                    parseInt(orderStats?.daily_comparison.Thursday.current_week.earnings || '0'),
                    parseInt(orderStats?.daily_comparison.Friday.current_week.earnings || '0'),
                    parseInt(orderStats?.daily_comparison.Saturday.current_week.earnings || '0'),
                    parseInt(orderStats?.daily_comparison.Sunday.current_week.earnings || '0')
                  ]
                }
              ]}
            />
          </Grid>
          <Grid item xs={12} md={6} lg={4}>
            <AnalyticsTotalEarning
              title={'Revenue Comparism'}
              subheader={orderStats?.earnings || '0'}
              subheaderValue={earningsPercentChange.toString()}
              data={[
                {
                  amount: parseInt(orderStats?.weekly_comparison.last_week.earnings || '0'),
                  subtitle: 'Earnings',
                  title: 'Last Week',
                  avatarColor: 'primary',
                  avatarIcon: 'tabler:currency-dollar'
                },
                {
                  amount: parseInt(orderStats?.weekly_comparison.current_week.earnings || '0'),
                  title: 'Current Week',
                  avatarColor: 'secondary',
                  subtitle: 'Earnings',
                  avatarIcon: 'tabler:brand-paypal'
                }
              ]}
              series={[
                {
                  name: 'Last Week',
                  data: [
                    parseInt(orderStats?.weekly_comparison.last_week.earnings || '0'),
                    parseInt(orderStats?.weekly_comparison.last_week.earnings || '0'),
                    parseInt(orderStats?.weekly_comparison.last_week.earnings || '0'),
                    parseInt(orderStats?.weekly_comparison.last_week.earnings || '0'),
                    parseInt(orderStats?.weekly_comparison.last_week.earnings || '0'),
                    parseInt(orderStats?.weekly_comparison.last_week.earnings || '0'),
                    parseInt(orderStats?.weekly_comparison.last_week.earnings || '0')
                  ]
                },
                {
                  name: 'Current Week',
                  data: [
                    parseInt(orderStats?.weekly_comparison.current_week.earnings || '0'),
                    parseInt(orderStats?.weekly_comparison.current_week.earnings || '0'),
                    parseInt(orderStats?.weekly_comparison.current_week.earnings || '0'),
                    parseInt(orderStats?.weekly_comparison.current_week.earnings || '0'),
                    parseInt(orderStats?.weekly_comparison.current_week.earnings || '0'),
                    parseInt(orderStats?.weekly_comparison.current_week.earnings || '0'),
                    parseInt(orderStats?.weekly_comparison.current_week.earnings || '0')
                  ]
                }
              ]}
            />
          </Grid>
          <Grid item xs={12} md={8}>
            <AnalyticsEarningReports
              data={[
                {
                  progress: 64,
                  stats: orderStats?.earnings || '0',
                  title: 'Earnings',
                  avatarIcon: 'tabler:currency-dollar'
                },
                {
                  progress: 59,
                  title: 'Profit',
                  stats: orderStats?.profit || '0',
                  avatarColor: 'info',
                  progressColor: 'info',
                  avatarIcon: 'tabler:chart-pie-2'
                },
                {
                  progress: 22,
                  stats: orderStats?.expense || '0',
                  title: 'Expense',
                  avatarColor: 'error',
                  progressColor: 'error',
                  avatarIcon: 'tabler:brand-paypal'
                }
              ]}
              series={[
                parseInt(orderStats?.daily_comparison.Monday.current_week.earnings || '0'),
                parseInt(orderStats?.daily_comparison.Tuesday.current_week.earnings || '0'),
                parseInt(orderStats?.daily_comparison.Wednesday.current_week.earnings || '0'),
                parseInt(orderStats?.daily_comparison.Thursday.current_week.earnings || '0'),
                parseInt(orderStats?.daily_comparison.Friday.current_week.earnings || '0'),
                parseInt(orderStats?.daily_comparison.Saturday.current_week.earnings || '0'),
                parseInt(orderStats?.daily_comparison.Sunday.current_week.earnings || '0')
              ]}
              total={
                orderStats?.earnings && orderStats.earnings.length > 10
                  ? `${orderStats.earnings.slice(0, 10)}...`
                  : orderStats?.earnings || '0'
              }
              title={'Orders Earnings Reports'}
              subheader={'Weekly Earnings Overview'}
            />
          </Grid>
        </Grid>
      </KeenSliderWrapper>
    </ApexChartWrapper>
  )
}
