import { Grid, Box, Typography, Card, CardContent, Avatar } from '@mui/material'
import React from 'react'
import CardStatsWithAreaChart from 'src/@core/components/card-statistics/card-stats-with-area-chart'
import KeenSliderWrapper from 'src/@core/styles/libs/keen-slider'
import ApexChartWrapper from 'src/@core/styles/libs/react-apexcharts'
import { useTravelsDashboardStats, useTravelsStats } from 'src/hooks/_services/useTravelmanagement'
import AnalyticsEarningReports from 'src/views/dashboards/analytics/AnalyticsEarningReports'
import AnalyticsMonthlyCampaignState from 'src/views/dashboards/analytics/AnalyticsMonthlyCampaignState'
import AnalyticsOrderVisits from 'src/views/dashboards/analytics/AnalyticsOrderVisits'
import AnalyticsSalesByCountries from 'src/views/dashboards/analytics/AnalyticsSalesByCountries'
import AnalyticsSourceVisits from 'src/views/dashboards/analytics/AnalyticsSourceVisits'
import AnalyticsSupportTracker, { DataType } from 'src/views/dashboards/analytics/AnalyticsSupportTracker'
import AnalyticsTotalEarning from 'src/views/dashboards/analytics/AnalyticsTotalEarning'
import AnalyticsWebsiteAnalyticsSlider, {
  SwiperData
} from 'src/views/dashboards/analytics/AnalyticsWebsiteAnalyticsSlider'

export default function TravelAnalytics() {
  const { data: travelStats, isLoading, isPending, isError, error } = useTravelsDashboardStats()
  const {
    data: basicStats,
    isLoading: isBasicLoading,
    isPending: isBasicPending,
    isError: isBasicError,
    error: basicError
  } = useTravelsStats()
  if (isLoading || isPending) {
    return (
      <Box className='flex items-center justify-center w-full h-64'>
        <Typography variant='h6' className='text-gray-500'>
          Loading...
        </Typography>
      </Box>
    )
  }

  if (isError) {
    return (
      <Box className='flex items-center justify-center w-full h-64'>
        <Typography variant='h6' className='text-gray-500'>
          {error.message}
        </Typography>
      </Box>
    )
  }

  const swiperData: SwiperData[] = [
    {
      title: 'Travels',
      img: '/images/cards/graphic-illustration-1.png',
      details: {
        'Ongoing Travels': basicStats?.ongoing_travels_count.toString(),
        'Completed Travels': basicStats?.completed_travels_count.toString(),
        'Canceled Travels': basicStats?.canceled_travels_count.toString(),
        'Ongoing Immobilizations': basicStats?.ongoing_immobilizations_count.toString()
      }
    }
  ]

  const supportTrackerData: DataType[] = [
    {
      subtitle: basicStats?.ongoing_travels_count.toString(),
      title: 'Ongoing Travels',
      avatarColor: 'primary',
      avatarIcon: 'tabler:ticket'
    },
    {
      subtitle: basicStats?.completed_travels_count.toString(),
      title: 'Completed',
      avatarColor: 'success',
      avatarIcon: 'tabler:ticket'
    },
    {
      subtitle: basicStats?.canceled_travels_count.toString(),
      avatarColor: 'error',
      title: 'Canceled',
      avatarIcon: 'tabler:circle-check'
    },
    {
      subtitle: basicStats?.ongoing_immobilizations_count.toString(),
      title: 'Ongoing Immobilizations',
      avatarColor: 'warning',
      avatarIcon: 'tabler:clock'
    }
  ]

  const completedPecent = ((basicStats?.completed_travels_count / travelStats?.total_travels) * 100).toFixed(2)
  const canceledPecent = ((basicStats?.canceled_travels_count / travelStats?.total_travels) * 100).toFixed(2)

  const totalPecentAdd = ((basicStats?.completed_travels_count / travelStats?.total_travels) * 100).toFixed(2)

  const weeklyTotal = travelStats.weekly_comparison.current_week.total + travelStats?.weekly_comparison.last_week.total
  const weeklyCurrentWeekPecent = ((travelStats?.weekly_comparison.current_week.total / weeklyTotal) * 100).toFixed(2)
  const weeklyLastWeekPecent = ((travelStats?.weekly_comparison.last_week.total / weeklyTotal) * 100).toFixed(2)
  const weeklyTotalPecentAdd = ((travelStats?.weekly_comparison.current_week.total / weeklyTotal) * 100).toFixed(2)
  const weeklyTotalPecentAddValue = travelStats?.weekly_comparison.current_week.total
  const weeklyFirstComparismValue = travelStats?.weekly_comparison.current_week.total

  return (
    <ApexChartWrapper>
      <KeenSliderWrapper>
        <Grid container spacing={6}>
          <Grid item xs={12} lg={6}>
            <AnalyticsWebsiteAnalyticsSlider
              data={swiperData}
              title={'Travel Analytics'}
              description={'Total 28.5% Conversion Rate'}
            />
          </Grid>
          <Grid item xs={12} sm={6} lg={3}>
            <AnalyticsOrderVisits
              title={'Travels Comparism'}
              total={travelStats?.total_travels.toString()}
              firstComparismTitle={'Completed'}
              firstComparismTitlepercent={completedPecent + '%'}
              secondComparismTitlepercent={canceledPecent + '%'}
              totalPecentAddValue={travelStats}
              firstComparismValue={travelStats?.completed.toString()}
              secondComparismTitle={'Canceled'}
              totalPecentAdd={totalPecentAdd + '%'}
              secondComparismValue={travelStats?.canceled.toString()}
            />
          </Grid>

          <Grid item xs={12} sm={6} lg={3}>
            <AnalyticsOrderVisits
              title={'Weekly Comparism'}
              total={weeklyTotal.toString()}
              firstComparismTitle={'Current'}
              firstComparismTitlepercent={weeklyCurrentWeekPecent + '%'}
              secondComparismTitlepercent={weeklyLastWeekPecent + '%'}
              totalPecentAddValue={weeklyTotalPecentAddValue.toString()}
              firstComparismValue={travelStats.weekly_comparison.current_week.completed.toString()}
              secondComparismTitle={'Last'}
              totalPecentAdd={weeklyTotalPecentAdd + '%'}
              secondComparismValue={travelStats.weekly_comparison.last_week.completed.toString()}
            />
          </Grid>
          {/* <Grid item xs={12} sm={6} lg={3}>
            <CardStatsWithAreaChart
              stats='97.5k'
              chartColor='success'
              avatarColor='success'
              title='Revenue Generated'
              avatarIcon='tabler:credit-card'
              chartSeries={[{ data: [6, 35, 25, 61, 32, 84, 70] }]}
            />
          </Grid> */}
          {/* <Grid item xs={12} md={6}>
            <AnalyticsEarningReports />
          </Grid> */}
          <Grid item xs={12} md={5}>
            <AnalyticsSupportTracker
              data={supportTrackerData}
              total={travelStats.total_travels}
              series={[parseInt(completedPecent)]}
              title={'Travels'}
            />
          </Grid>
        </Grid>
      </KeenSliderWrapper>
    </ApexChartWrapper>
  )
}
