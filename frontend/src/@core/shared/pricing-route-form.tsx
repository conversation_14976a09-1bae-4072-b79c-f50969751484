import { Fragment, useEffect } from 'react'
import { useRouter } from 'next/router'
import { Card, CardContent, CardHeader, Box, CircularProgress, <PERSON><PERSON>, <PERSON><PERSON>, Divider } from '@mui/material'
import { useForm, Controller } from 'react-hook-form'
import { yupResolver } from '@hookform/resolvers/yup'
import * as yup from 'yup'
import CustomTextField from 'src/@core/components/mui/text-field'
import CustomAutocomplete from 'src/@core/components/mui/autocomplete'
import { useArticles } from 'src/hooks/_services/useArticles'
import { useRoutes } from '../../hooks/helpers/useRoutes'
import Icon from 'src/@core/components/icon'
import toast from 'react-hot-toast'
import { useQueryClient } from '@tanstack/react-query'
import { useCreatePricingRoute, useUpdatePricingRoute, usePricingRouteById } from 'src/hooks/_services/usePricingRoute'
import { PricingRoute } from 'src/types/models/_services/pricing-route'
import FormButtons from '../components/form-buttons'

interface PricingRouteFormData {
  route?: string
  good_type?: number
  unite_price?: string
}

interface PricingRouteFormProps {
  mode: 'create' | 'edit'
  initialData?: PricingRoute
  pricingId?: string
  onSuccess?: () => void
}

const schema = yup.object().shape({
  route: yup.string().required('Le trajet est requis'),
  good_type: yup.string().required('La nature du produit est requise'),
  unite_price: yup
    .number()
    .typeError('Le prix doit être un nombre')
    .required('Le prix unitaire est requis')
    .min(0, 'Le prix ne peut pas être négatif')
})

export const PricingRouteForm = ({ mode, initialData, pricingId, onSuccess }: PricingRouteFormProps) => {
  const router = useRouter()
  const queryClient = useQueryClient()

  const {
    data: productsData,
    isLoading: isLoadingProducts,
    isError: isProductsError
  } = useArticles({
    limit: 100,
    offset: 0
  })

  const {
    data: routesData,
    isLoading: isLoadingRoutes,
    isError: isRoutesError
  } = useRoutes({
    limit: 100,
    offset: 0
  })

  const { mutateAsync: createPricingRoute, isPending: isCreating } = useCreatePricingRoute({
    onSuccess: () => {
      toast.success('Tarif créé avec succès')
      queryClient.invalidateQueries({ queryKey: ['pricing-route'] })
      onSuccess?.()
    },
    onError: () => {
      toast.error('Une erreur est survenue')
    }
  })

  const { mutateAsync: updatePricingRoute, isPending: isUpdating } = useUpdatePricingRoute(pricingId || '', {
    onSuccess: () => {
      toast.success('Tarif modifié avec succès')
      queryClient.invalidateQueries({ queryKey: ['pricing-route'] })
      onSuccess?.()
    },
    onError: () => {
      toast.error('Une erreur est survenue')
    }
  })

  const {
    control,
    handleSubmit,
    reset,
    formState: { errors }
  } = useForm<PricingRouteFormData>({
    resolver: yupResolver(schema),
    defaultValues: {
      route: initialData?.route?.id,
      good_type: initialData?.good_type?.id,
      unite_price: initialData?.unite_price
    }
  })

  useEffect(() => {
    if (mode === 'edit' && initialData) {
      reset({
        route: initialData.route.id,
        good_type: initialData.good_type.id,
        unite_price: initialData.unite_price
      })
    }
  }, [reset, initialData, mode])

  const handleSave = async (formData: PricingRouteFormData) => {
    try {
      if (mode === 'create') {
        return await createPricingRoute(formData)
      } else {
        return await updatePricingRoute(formData)
      }
    } catch (error) {
      // Error is handled by the mutation callbacks
      throw error
    }
  }

  const handleSaveAndReturn = async (formData: PricingRouteFormData) => {
    try {
      await handleSave(formData)
      router.push('/services/pricing')
    } catch (error) {
      // Error handled in handleSave
    }
  }

  const handleSaveAndEdit = async (formData: PricingRouteFormData) => {
    const pricing = await handleSave(formData)
    if (mode === 'create' && pricing?.id) {
      router.push(`/services/pricing/${pricing.id}/update`)
    }
  }

  const handleSaveAndAddNew = async (formData: PricingRouteFormData) => {
    await handleSave(formData)
    if (mode === 'create') {
      reset()
    }
  }

  return (
    <Card>
      <CardHeader
        title={
          mode === 'create'
            ? 'Créer un tarif standard'
            : `${initialData?.route.name}/ ${initialData?.good_type.label} / ${initialData?.unite_price}`
        }
        sx={{
          px: 5,
          py: 5,
          '& .MuiCardHeader-title': {
            fontWeight: 600
          }
        }}
      />
      <CardContent>
        <form onSubmit={e => e.preventDefault()}>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 5 }}>
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 4 }}>
              <Controller
                name='route'
                control={control}
                render={({ field: { value, onChange, ...field } }) => (
                  <CustomAutocomplete
                    {...field}
                    options={routesData?.results || []}
                    loading={isLoadingRoutes}
                    id='route-autocomplete'
                    value={routesData?.results?.find((route: any) => route.id === value) || null}
                    onChange={(_, newValue) => onChange(newValue ? newValue.id : '')}
                    getOptionLabel={option => option.name || ''}
                    isOptionEqualToValue={(option, value) => option.id === value.id}
                    renderInput={params => (
                      <CustomTextField
                        {...params}
                        label='Trajet'
                        error={Boolean(errors.route)}
                        helperText={errors.route?.message}
                        InputProps={{
                          ...params.InputProps,
                          endAdornment: (
                            <Fragment>
                              {isLoadingRoutes ? <CircularProgress size={20} /> : null}
                              {params.InputProps.endAdornment}
                            </Fragment>
                          )
                        }}
                      />
                    )}
                  />
                )}
              />

              <Controller
                name='good_type'
                control={control}
                render={({ field: { value, onChange, ...field } }) => (
                  <CustomAutocomplete
                    {...field}
                    options={productsData?.results || []}
                    loading={isLoadingProducts}
                    id='product-autocomplete'
                    value={productsData?.results?.find((product: any) => product.id === value) || null}
                    onChange={(_, newValue) => onChange(newValue ? newValue.id : '')}
                    getOptionLabel={option => option.label || ''}
                    isOptionEqualToValue={(option, value) => option.id === value.id}
                    renderInput={params => (
                      <CustomTextField
                        {...params}
                        label='Type de marchandise'
                        error={Boolean(errors.good_type)}
                        helperText={errors.good_type?.message}
                        InputProps={{
                          ...params.InputProps,
                          endAdornment: (
                            <Fragment>
                              {isLoadingProducts ? <CircularProgress size={20} /> : null}
                              {params.InputProps.endAdornment}
                            </Fragment>
                          )
                        }}
                      />
                    )}
                  />
                )}
              />

              <Controller
                name='unite_price'
                control={control}
                render={({ field }) => (
                  <CustomTextField
                    fullWidth
                    type='number'
                    label='Prix unitaire'
                    {...field}
                    error={Boolean(errors.unite_price)}
                    helperText={errors.unite_price?.message}
                  />
                )}
              />
            </Box>

            <FormButtons
              isLoading={isCreating || isUpdating}
              mode={mode}
              cancelPath='/services/pricing'
              onSave={handleSubmit(handleSaveAndReturn)}
              onSaveAndEdit={handleSubmit(handleSaveAndEdit)}
              onSaveAndAddNew={handleSubmit(handleSaveAndAddNew)}
            />
          </Box>
        </form>
      </CardContent>
    </Card>
  )
}
