import React, { useEffect, useState } from 'react'
import { useF<PERSON>, Controller, useFieldArray } from 'react-hook-form'
import { yupResolver } from '@hookform/resolvers/yup'
import {
  Box,
  Button,
  Typography,
  FormControl,
  Grid,
  Card,
  CardHeader,
  IconButton,
  Divider,
  Select,
  MenuItem,
  FormLabel,
  FormHelperText,
  Alert,
  InputLabel,
  CardContent
} from '@mui/material'
import Icon from 'src/@core/components/icon'
import CustomTextField from 'src/@core/components/mui/text-field'
import InfiniteSelect from '../components/infinite-select/InfiniteSelect'
import { getInvoiceItemById, infiniteInvoiceItem } from 'src/services/_service/invoice-items'
import { getStationById, infiniteFuelStattion } from 'src/services/_service/stations'
import { useCreateFuelConsumption } from 'src/hooks/useFeulConsumption'
import { useUpdateFuelConsumption } from 'src/hooks/useFeulConsumption'
import { useQueryClient } from '@tanstack/react-query'
import { useRouter } from 'next/router'
import { FuelConsumption } from 'src/types/models/fuel-consumption'
import { LoadingButton } from '@mui/lab'
import { ConsumptionFormData } from 'src/types/models/fuel-consumption-partition'
import { useTranslation } from 'react-i18next'
import FormButtons from '../components/form-buttons'
import { consumptionSchema } from 'src/lib/schemas'
import AsyncInfiniteSelect from './components/infinite-select'
import toast from 'react-hot-toast'
import { useDeleteFuelConsumptionPartition } from 'src/hooks/useFuelConsumptionPartition'
import ConfirmationModal from 'src/@core/components/modals/ConfirmationModal'

interface ConsumptionFormProps {
  mode: 'create' | 'edit'
  initialData?: FuelConsumption | null
  consumptionId?: string
  onSuccess?: () => void
}

const consoleType = [
  { value: 'Normale', title: 'Normale' },
  { value: 'Extra', title: 'Extra' },
  { value: 'Motopompe', title: 'Motopompe' }
]

const validation = [
  { value: 'EN ATTENTE', title: 'En attente' },
  { value: 'VALIDEE', title: 'Validée' },
  { value: 'REJETEE', title: 'Rejetée' }
]

const paymentType = [
  { value: 'Carte', title: 'Carte' },
  { value: 'Sans Carte', title: 'Sans Carte' }
]

const ConsumptionForm = ({ mode = 'create', initialData, consumptionId, onSuccess }: ConsumptionFormProps) => {
  const [submitError, setSubmitError] = useState<string | null>(null)
  const [currentOperation, setCurrentOperation] = useState<any>(null)
  const router = useRouter()
  const queryClient = useQueryClient()
  const { t } = useTranslation()
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [distributionToDelete, setDistributionToDelete] = useState<string | null>(null)

  const {
    control,
    handleSubmit,
    watch,
    reset,
    formState: { errors }
  } = useForm<ConsumptionFormData>({
    resolver: yupResolver(consumptionSchema),
    defaultValues: {
      type_conso: 'Normale',
      validation: 'EN ATTENTE',
      code: '',
      distributions: [
        {
          station: null,
          volume: 0,
          cash_card: '',
          card_number: ''
        }
      ]
    }
  })
  const { fields, append, remove } = useFieldArray({
    control,
    name: 'distributions'
  })

  const createConsumption = useCreateFuelConsumption({
    onSuccess() {
      toast.success(t('forms.consumptionOperationsForm.messages.create_success'))
      queryClient.invalidateQueries({ queryKey: ['fuel-consumption'] })
      onSuccess?.()
    },
    onError(error) {
      toast.error(t('forms.consumptionOperationsForm.messages.create_error', { error }))
    }
  })

  const updateConsumption = useUpdateFuelConsumption({
    onSuccess() {
      toast.success(t('forms.consumptionOperationsForm.messages.update_success'))
      queryClient.invalidateQueries({ queryKey: ['fuel-consumption', consumptionId] })
      onSuccess?.()
    },
    onError() {
      toast.error(t('forms.consumptionOperationsForm.messages.update_error'))
    }
  })

  const deleteDistribution = useDeleteFuelConsumptionPartition({
    onSuccess() {
      toast.success(t('forms.consumptionOperationsForm.messages.delete_success'))
      queryClient.invalidateQueries({ queryKey: ['fuel-consumption-partitions', consumptionId] })
    },
    onError() {
      toast.error(t('forms.consumptionOperationsForm.messages.delete_error'))
    }
  })

  useEffect(() => {
    if (mode === 'edit' && initialData) {
      reset({
        operation: initialData.operation.id,
        type_conso: initialData.type_conso,
        validation: initialData.validation,
        code: initialData.code,
        distributions: initialData.repartitions.map((dist: any) => ({
          id: dist.id,
          _id: dist.id,
          station: dist.station,
          volume: dist.volume,
          cash_card: dist.cash_card,
          card_number: dist.card_number
        }))
      })
    }
  }, [mode, initialData, reset])

  const distributions = watch('distributions')
  const volumeTotal = currentOperation?.petrol_volume

  const calculateTotalDistributionVolume = () => {
    const result = distributions?.reduce((sum, dist) => {
      const volume = parseFloat(dist.volume?.toString() || '0')

      return isNaN(volume) ? sum : sum + volume
    }, 0)

    return result
  }

  const validateVolumes = () => {
    const totalDistributionVolume = calculateTotalDistributionVolume()

    if (totalDistributionVolume > parseFloat(volumeTotal)) {
      return {
        isValid: false,
        error: `Le volume total des distributions (${totalDistributionVolume}) ne peut pas dépasser le volume de l'opération (${volumeTotal})`
      }
    }

    return { isValid: true, error: null }
  }

  const currentTotalVolume = calculateTotalDistributionVolume()
  const isVolumeTotalExceeded = currentTotalVolume > parseFloat(volumeTotal)
  const remainingVolume = parseFloat(volumeTotal) - currentTotalVolume
  const handleAdd = () =>
    append({
      station: null,
      volume: 0,
      cash_card: '',
      card_number: ''
    })

  const handleRemove = (index: number, id?: string) => {
    if (mode === 'edit' && id) {
      setDistributionToDelete(id)
      setDeleteDialogOpen(true)
    }
    if (mode === 'create') {
      remove(index)
    }
  }

  const handleDeleteConfirm = async () => {
    if (mode === 'edit' && distributionToDelete) await deleteDistribution.mutateAsync({ id: distributionToDelete })
  }

  const handleDeleteCancel = () => {
    setDeleteDialogOpen(false)
  }

  const handleSaveAndReturn = async (data: ConsumptionFormData) => {
    const volumeValidation = validateVolumes()
    if (!volumeValidation.isValid) {
      setSubmitError(volumeValidation.error)

      return
    }
    if (mode === 'create') await createConsumption.mutateAsync({ data })
    if (mode === 'edit') await updateConsumption.mutateAsync({ data, id: consumptionId! })
    router.push('/fuel/consumption')
  }

  const handleSaveAndEdit = async (data: ConsumptionFormData) => {
    const volumeValidation = validateVolumes()
    if (!volumeValidation.isValid) {
      setSubmitError(volumeValidation.error)

      return
    }

    if (mode === 'create') {
      createConsumption.mutateAsync({ data })
      router.push(`/fuel/consumption/${createConsumption.data.id}/edit`)
    } else {
      await updateConsumption.mutateAsync({ data, id: consumptionId! })
      router.push(`/fuel/consumption/${consumptionId}/edit`)
    }
  }

  const handleSaveAndAddNew = async (data: ConsumptionFormData) => {
    const volumeValidation = validateVolumes()
    if (!volumeValidation.isValid) {
      setSubmitError(volumeValidation.error)

      return
    }

    if (mode === 'create') {
      await createConsumption.mutateAsync({ data })
      reset({
        operation: null,
        type_conso: 'Normale',
        validation: 'EN ATTENTE',
        distributions: [
          {
            station: null,
            volume: 0,
            cash_card: '',
            card_number: ''
          }
        ]
      })
    }

    if (mode === 'edit') {
      await updateConsumption.mutateAsync({ data, id: consumptionId! })
    }

    setSubmitError(null)
  }

  return (
    <Card sx={{ mt: 4 }}>
      <CardHeader title={t('forms.consumptionOperationsForm.title')} />
      <Box component='form' onSubmit={e => e.preventDefault()} sx={{ px: 6, pb: 6 }}>
        {submitError && (
          <Alert severity='error' sx={{ mb: 4 }}>
            {submitError}
          </Alert>
        )}

        <Grid container spacing={4}>
          <Grid item xs={12} md={6}>
            <FormControl fullWidth required sx={{ mb: 4 }}>
              <FormLabel>{t('forms.consumptionOperationsForm.fields.operation')}</FormLabel>
              <Controller
                name='operation'
                control={control}
                render={({ field }) => {
                  return (
                    <AsyncInfiniteSelect
                      value={field.value}
                      fetchFn={async ({ pageParam, searchQuery }) => {
                        const res = await infiniteInvoiceItem({ pageParam, searchQuery })
                        setCurrentOperation(res.items[0])

                        return res
                      }}
                      onChange={field.onChange}
                      getOptionLabel={option => `${option.order_code}- BE:${option.be_number}`}
                      fetchByIdFn={async (id: string) => {
                        const res = await getInvoiceItemById(id)
                        setCurrentOperation(res)
                        console.log(res)

                        return res
                      }}
                      queryKey='infinite-invoice-items'
                      placeholder='Sélectionner une opération'
                      size='small'
                      renderOption={(props, option) => (
                        <MenuItem {...props} key={option.id}>
                          {option.order_code}- BE:{option.be_number}
                        </MenuItem>
                      )}
                    />
                  )
                }}
              />
              {errors.operation && <FormHelperText error>{errors.operation.message as string}</FormHelperText>}
            </FormControl>

            <FormControl fullWidth required sx={{ mb: 4 }}>
              <FormLabel>{t('forms.consumptionOperationsForm.fields.type_conso')}</FormLabel>
              <Controller
                name='type_conso'
                control={control}
                render={({ field }) => (
                  <Select {...field} size='small' error={Boolean(errors.type_conso)}>
                    {consoleType.map(option => (
                      <MenuItem key={option.value} value={option.value}>
                        {option.title}
                      </MenuItem>
                    ))}
                  </Select>
                )}
              />
              {errors.type_conso && <FormHelperText error>{errors.type_conso.message}</FormHelperText>}
            </FormControl>

            <FormControl fullWidth required sx={{ mb: 4 }}>
              <FormLabel>{t('forms.consumptionOperationsForm.fields.validation')}</FormLabel>
              <Controller
                name='validation'
                control={control}
                render={({ field }) => (
                  <Select {...field} size='small' error={Boolean(errors.validation)}>
                    {validation.map(option => (
                      <MenuItem key={option.value} value={option.value}>
                        {option.title}
                      </MenuItem>
                    ))}
                  </Select>
                )}
              />
              {errors.validation && <FormHelperText error>{errors.validation.message}</FormHelperText>}
            </FormControl>

            <Box sx={{ mb: 4, display: 'flex', flexDirection: 'column' }}>
              <FormLabel>{t('forms.consumptionOperationsForm.fields.volumeTotal')}</FormLabel>
              <CustomTextField
                value={volumeTotal?.toFixed(2) || '0'}
                InputProps={{
                  readOnly: true
                }}
                error={isVolumeTotalExceeded}
                helperText={
                  currentOperation
                    ? isVolumeTotalExceeded
                      ? `Dépassement de ${(currentTotalVolume - parseFloat(volumeTotal || '0')).toFixed(2)}`
                      : `Reste à distribuer: ${remainingVolume.toFixed(2)}`
                    : 'Reste à distribuer: 0.0'
                }
                sx={{ mb: 2 }}
              />
            </Box>
          </Grid>
        </Grid>

        <Divider sx={{ my: 4 }} />

        <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 4 }}>
          <Typography variant='h6'>Répartition de consommation</Typography>
        </Box>

        <Box sx={{ px: 2 }}>
          {fields.map((item, index) => {
            const cashCardValue = watch(`distributions.${index}.cash_card`)
            const isCardRequired = cashCardValue === 'Carte'

            return (
              <Card key={item.id} sx={{ mb: 4 }}>
                <CardContent>
                  <Grid item xs={12}>
                    <Grid container xs={12} spacing={3}>
                      <Grid item xs={4}>
                        <Controller
                          name={`distributions.${index}.station`}
                          control={control}
                          render={({ field }) => (
                            <AsyncInfiniteSelect
                              label={t('forms.consumptionOperationsForm.fields.station') as string}
                              value={field.value}
                              onChange={field.onChange}
                              getOptionLabel={f => f.station_name}
                              fetchByIdFn={getStationById}
                              isRequired
                              queryKey='infinite-fuel-stations'
                              placeholder={t('forms.consumptionOperationsForm.fields.station') as string}
                              size='small'
                              fetchFn={infiniteFuelStattion}
                            />
                          )}
                        />
                      </Grid>

                      <Grid item xs={12} sm={6} md={3}>
                        <Controller
                          name={`distributions.${index}.volume`}
                          control={control}
                          render={({ field }) => (
                            <CustomTextField
                              required
                              label={t('forms.consumptionOperationsForm.fields.volume')}
                              {...field}
                              fullWidth
                              size='small'
                              type='number'
                              placeholder={t('forms.consumptionOperationsForm.fields.volume') as string}
                              error={Boolean(errors.distributions?.[index]?.volume)}
                              helperText={errors.distributions?.[index]?.volume?.message}
                            />
                          )}
                        />
                      </Grid>
                    </Grid>
                  </Grid>

                  <Grid xs={12} sm={6} sx={{ mt: 4 }}>
                    <Grid container xs={12} spacing={3}>
                      <Grid item xs={12} sm={4} md={3}>
                        <Controller
                          name={`distributions.${index}.cash_card`}
                          control={control}
                          render={({ field }) => (
                            <CustomTextField
                              select
                              fullWidth
                              label={t('forms.consumptionOperationsForm.fields.cash_card')}
                              {...field}
                              size='small'
                              error={Boolean(errors.distributions?.[index]?.cash_card)}
                              helperText={errors.distributions?.[index]?.cash_card?.message}
                            >
                              {paymentType.map(option => (
                                <MenuItem key={option.value} value={option.value}>
                                  {option.title}
                                </MenuItem>
                              ))}
                            </CustomTextField>
                          )}
                        />
                      </Grid>

                      <Grid item xs={12} sm={4}>
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          <Controller
                            name={`distributions.${index}.card_number`}
                            control={control}
                            render={({ field }) => (
                              <CustomTextField
                                label={t('forms.consumptionOperationsForm.fields.card_number')}
                                {...field}
                                fullWidth
                                size='small'
                                placeholder='N° Carte'
                                disabled={!isCardRequired}
                                error={isCardRequired && Boolean(errors.distributions?.[index]?.card_number)}
                                helperText={isCardRequired ? errors.distributions?.[index]?.card_number?.message : ''}
                              />
                            )}
                          />
                          <IconButton
                            onClick={() => handleRemove(index, item._id)}
                            color='error'
                            disabled={fields.length === 1}
                            sx={{ ml: 1 }}
                          >
                            <Icon icon='tabler:trash' />
                          </IconButton>
                        </Box>
                      </Grid>
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>
            )
          })}
        </Box>

        <Box sx={{ mt: 4, display: 'flex', justifyContent: 'start', px: 2 }}>
          <Button variant='contained' color='primary' onClick={handleAdd}>
            <Icon icon='mdi:plus' /> {t('forms.consumptionOperationsForm.addDistribution')}
          </Button>
        </Box>

        {fields.length > 0 && (
          <Box sx={{ display: 'flex', justifyContent: 'flex-end' }}>
            <FormButtons
              isLoading={createConsumption.isPending || updateConsumption.isPending}
              mode={mode}
              cancelPath='/fuel/consumption'
              onSaveAndEdit={handleSubmit(handleSaveAndEdit)}
              onSaveAndAddNew={handleSubmit(handleSaveAndAddNew)}
              onSave={handleSubmit(handleSaveAndReturn)}
              saveLabel='Enregistrer'
            />
          </Box>
        )}
      </Box>

      {mode === 'edit' && (
        <ConfirmationModal
          open={deleteDialogOpen}
          title={t('forms.consumptionOperationsForm.deleteDistributionDialog.title')}
          message={t('forms.consumptionOperationsForm.deleteDistributionDialog.message')}
          confirmButtonText={t('forms.consumptionOperationsForm.deleteDistributionDialog.confirm') as string}
          cancelButtonText={t('forms.consumptionOperationsForm.deleteDistributionDialog.cancel') as string}
          onConfirm={handleDeleteConfirm}
          onCancel={handleDeleteCancel}
        />
      )}
    </Card>
  )
}

export default ConsumptionForm
