import { useQueryClient } from '@tanstack/react-query'
import { useRouter } from 'next/router'
import { useTranslation } from 'react-i18next'
import { useEffect, useState } from 'react'
import { yupResolver } from '@hookform/resolvers/yup'
import { useForm } from 'react-hook-form'
import toast from 'react-hot-toast'
import { documentTypeSchema } from 'src/lib/schemas'

import { Box, Button, Card, CardContent, CardHeader, FormControlLabel, Grid, Switch, TextField } from '@mui/material'
import { Controller } from 'react-hook-form'
import FormButtons from '../components/form-buttons'
import { useCreateDocumentType, useUpdateDocumentType } from 'src/hooks/helpers/useDocumentType'
import { DocumentTypeRequestPayload } from 'src/types/models/settings/document-type'
import CustomTextField from '../components/mui/text-field'

interface DocumentTypeFormProps {
  mode: 'create' | 'edit'
  documentTypeId?: string
  initialData?: any
  onSuccess?: () => void
}

export const DocumentTypeForm = ({ mode, documentTypeId, initialData, onSuccess }: DocumentTypeFormProps) => {
  const { t } = useTranslation()
  const router = useRouter()
  const queryClient = useQueryClient()

  const {
    control,
    handleSubmit,
    reset,
    formState: { errors }
  } = useForm<DocumentTypeRequestPayload>({
    resolver: yupResolver(documentTypeSchema),
    defaultValues: initialData || {
      name: '',
      is_active: true
    }
  })

  const {
    mutateAsync: createDocumentType,
    isPending: isCreating,
    data: createDocumentTypeRes
  } = useCreateDocumentType({
    onSuccess: () => {
      toast.success(t('forms.documentTypeForm.messages.create_success'))
      queryClient.invalidateQueries({ queryKey: ['documentTypes'] })
      onSuccess?.()
    },
    onError: () => {
      toast.error(t('forms.documentTypeForm.messages.create_error'))
    }
  })

  const {
    mutateAsync: updateDocumentType,
    isPending: isUpdating,
    data: updateDocumentTypeRes
  } = useUpdateDocumentType(documentTypeId || '', {
    onSuccess: () => {
      toast.success(t('forms.documentTypeForm.messages.update_success'))
      queryClient.invalidateQueries({ queryKey: ['documentTypes'] })
      onSuccess?.()
    },
    onError: () => {
      toast.error(t('forms.documentTypeForm.messages.update_error'))
    }
  })

  const handleSave = async (data: DocumentTypeRequestPayload) => {
    if (mode === 'create') {
      await createDocumentType(data)
    } else {
      await updateDocumentType(data)
    }
  }

  const handleSaveAndReturn = async (data: DocumentTypeRequestPayload) => {
    await handleSave(data)
    router.push('/settings/document-type')
  }

  const handleSaveAndEdit = async (data: DocumentTypeRequestPayload) => {
    await handleSave(data)
    reset(data)
  }

  const handleSaveAndAddNew = async (data: DocumentTypeRequestPayload) => {
    await handleSave(data)
    reset()
  }

  return (
    <Card>
      <CardHeader title={t(`forms.documentTypeForm.${mode === 'create' ? 'create_title' : 'edit_title'}`)} />
      <CardContent>
        <form onSubmit={handleSubmit(handleSaveAndReturn)}>
          <Grid container spacing={5}>
            <Grid item xs={12} sm={4}>
              <Controller
                name='name'
                control={control}
                render={({ field }) => (
                  <CustomTextField
                    {...field}
                    fullWidth
                    label={t('forms.documentTypeForm.fields.name')}
                    error={Boolean(errors.name)}
                    helperText={errors.name?.message}
                  />
                )}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <Controller
                name='amount'
                control={control}
                render={({ field }) => (
                  <CustomTextField
                    fullWidth
                    type='number'
                    label={t('forms.documentTypeForm.fields.amount')}
                    error={Boolean(errors.amount)}
                    helperText={
                      errors.amount?.message ? errors.amount?.message : t('forms.documentTypeForm.description.distance')
                    }
                    {...field}
                  />
                )}
              />
            </Grid>
            <Grid item xs={12}>
              <FormControlLabel
                control={
                  <Controller
                    name='is_default'
                    control={control}
                    render={({ field }) => <Switch {...field} checked={field.value} />}
                  />
                }
                label={t('forms.documentTypeForm.fields.is_active')}
              />
            </Grid>
            <Grid item xs={12}>
              <FormControlLabel
                control={
                  <Controller
                    name='is_chargeable'
                    control={control}
                    render={({ field }) => <Switch {...field} checked={field.value} />}
                  />
                }
                label={t('forms.documentTypeForm.fields.is_city')}
              />
            </Grid>
          </Grid>
          <Box sx={{ mt: 5 }}>
            <FormButtons
              isLoading={isCreating || isUpdating}
              mode={mode}
              cancelPath='/settings/document-type'
              onSave={handleSubmit(handleSaveAndReturn)}
              onSaveAndEdit={handleSubmit(handleSaveAndEdit)}
              onSaveAndAddNew={handleSubmit(handleSaveAndAddNew)}
            />
          </Box>
        </form>
      </CardContent>
    </Card>
  )
}
