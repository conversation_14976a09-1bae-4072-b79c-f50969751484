import { useTranslation } from 'react-i18next'
import { useRouter } from 'next/router'
import { useQueryClient } from '@tanstack/react-query'
import { useCreateAgency, useUpdateAgency } from 'src/hooks/useAgency'
import { Box, Button, Card, CardContent, CardHeader, FormControlLabel, Grid, Switch, TextField } from '@mui/material'
import { Icon } from '@iconify/react'
import toast from 'react-hot-toast'
import { yupResolver } from '@hookform/resolvers/yup'
import { Controller, useForm } from 'react-hook-form'
import { agencySchema } from 'src/lib/schemas'
import { AgencyRequestPayload } from 'src/types/models/agencies'
import AsyncInfiniteSelect from './components/infinite-select'
import { fetchInfiniteCustomers, getCustomerById } from 'src/services/customer'
import CustomTextField from '../components/mui/text-field'
import FormButtons from '../components/form-buttons'

interface AgencyFormProps {
  mode: 'create' | 'edit'
  agencyId?: string
  initialData?: AgencyRequestPayload
  onSuccess?: () => void
}

export const AgencyForm = ({ mode, agencyId, initialData, onSuccess }: AgencyFormProps) => {
  const { t } = useTranslation()
  const router = useRouter()
  const queryClient = useQueryClient()

  const { mutateAsync: createAgency, isPending: isCreating } = useCreateAgency({
    onSuccess: () => {
      toast.success(t('forms.agencyForm.messages.create_success'))
      queryClient.invalidateQueries({ queryKey: ['agencies'] })
      onSuccess?.()
    },
    onError: () => {
      toast.error(t('forms.agencyForm.messages.create_error') || 'Error creating agency')
    }
  })

  const { mutateAsync: updateAgency, isPending: isUpdating } = useUpdateAgency({
    onSuccess: () => {
      toast.success(t('forms.agencyForm.messages.update_success'))
      queryClient.invalidateQueries({ queryKey: ['agencies'] })
      onSuccess?.()
    },
    onError: () => {
      toast.error(t('forms.agencyForm.messages.update_error') || 'Error updating agency')
    }
  })

  const {
    control,
    handleSubmit,
    reset,
    formState: { errors }
  } = useForm<AgencyRequestPayload>({
    resolver: yupResolver(agencySchema),
    defaultValues: {
      name: '',
      address: '',
      company: '',
      is_active: true
    }
  })

  const isLoading = isCreating || isUpdating

  const handleSaveAndReturn = async (data: AgencyRequestPayload) => {
    if (mode === 'create') {
      await createAgency(data)
    } else {
      await updateAgency({ id: agencyId!, data })
    }
    reset()
  }

  const handleSaveAndEdit = async (data: AgencyRequestPayload) => {
    if (mode === 'create') {
      await createAgency(data)
    } else {
      await updateAgency({ id: agencyId!, data })
    }
    reset()
  }

  const handleSaveAndAddNew = async (data: AgencyRequestPayload) => {
    if (mode === 'create') {
      await createAgency(data)
      reset()
    } else {
      await updateAgency({ id: agencyId!, data })
      reset()
    }
  }

  return (
    <Card>
      <CardHeader title={t(`forms.agencyForm.${mode === 'create' ? 'create_title' : 'edit_title'}`)} />
      <CardContent>
        <form onSubmit={handleSubmit(handleSaveAndReturn)}>
          <Grid container spacing={5}>
            <Grid item xs={12} sm={4}>
              <Controller
                name='name'
                control={control}
                render={({ field }) => (
                  <CustomTextField
                    {...field}
                    required
                    fullWidth
                    label={t('forms.agencyForm.fields.name')}
                    error={Boolean(errors.name)}
                    helperText={errors.name?.message}
                  />
                )}
              />
            </Grid>
            <Grid item xs={12} sm={4}>
              <Controller
                name='address'
                control={control}
                render={({ field }) => (
                  <CustomTextField
                    {...field}
                    required
                    fullWidth
                    label={t('forms.agencyForm.fields.address')}
                    error={Boolean(errors.address)}
                    helperText={errors.address?.message}
                  />
                )}
              />
            </Grid>
          </Grid>
          <Grid container spacing={5} sx={{ mt: 2 }}>
            <Grid item xs={12} sm={4}>
              <Controller
                name='company'
                control={control}
                render={({ field }) => (
                  <AsyncInfiniteSelect
                    {...field}
                    fetchByIdFn={getCustomerById}
                    getOptionLabel={customer => customer.name}
                    getOptionValue={customer => String(customer.id)}
                    isOptionEqualToValue={(option, value) => String(option.id) === value}
                    label={t('forms.agencyForm.fields.company') as string}
                    value={field.value}
                    onChange={field.onChange}
                    isRequired
                    queryKey={'customers'}
                    fetchFn={fetchInfiniteCustomers}
                    placeholder={'search client..'}
                    size='small'
                    minSearchLength={2}
                    debounceMs={400}
                    prefetch
                  />
                )}
              />
            </Grid>
            <Grid item xs={12} sm={4}>
              <Controller
                name='is_active'
                control={control}
                render={({ field: { value, onChange } }) => (
                  <FormControlLabel control={<Switch checked={value} onChange={onChange} />} label='Activé' />
                )}
              />
            </Grid>
          </Grid>
          <Box sx={{ mt: 5 }}>
            <FormButtons
              isLoading={isLoading}
              mode={mode}
              cancelPath='/services/articles'
              onSave={handleSubmit(handleSaveAndReturn)}
              onSaveAndEdit={handleSubmit(handleSaveAndEdit)}
              onSaveAndAddNew={handleSubmit(handleSaveAndAddNew)}
            />
          </Box>
        </form>
      </CardContent>
    </Card>
  )
}
