import React from 'react'
import { useForm, Controller } from 'react-hook-form'
import { yupResolver } from '@hookform/resolvers/yup'
import { Box, Card, CardContent, CardHeader, Grid, MenuItem } from '@mui/material'
import CustomTextField from 'src/@core/components/mui/text-field'
import FormActions from 'src/@core/components/form-buttons'
import { dataValidationSchema } from 'src/lib/schemas'
import toast from 'react-hot-toast'
import { useTranslation } from 'react-i18next'
import { useQueryClient } from '@tanstack/react-query'
import { useRouter } from 'next/router'
import { useCreateDataValidation, useUpdateDataValidation } from 'src/hooks/user-management/useValidation'

interface DataValidationFormProps {
  initialData?: any
  mode?: 'create' | 'edit'
  dataValidationId?: string
}

const DataValidationForm: React.FC<DataValidationFormProps> = ({ initialData, mode = 'create', dataValidationId }) => {
  const { t } = useTranslation()
  const queryClient = useQueryClient()
  const router = useRouter()

  const dataTypeOptions = [
    { label: t('forms.dataValidationForm.dataTypeOptions.PURCHAGES_REQUESTS'), value: 'PURCHAGES REQUESTS' },
    { label: t('forms.dataValidationForm.dataTypeOptions.UPDATE_REQUESTS'), value: 'UPDATE REQUESTS' },
    { label: t('forms.dataValidationForm.dataTypeOptions.MISSIONS_EXPENSES'), value: 'MISSIONS EXPENSES' },
    { label: t('forms.dataValidationForm.dataTypeOptions.INVOICES'), value: 'INVOICES' }
  ]

  const {
    control,
    handleSubmit,
    formState: { errors },
    reset,
    watch,
    setValue
  } = useForm({
    defaultValues: {
      level: 2,
      level_to_validate: 2,
      data_type: 'PURCHAGES REQUESTS'
    },
    resolver: yupResolver(dataValidationSchema)
  })

  const {
    mutate: createDataValidation,
    isPending: isCreating,
    data: createDataValidationRes
  } = useCreateDataValidation({
    onSuccess: () => {
      toast.success(t('forms.dataValidationForm.messages.create_success'))
      queryClient.invalidateQueries({ queryKey: ['dataValidations'] })
    },
    onError: () => {
      toast.error(t('forms.dataValidationForm.messages.create_error'))
    }
  })
  const { mutate: updateDataValidation, isPending: isUpdating } = useUpdateDataValidation({
    onSuccess: () => {
      toast.success(t('forms.dataValidationForm.messages.update_success'))
      queryClient.invalidateQueries({ queryKey: ['dataValidations'] })
    },
    onError: () => {
      toast.error(t('forms.dataValidationForm.messages.update_error'))
    }
  })

  const isLoading = isCreating || isUpdating

  const onSubmit = (data: any) => {
    if (mode === 'create') {
      createDataValidation(data)
    } else {
      updateDataValidation({ id: dataValidationId, ...data })
    }
  }
  const handleSaveAndReturn = (data: any) => {
    onSubmit(data)
    router.push('/user-management/data-validations')
  }

  const handleSaveAndEdit = (data: any) => {
    onSubmit(data)
    if (mode === 'create' && createDataValidationRes?.id) {
      router.push(`/user-management/data-validations/${createDataValidationRes.id}/edit`)
    }
  }

  const handleSaveAndAddNew = (data: any) => {
    onSubmit(data)
    reset()
  }

  return (
    <Card>
      <CardHeader title={t(`forms.dataValidationForm.${mode === 'create' ? 'create_title' : 'edit_title'}`)} />
      <CardContent>
        <form onSubmit={handleSubmit(handleSaveAndReturn)}>
          <Grid container spacing={5}>
            <Grid item xs={12} sm={4}>
              <Controller
                name='level'
                control={control}
                render={({ field }) => (
                  <CustomTextField
                    {...field}
                    select
                    fullWidth
                    label={t('forms.dataValidationForm.fields.validationLevels')}
                    error={Boolean(errors.level)}
                    helperText={errors.level?.message as string}
                  >
                    {[0, 1, 2, 3, 4].map(value => (
                      <MenuItem key={value} value={value}>
                        {value}
                      </MenuItem>
                    ))}
                  </CustomTextField>
                )}
              />
            </Grid>
            <Grid item xs={12} sm={4}>
              <Controller
                name='level_to_validate'
                control={control}
                render={({ field }) => (
                  <CustomTextField
                    {...field}
                    select
                    fullWidth
                    label={t('forms.dataValidationForm.fields.levelTovalidate')}
                    error={Boolean(errors.level_to_validate)}
                    helperText={errors.level_to_validate?.message as string}
                  >
                    {[0, 1, 2, 3, 4].map(value => (
                      <MenuItem key={value} value={value}>
                        {value}
                      </MenuItem>
                    ))}
                  </CustomTextField>
                )}
              />
            </Grid>
          </Grid>

          <Grid container spacing={5} sx={{ mt: 2 }}>
            <Grid item xs={12} sm={4}>
              <Controller
                name='data_type'
                control={control}
                render={({ field }) => (
                  <CustomTextField
                    {...field}
                    select
                    fullWidth
                    label={t('forms.dataValidationForm.fields.dataType')}
                    error={Boolean(errors.data_type)}
                    helperText={errors.data_type?.message as string}
                  >
                    {dataTypeOptions.map(option => (
                      <MenuItem key={option.value} value={option.value}>
                        {option.label}
                      </MenuItem>
                    ))}
                  </CustomTextField>
                )}
              />
            </Grid>
          </Grid>
          <Box sx={{ mt: 5 }}>
            <FormActions
              cancelPath={'/user-management/validations'}
              isLoading={isLoading}
              mode={mode}
              onSave={handleSubmit(handleSaveAndReturn)}
              onSaveAndEdit={handleSubmit(handleSaveAndEdit)}
              onSaveAndAddNew={handleSubmit(handleSaveAndAddNew)}
            />
          </Box>
        </form>
      </CardContent>
    </Card>
  )
}

export default DataValidationForm
