import { use<PERSON><PERSON>, Controller } from 'react-hook-form'
import { yupResolver } from '@hookform/resolvers/yup'
import { useRouter } from 'next/router'
import { Card, CardContent, CardHeader, Grid, MenuItem } from '@mui/material'
import CustomTextField from 'src/@core/components/mui/text-field'
import FormActions from 'src/@core/components/form-buttons'
import { useQueryClient } from '@tanstack/react-query'
import { driverSchema } from 'src/lib/schemas'
import DatePickerWrapper from 'src/@core/styles/libs/react-datepicker'
import CustomInput from 'src/views/forms/form-elements/pickers/PickersCustomInput'
import DatePicker from 'react-datepicker'
import { useCreateDriver, useUpdateDriver } from 'src/hooks/user-management/useDrivers'
import { useTranslation } from 'react-i18next'
import toast from 'react-hot-toast'
import { useEffect } from 'react'

interface DriverFormProps {
  initialData?: any
  mode?: 'create' | 'edit'
  driverId?: string
}

interface DriverFormData {
  matricule: string
  fonction: string
  dated_embauche_societe: Date | null
  sexe: string
  date_de_naissance: Date | null
  first_name: string
  last_name: string
  address: string
  phone_number: string
}

export default function DriverForm({ initialData, mode = 'create', driverId }: DriverFormProps) {
  const router = useRouter()
  const { t } = useTranslation()
  const queryClient = useQueryClient()

  const fonctionOptions = [
    { value: 'CHAUFFEUR LIGNE', label: t('forms.driverForm.driversFunction.CHAUFFEUR LIGNE') },
    { value: 'CHAUFFEUR VILLE', label: t('forms.driverForm.driversFunction.CHAUFFEUR VILLE') },
    { value: 'CHAUFFEUR', label: t('forms.driverForm.driversFunction.CHAUFFEUR') },
    { value: 'CHAUFFEUR BUREAU', label: t('forms.driverForm.driversFunction.CHAUFFEUR BUREAU') }
  ]

  const sexeOptions = [
    { value: 'Masculin', label: t('forms.driverForm.driversSexe.Masculin') },
    { value: 'Féminin', label: t('forms.driverForm.driversSexe.Féminin') }
  ]

  const {
    mutateAsync: createDriver,
    data: createDriverRes,
    isPending: pendingCreate,
    isSuccess: successCreate
  } = useCreateDriver({
    onSuccess() {
      queryClient.invalidateQueries({ queryKey: ['drivers'] })

      toast.success(t('forms.driverForm.messages.create_success'))
    },
    onError() {
      toast.error(t('forms.driverForm.messages.create_error'))
    }
  })
  const {
    mutateAsync: updateDriver,
    isPending: pendingUpdate,
    data: updateDriverRes,
    isSuccess: successUpdate
  } = useUpdateDriver({
    onSuccess() {
      queryClient.invalidateQueries({ queryKey: ['drivers'] })
      toast.success(t('forms.driverForm.messages.update_success'))
    },
    onError() {
      toast.error(t('forms.driverForm.messages.update_error'))
    }
  })

  const {
    control,
    handleSubmit,
    reset,
    formState: { errors }
  } = useForm<DriverFormData>({
    resolver: yupResolver(driverSchema),
    defaultValues: {
      matricule: '',
      fonction: 'CHAUFFEUR LIGNE',
      dated_embauche_societe: null,
      sexe: 'Masculin',
      date_de_naissance: null,
      first_name: '',
      last_name: '',
      address: '',
      phone_number: ''
    }
  })

  useEffect(() => {
    if (mode === 'edit' && initialData) {
      reset({
        matricule: initialData.matricule,
        fonction: initialData.fonction,
        dated_embauche_societe: initialData.dated_embauche_societe
          ? new Date(initialData.dated_embauche_societe)
          : null,
        sexe: initialData.sexe,
        date_de_naissance: initialData.date_de_naissance ? new Date(initialData.date_de_naissance) : null,
        first_name: initialData.first_name,
        last_name: initialData.last_name,
        address: initialData.address,
        phone_number: initialData.phone_number
      })
    }
  }, [mode, initialData, reset])

  const handleSaveAndReturn = async (formData: any) => {
    try {
      if (mode === 'create') {
        await createDriver({ ...formData })
        router.push('/user-management/drivers')
      } else {
        await updateDriver({ data: formData, id: driverId || '' })
        router.push('/user-management/drivers')
      }
    } catch (error) {}
  }
  const handleSaveAndEdit = async (formData: any) => {
    try {
      if (mode === 'create') {
        await createDriver({ ...formData })
        router.push(`/user-management/drivers/${createDriverRes.id}/edit`)
      } else {
        await updateDriver({ data: formData, id: driverId || '' })
        router.push(`/user-management/drivers/${updateDriverRes.id}/edit`)
      }
    } catch (error) {}
  }
  const handleSaveAndAddNew = async (formData: any) => {
    try {
      if (mode === 'create') {
        await createDriver({ ...formData })
        reset()
      } else {
        await updateDriver({ data: formData, id: driverId || '' })
        reset()
      }
    } catch (error) {}
  }

  return (
    <Card>
      <CardHeader title={t(`forms.driverForm.${mode === 'create' ? 'create_title' : 'edit_title'}`)} />
      <CardContent>
        <form onSubmit={handleSubmit(handleSaveAndReturn)}>
          <Grid container spacing={5}>
            <Grid item xs={12} sm={4}>
              <Controller
                name='matricule'
                control={control}
                render={({ field }) => (
                  <CustomTextField
                    {...field}
                    fullWidth
                    label={t('forms.driverForm.fields.matricule')}
                    error={Boolean(errors.matricule)}
                    helperText={errors.matricule?.message as string}
                  />
                )}
              />
            </Grid>
            <Grid item xs={12} sm={4}>
              <Controller
                name='fonction'
                control={control}
                render={({ field }) => (
                  <CustomTextField
                    {...field}
                    select
                    fullWidth
                    label={t('forms.driverForm.fields.fonction')}
                    error={Boolean(errors.fonction)}
                    helperText={errors.fonction?.message as string}
                  >
                    {fonctionOptions.map(option => (
                      <MenuItem key={option.value} value={option.value}>
                        {option.label}
                      </MenuItem>
                    ))}
                  </CustomTextField>
                )}
              />
            </Grid>
            <Grid item xs={12} sm={4}>
              <Controller
                name='dated_embauche_societe'
                control={control}
                render={({ field }) => (
                  <DatePickerWrapper>
                    <DatePicker
                      selected={field.value}
                      onChange={(date: Date | null) => field.onChange(date)}
                      customInput={
                        <CustomInput
                          fullWidth
                          label={t('forms.driverForm.fields.dated_embauche_societe') as string}

                          //   error={Boolean(errors.dated_embauche_societe)}
                          //   helperText={errors.dated_embauche_societe?.message as string}
                        />
                      }
                    />
                  </DatePickerWrapper>
                )}
              />
            </Grid>
            <Grid item xs={12} sm={4}>
              <Controller
                name='sexe'
                control={control}
                render={({ field }) => (
                  <CustomTextField
                    {...field}
                    select
                    fullWidth
                    label={t('forms.driverForm.fields.sexe')}
                    error={Boolean(errors.sexe)}
                    helperText={errors.sexe?.message as string}
                  >
                    {sexeOptions.map(option => (
                      <MenuItem key={option.value} value={option.value}>
                        {option.label}
                      </MenuItem>
                    ))}
                  </CustomTextField>
                )}
              />
            </Grid>
            <Grid item xs={12} sm={4}>
              <Controller
                name='date_de_naissance'
                control={control}
                render={({ field }) => (
                  <DatePickerWrapper>
                    <DatePicker
                      selected={field.value}
                      onChange={(date: Date | null) => field.onChange(date)}
                      customInput={
                        <CustomInput
                          fullWidth
                          label={t('forms.driverForm.fields.date_de_naissance') as string}

                          //   error={Boolean(errors.date_de_naissance ? true : false)}
                          //   helperText={errors.date_de_naissance?.message as string}
                        />
                      }
                    />
                  </DatePickerWrapper>
                )}
              />
            </Grid>
            <Grid item xs={12} sm={4}>
              <Controller
                name='first_name'
                control={control}
                render={({ field }) => (
                  <CustomTextField
                    {...field}
                    fullWidth
                    label={t('forms.driverForm.fields.first_name')}
                    error={Boolean(errors.first_name)}
                    helperText={errors.first_name?.message as string}
                  />
                )}
              />
            </Grid>
            <Grid item xs={12} sm={4}>
              <Controller
                name='last_name'
                control={control}
                render={({ field }) => (
                  <CustomTextField
                    {...field}
                    fullWidth
                    label={t('forms.driverForm.fields.last_name')}
                    error={Boolean(errors.last_name)}
                    helperText={errors.last_name?.message as string}
                  />
                )}
              />
            </Grid>
            <Grid item xs={12} sm={4}>
              <Controller
                name='address'
                control={control}
                render={({ field }) => (
                  <CustomTextField
                    {...field}
                    fullWidth
                    label={t('forms.driverForm.fields.address')}
                    error={Boolean(errors.address)}
                    helperText={errors.address?.message as string}
                  />
                )}
              />
            </Grid>
            <Grid item xs={12} sm={4}>
              <Controller
                name='phone_number'
                control={control}
                render={({ field }) => (
                  <CustomTextField
                    {...field}
                    fullWidth
                    label={t('forms.driverForm.fields.phone_number')}
                    error={Boolean(errors.phone_number)}
                    helperText={errors.phone_number?.message as string}
                  />
                )}
              />
            </Grid>
          </Grid>
          <FormActions
            isLoading={pendingCreate || pendingUpdate}
            mode={mode}
            onSave={handleSubmit(handleSaveAndReturn)}
            onSaveAndAddNew={handleSubmit(handleSaveAndAddNew)}
            onSaveAndEdit={handleSubmit(handleSaveAndEdit)}
            cancelPath={'/user-management/drivers'}
          />
        </form>
      </CardContent>
    </Card>
  )
}
