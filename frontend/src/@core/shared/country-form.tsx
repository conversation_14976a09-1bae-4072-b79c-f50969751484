import { useEffect, useState } from 'react'
import { useForm, Controller } from 'react-hook-form'
import { yupResolver } from '@hookform/resolvers/yup'
import * as yup from 'yup'
import {
  Box,
  Card,
  CardContent,
  CardHeader,
  Grid,
  Button,
  FormHelperText,
  Switch,
  FormControlLabel,
  CircularProgress
} from '@mui/material'
import CustomTextField from 'src/@core/components/mui/text-field'
import { useCreateCountry, useCountryById, useUpdateCountry } from 'src/hooks/_services/useCountries'
import toast from 'react-hot-toast'
import { CountryFormData, CountryRequestPayload } from 'src/types/models/_services/countries'
import LoadingButton from '@mui/lab/LoadingButton'
import { useRouter } from 'next/router'
import Icon from 'src/@core/components/icon'
import { countrySchema } from 'src/lib/schemas'

interface CountryFormProps {
  mode: 'create' | 'edit'
  countryId?: string
  onSuccess?: () => void
}

export const CountryForm = ({ mode, countryId, onSuccess }: CountryFormProps) => {
  const [initialData, setInitialData] = useState<CountryFormData | null>(null)
  const router = useRouter()

  const { data: countryData, isLoading: isLoadingCountry } = useCountryById(countryId || '')

  const { mutateAsync: createCountry, isPending: isCreating } = useCreateCountry({
    onSuccess: () => {
      onSuccess && onSuccess()
    }
  })

  const { mutateAsync: updateCountry, isPending: isUpdating } = useUpdateCountry(countryId || '', {
    onSuccess: () => {
      onSuccess && onSuccess()
    }
  })

  const {
    control,
    handleSubmit,
    reset,
    formState: { errors, isSubmitting }
  } = useForm<CountryFormData>({
    resolver: yupResolver(countrySchema),
    defaultValues: initialData || {
      name: '',
      iso_2: '',
      code: '',
      is_active: true
    }
  })

  useEffect(() => {
    if (mode === 'edit' && countryData) {
      setInitialData({
        name: countryData.name,
        iso_2: countryData.iso_2,
        code: countryData.code || '',
        is_active: countryData.is_active
      })
      reset({
        name: countryData.name,
        iso_2: countryData.iso_2,
        code: countryData.code || '',
        is_active: countryData.is_active
      })
    }
  }, [countryData, mode, reset])

  const handleSave = async (formData: CountryFormData) => {
    try {
      if (mode === 'create') {
        const response = await createCountry(formData as CountryRequestPayload)
        toast.success('Pays créé avec succès')

        return response
      } else {
        const response = await updateCountry(formData as CountryRequestPayload)
        toast.success('Pays modifié avec succès')

        return response
      }
    } catch (error) {
      console.error(error)
      toast.error('Une erreur est survenue')
      throw error
    }
  }

  const handleSaveAndReturn = async (formData: CountryFormData) => {
    try {
      await handleSave(formData)
      router.push('/settings/countries')
    } catch (error) {
      // Error handled in handleSave
    }
  }

  const handleSaveAndEdit = async (formData: CountryFormData) => {
    try {
      const country = await handleSave(formData)
      if (mode === 'create' && country?.id) {
        router.push(`/settings/countries/${country.id}/edit`)
      }
    } catch (error) {
      // Error handled in handleSave
    }
  }

  const handleSaveAndAddNew = async (formData: CountryFormData) => {
    try {
      await handleSave(formData)
      if (mode === 'create') {
        reset({
          name: '',
          iso_2: '',
          code: '',
          is_active: true
        })
      }
    } catch (error) {
      // Error handled in handleSave
    }
  }

  if (mode === 'edit' && isLoadingCountry) {
    return <div>Chargement...</div>
  }

  const isLoading = isCreating || isUpdating || isSubmitting

  return (
    <Card>
      <CardHeader title={mode === 'create' ? 'Créer un pays' : 'Modifier un pays'} />
      <CardContent>
        <form onSubmit={e => e.preventDefault()}>
          <Grid container spacing={5}>
            <Grid item xs={12} sm={6}>
              <Controller
                name='name'
                control={control}
                render={({ field }) => (
                  <CustomTextField
                    {...field}
                    fullWidth
                    label='Nom'
                    placeholder='France'
                    error={Boolean(errors.name)}
                    helperText={errors.name?.message}
                  />
                )}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <Controller
                name='iso_2'
                control={control}
                render={({ field }) => (
                  <CustomTextField
                    {...field}
                    fullWidth
                    label='Code ISO 2'
                    placeholder='FR'
                    error={Boolean(errors.iso_2)}
                    helperText={errors.iso_2?.message}
                  />
                )}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <Controller
                name='code'
                control={control}
                render={({ field }) => (
                  <CustomTextField
                    {...field}
                    fullWidth
                    label='Code téléphonique'
                    placeholder='33'
                    error={Boolean(errors.code)}
                    helperText={errors.code?.message}
                  />
                )}
              />
            </Grid>

            <Grid item xs={12}>
              <Controller
                name='is_active'
                control={control}
                render={({ field: { value, onChange } }) => (
                  <FormControlLabel control={<Switch checked={value} onChange={onChange} />} label='Actif' />
                )}
              />
            </Grid>
            <Grid item xs={12}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                <Button
                  type='button'
                  variant='outlined'
                  color='secondary'
                  onClick={() => router.back()}
                  disabled={isLoading}
                  startIcon={<Icon icon='mdi:arrow-left' />}
                >
                  Annuler
                </Button>
                <Box sx={{ display: 'flex', gap: 2 }}>
                  <Button
                    type='button'
                    variant='contained'
                    color='primary'
                    onClick={handleSubmit(handleSaveAndEdit)}
                    disabled={isLoading}
                    startIcon={<Icon icon='mdi:content-save-edit' />}
                  >
                    {isLoading ? (
                      <>
                        <CircularProgress size={20} sx={{ mr: 2 }} />
                        Enregistrement...
                      </>
                    ) : mode === 'create' ? (
                      'Créer & Modifier'
                    ) : (
                      'Enregistrer & Continuer'
                    )}
                  </Button>
                  {mode === 'create' && (
                    <Button
                      type='button'
                      variant='contained'
                      color='success'
                      onClick={handleSubmit(handleSaveAndAddNew)}
                      disabled={isLoading}
                      startIcon={<Icon icon='mdi:plus-circle' />}
                    >
                      {isLoading ? (
                        <>
                          <CircularProgress size={20} sx={{ mr: 2 }} />
                          Enregistrement...
                        </>
                      ) : (
                        'Créer & Ajouter Nouveau'
                      )}
                    </Button>
                  )}
                  <Button
                    type='button'
                    variant='contained'
                    color='primary'
                    onClick={handleSubmit(handleSaveAndReturn)}
                    disabled={isLoading}
                    startIcon={<Icon icon='mdi:content-save' />}
                  >
                    {isLoading ? (
                      <>
                        <CircularProgress size={20} sx={{ mr: 2 }} />
                        Enregistrement...
                      </>
                    ) : (
                      'Enregistrer'
                    )}
                  </Button>
                </Box>
              </Box>
            </Grid>
          </Grid>
        </form>
      </CardContent>
    </Card>
  )
}
