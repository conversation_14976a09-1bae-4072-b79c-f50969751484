import React, { useEffect } from 'react'
import { Box, Grid, Typography, Switch, FormControlLabel, Card, CardHeader, Divider } from '@mui/material'
import { useTranslation } from 'react-i18next'
import DatePicker from 'react-datepicker'
import { Controller, useForm } from 'react-hook-form'
import { yupResolver } from '@hookform/resolvers/yup'
import * as yup from 'yup'
import CustomInput from 'src/views/forms/form-elements/pickers/PickersCustomInput'
import DatePickerWrapper from 'src/@core/styles/libs/react-datepicker'
import { Travel, TravelFormData } from 'src/types/models/_services/travel'
import FormButtons from '../components/form-buttons'
import { useCreateTravel, useUpdateTravel } from 'src/hooks/_services/useTravelmanagement'
import { useRouter } from 'next/router'
import { toast } from 'react-hot-toast'
import { formateTableData, formateTime } from 'src/utils'
import { travelSchema } from 'src/lib/schemas'

type DetailRowProps = {
  label: string
  value: string | number | React.ReactNode
}

interface TravelFormProps {
  mode?: 'create' | 'edit'
  travel?: Partial<Travel>
  travelId?: string
  readOnly?: boolean
  onSuccess?: () => void
}

const DetailRow = ({ label, value }: DetailRowProps) => (
  <Box sx={{ display: 'flex', flexDirection: 'column', mb: 2 }}>
    <Typography variant='caption' sx={{ color: 'text.secondary', fontWeight: 600 }}>
      {label}
    </Typography>
    <Typography variant='body2' sx={{ fontWeight: 500 }}>
      {value}
    </Typography>
  </Box>
)

export default function TravelForm({
  mode = 'create',
  travel,
  travelId,
  readOnly = false,
  onSuccess
}: TravelFormProps) {
  const { t } = useTranslation()

  const router = useRouter()

  const { mutateAsync: createTravel, isPending: isCreating } = useCreateTravel({
    onSuccess: () => {
      toast.success('Voyage créé avec succès')
      onSuccess?.()
    },
    onError: () => {
      toast.error('Erreur lors de la création du voyage')
    }
  })

  const { mutateAsync: updateTravel, isPending: isUpdating } = useUpdateTravel(travelId || '', {
    onSuccess: () => {
      toast.success('Voyage mis à jour avec succès')
      onSuccess?.()
    },
    onError: () => {
      toast.error('Erreur lors de la mise à jour du voyage')
    }
  })

  const {
    control,
    handleSubmit,
    reset,
    formState: { errors }
  } = useForm<TravelFormData>({
    resolver: yupResolver(travelSchema),
    defaultValues: {
      order: travel?.order?.code?.toString() || '',
      process: travel?.process?.be_number || '',
      duration: travel?.duration || 0,
      started_date: travel?.started_date ? new Date(travel.started_date) : new Date(),
      ended_date: travel?.ended_date ? new Date(travel.ended_date) : new Date(),
      started_at: travel?.started_at ? new Date(travel.started_at) : new Date(),
      ended_at: travel?.ended_at ? new Date(travel.ended_at) : new Date(),
      charged_date: travel?.charged_date ? new Date(travel.charged_date) : new Date(),
      unloaded_date: travel?.unloaded_date ? new Date(travel.unloaded_date) : new Date(),
      charged_at: travel?.charged_at ? new Date(travel.charged_at) : new Date(),
      unloaded_at: travel?.unloaded_at ? new Date(travel.unloaded_at) : new Date(),
      is_round_trip: travel?.is_round_trip || false,
      immobilization: travel?.immobilization || [],
      blocking_duration: travel?.blocking_duration || 0
    }
  })

  useEffect(() => {
    reset({
      order: travel?.order?.code?.toString() || '',
      process: travel?.process?.be_number || '',
      duration: travel?.duration || 0,
      started_date: travel?.started_date ? new Date(travel.started_date) : new Date(),
      ended_date: travel?.ended_date ? new Date(travel.ended_date) : new Date(),
      started_at: travel?.started_at ? new Date(travel.started_at) : new Date(),
      ended_at: travel?.ended_at ? new Date(travel.ended_at) : new Date(),
      charged_date: travel?.charged_date ? new Date(travel.charged_date) : new Date(),
      unloaded_date: travel?.unloaded_date ? new Date(travel.unloaded_date) : new Date(),
      charged_at: travel?.charged_at ? new Date(travel.charged_at) : new Date(),
      unloaded_at: travel?.unloaded_at ? new Date(travel.unloaded_at) : new Date(),
      is_round_trip: travel?.is_round_trip || false,
      immobilization: travel?.immobilization || [],
      blocking_duration: travel?.blocking_duration || 0
    })
  }, [reset, travel])

  const handleSave = async (data: TravelFormData) => {
    try {
      if (mode === 'create') {
        // await createTravel(data)
      } else {
        // await updateTravel(data)
      }
    } catch (error) {
      // Error is handled by mutation callbacks
    }
  }

  const handleSaveAndReturn = async (data: TravelFormData) => {
    try {
      await handleSave(data)
      router.push('/services/travels')
    } catch (error) {
      // Error handled in handleSave
    }
  }

  const handleSaveAndCreate = async (data: TravelFormData) => {
    try {
      await handleSave(data)
      router.push('/services/travels/create')
    } catch (error) {
      // Error handled in handleSave
    }
  }

  const handleSaveAndEdit = async (data: TravelFormData) => {
    try {
      if (mode === 'create') {
        // const response = await createTravel(data)
        // router.push(`/services/travels/${response.id}/edit`)
      } else {
        // await updateTravel(data)
      }
    } catch (error) {
      // Error handled in handleSave
    }
  }

  const isLoading = isCreating || isUpdating

  const renderDateTimePicker = (dateFieldName: any, timeFieldName: any, label: string) => (
    <Grid container spacing={2}>
      <Grid item xs={12} sm={6} md={4} lg={3}>
        <DatePickerWrapper sx={{ width: '100%' }}>
          <Controller
            name={dateFieldName}
            control={control}
            render={({ field }) => (
              <DatePicker
                selected={field.value}
                onChange={field.onChange}
                dateFormat='dd/MM/yyyy'
                disabled={readOnly}
                customInput={<CustomInput fullWidth label={`${label} - Date`} />}
              />
            )}
          />
        </DatePickerWrapper>
      </Grid>
      <Grid item xs={12} sm={6} md={4} lg={3}>
        <DatePickerWrapper sx={{ width: '100%' }}>
          <Controller
            name={timeFieldName}
            control={control}
            render={({ field }) => (
              <DatePicker
                selected={field.value}
                onChange={field.onChange}
                showTimeSelect
                showTimeSelectOnly
                timeIntervals={15}
                timeFormat='HH:mm'
                dateFormat='HH:mm'
                disabled={readOnly}
                customInput={
                  <CustomInput
                    fullWidth
                    label={`${label} - Heure`}

                    // error={Boolean(errors[timeFieldName])}
                    // helperText={errors[timeFieldName]?.message}
                  />
                }
              />
            )}
          />
        </DatePickerWrapper>
      </Grid>
    </Grid>
  )

  return (
    <Card>
      <CardHeader
        title={
          mode === 'create'
            ? 'Ajouter Voyage'
            : `${travel?.code} ${formateTableData(travel?.started_date)} ${formateTime(travel?.started_at)}`
        }
        sx={{
          px: 5,
          py: 5,
          '& .MuiCardHeader-title': {
            fontWeight: 600
          }
        }}
      />
      <Box sx={{ p: 5, pt: 0 }}>
        <form onSubmit={handleSubmit(handleSave)}>
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <Typography variant='subtitle2' sx={{ mb: 0.5 }}>
                Commencé le :
              </Typography>
              {renderDateTimePicker('started_date', 'started_at', 'Début')}
            </Grid>

            <Grid item xs={12}>
              <Typography variant='subtitle2' sx={{ mb: 0.5 }}>
                Terminé le :
              </Typography>
              {renderDateTimePicker('ended_date', 'ended_at', 'Fin')}
            </Grid>

            <Grid item xs={12}>
              <Typography variant='subtitle2' sx={{ mb: 0.5 }}>
                Chargé le :
              </Typography>
              {renderDateTimePicker('charged_date', 'charged_at', 'Chargement')}
            </Grid>

            <Grid item xs={12}>
              <Typography variant='subtitle2' sx={{ mb: 0.5 }}>
                Déchargé le :
              </Typography>
              {renderDateTimePicker('unloaded_date', 'unloaded_at', 'Déchargement')}
            </Grid>

            <Grid item xs={12}>
              <FormControlLabel
                control={
                  <Controller
                    name='is_round_trip'
                    control={control}
                    render={({ field }) => <Switch {...field} checked={field.value} disabled={readOnly} size='small' />}
                  />
                }
                label={<Typography sx={{ fontWeight: 600 }}>Voyage aller et retour ?</Typography>}
              />
            </Grid>

            {/* New Details Section */}
            <Grid item xs={12}>
              <Divider sx={{ my: 2 }} />
              <Grid container spacing={2}>
                <Grid item xs={12} md={6}>
                  <DetailRow label='Commande' value={travel?.order?.code ? travel?.order?.code : ''} />
                  <DetailRow
                    label='Durrée du voyage'
                    value={
                      <Box>
                        <Typography variant='body2' sx={{ fontWeight: 500 }}>
                          {travel?.duration || 0}
                        </Typography>
                        <Typography variant='caption' sx={{ color: 'text.secondary' }}>
                          La durrée du vayage est estimée en nombre d'heures (Ex: 48 pour definir 2 jours)
                        </Typography>
                      </Box>
                    }
                  />
                  <DetailRow
                    label='Opération'
                    value={travel?.process?.be_number ? `${travel?.order?.code} - ${travel?.process?.be_number}` : ''}
                  />
                  <DetailRow
                    label='Date Enreg. Charg.'
                    value={
                      travel?.charged_date
                        ? `${formateTableData(travel?.charged_date)} ${formateTime(travel?.charged_at)}`
                        : ''
                    }
                  />
                </Grid>
                <Grid item xs={12} md={6}>
                  <DetailRow
                    label='Durée Immobilisations'
                    value={
                      <Box>
                        <Typography variant='body2' sx={{ fontWeight: 500 }}>
                          {travel?.immobilization?.length || 0}
                        </Typography>
                        <Typography variant='caption' sx={{ color: 'text.secondary' }}>
                          Durée total des immobilisations en heures
                        </Typography>
                      </Box>
                    }
                  />
                  <DetailRow
                    label='Date Enreg. Debut'
                    value={
                      travel?.started_date
                        ? `${formateTableData(travel?.started_date)} ${formateTime(travel?.started_at)}`
                        : ''
                    }
                  />
                  <DetailRow
                    label='Date Enreg. Décharg.'
                    value={
                      travel?.unloaded_date
                        ? `${formateTableData(travel?.unloaded_date)} ${formateTime(travel?.unloaded_at)}`
                        : ''
                    }
                  />
                  <DetailRow
                    label='Date Enreg. Fin'
                    value={
                      travel?.ended_date
                        ? `${formateTableData(travel?.ended_date)} ${formateTime(travel?.ended_at)}`
                        : ''
                    }
                  />
                </Grid>
              </Grid>
            </Grid>

            <Grid item xs={12}>
              <FormButtons
                isLoading={isLoading}
                mode={mode}
                cancelPath='/services/travels'
                onSave={handleSubmit(handleSave)}
                onSaveAndAddNew={handleSubmit(handleSaveAndCreate)}
                onSaveAndEdit={handleSubmit(handleSaveAndEdit)}
              />
            </Grid>
          </Grid>
        </form>
      </Box>
    </Card>
  )
}
