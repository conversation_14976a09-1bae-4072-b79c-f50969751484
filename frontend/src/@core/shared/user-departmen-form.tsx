import { useEffect, useState } from 'react'
import { useRouter } from 'next/router'
import { useForm, Controller } from 'react-hook-form'
import { yupResolver } from '@hookform/resolvers/yup'
import * as yup from 'yup'
import { useTranslation } from 'react-i18next'
import { useQueryClient } from '@tanstack/react-query'
import toast from 'react-hot-toast'

// MUI Imports
import Box from '@mui/material/Box'
import Card from '@mui/material/Card'
import CardHeader from '@mui/material/CardHeader'
import CardContent from '@mui/material/CardContent'
import Grid from '@mui/material/Grid'

// Custom Components
import CustomTextField from 'src/@core/components/mui/text-field'
import FormButtons from 'src/@core/components/form-buttons'
import { useCreateUserDepartment, useUpdateUserDepartment } from 'src/hooks/_services/useUserDepartment'

// Hooks

// Define form data type
interface DepartmentFormData {
  name: string

  //   code: string
  //   description: string
  //   is_active: boolean
}

// Form validation schema
const departmentSchema = yup.object().shape({
  name: yup.string().required('Name is required')

  //   code: yup.string().required('Code is required'),
  //   description: yup.string(),
  //   is_active: yup.boolean().default(true)
})

interface UserDepartmentFormProps {
  mode: 'create' | 'edit'
  departmentId?: string
  initialData?: DepartmentFormData
  onSuccess?: () => void
}

export default function UserDepartmentForm({
  mode = 'create',
  departmentId,
  initialData,
  onSuccess
}: UserDepartmentFormProps) {
  const { t } = useTranslation()
  const router = useRouter()
  const queryClient = useQueryClient()

  // Create mutation
  const {
    mutateAsync: createDepartment,
    isPending: isCreating,
    data: createDepartmentRes
  } = useCreateUserDepartment({
    onSuccess: () => {
      toast.success(t('forms.userDepartment.messages.create_success'))
      queryClient.invalidateQueries({ queryKey: ['user-departments'] })
      onSuccess?.()
    },
    onError: () => {
      toast.error(t('forms.userDepartment.messages.create_error') || 'Error creating department')
    }
  })

  // Update mutation
  const {
    mutateAsync: updateDepartment,
    isPending: isUpdating,
    data: updateDepartmentRes
  } = useUpdateUserDepartment(departmentId || '', {
    onSuccess: () => {
      toast.success(t('forms.userDepartment.messages.update_success'))
      queryClient.invalidateQueries({ queryKey: ['departments'] })
      onSuccess?.()
    },
    onError: () => {
      toast.error(t('forms.userDepartment.messages.update_error') || 'Error updating department')
    }
  })

  // Form setup
  const {
    control,
    handleSubmit,
    reset,
    formState: { errors }
  } = useForm<DepartmentFormData>({
    resolver: yupResolver(departmentSchema),
    defaultValues: initialData || {
      name: ''

      //   code: '',
      //   description: '',
      //   is_active: true
    }
  })

  // Reset form when initialData changes
  useEffect(() => {
    if (initialData) {
      reset(initialData)
    }
  }, [initialData, reset])

  // Form submission handlers
  const handleSave = async (data: DepartmentFormData) => {
    try {
      if (mode === 'create') {
        await createDepartment(data)
      } else {
        await updateDepartment(data)
      }
    } catch (error) {
      // Error is handled by mutation callbacks
    }
  }

  const handleSaveAndReturn = async (data: DepartmentFormData) => {
    try {
      await handleSave(data)
      router.push('/users-management/departments')
    } catch (error) {
      // Error handled in handleSave
    }
  }

  const handleSaveAndEdit = async (data: DepartmentFormData) => {
    await handleSave(data)
    if (mode === 'create' && createDepartmentRes?.id) {
      router.push(`/users-management/departments/${createDepartmentRes.id}/edit`)
    }
  }

  const handleSaveAndCreate = async (data: DepartmentFormData) => {
    try {
      await handleSave(data)
      reset({
        name: ''

        // code: '',
        // description: '',
        // is_active: true
      })
    } catch (error) {
      // Error handled in handleSave
    }
  }

  const isLoading = isCreating || isUpdating

  return (
    <Card>
      <CardHeader title={t(`forms.userDepartment.${mode === 'create' ? 'create_title' : 'edit_title'}`)} />
      <CardContent>
        <form onSubmit={e => e.preventDefault()}>
          <Grid container spacing={5}>
            <Grid item xs={6}>
              <Controller
                name='name'
                control={control}
                render={({ field }) => (
                  <CustomTextField
                    fullWidth
                    label={t('forms.userDepartment.fields.name')}
                    error={Boolean(errors.name)}
                    helperText={errors.name?.message}
                    {...field}
                  />
                )}
              />
            </Grid>

            {/* <Grid item xs={12}>
              <Controller
                name='code'
                control={control}
                render={({ field }) => (
                  <CustomTextField
                    fullWidth
                    label={t('department.form.code') || 'Code'}
                    error={Boolean(errors.code)}
                    helperText={errors.code?.message}
                    {...field}
                  />
                )}
              />
            </Grid> */}

            {/* <Grid item xs={12}>
              <Controller
                name='description'
                control={control}
                render={({ field }) => (
                  <CustomTextField
                    fullWidth
                    multiline
                    rows={4}
                    label={t('department.form.description') || 'Description'}
                    error={Boolean(errors.description)}
                    helperText={errors.description?.message}
                    {...field}
                  />
                )}
              />
            </Grid> */}
          </Grid>

          <Box sx={{ mt: 5, display: 'flex', justifyContent: 'flex-end' }}>
            <FormButtons
              isLoading={isLoading}
              mode={mode}
              cancelPath='/departments'
              onSave={handleSubmit(handleSaveAndReturn)}
              onSaveAndAddNew={handleSubmit(handleSaveAndCreate)}
              onSaveAndEdit={handleSubmit(handleSaveAndEdit)}
            />
          </Box>
        </form>
      </CardContent>
    </Card>
  )
}
