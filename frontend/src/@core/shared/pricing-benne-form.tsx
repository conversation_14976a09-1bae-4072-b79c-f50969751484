import { Fragment, useEffect, useState } from 'react'
import { useRouter } from 'next/router'
import { Card, CardContent, CardHeader, Box, CircularProgress, Al<PERSON>, But<PERSON>, Grid } from '@mui/material'
import { useForm, Controller } from 'react-hook-form'
import { yupResolver } from '@hookform/resolvers/yup'
import * as yup from 'yup'
import CustomTextField from 'src/@core/components/mui/text-field'
import CustomAutocomplete from 'src/@core/components/mui/autocomplete'
import { useArticles } from 'src/hooks/_services/useArticles'
import { useRoutes } from '../../hooks/helpers/useRoutes'
import Icon from 'src/@core/components/icon'
import toast from 'react-hot-toast'
import { useQueryClient } from '@tanstack/react-query'
import { useCustomers } from 'src/hooks/useCustomer'
import { useCreatePricingBenne, useUpdatePricingBenne } from 'src/hooks/_services/usePrisingBanne'
import { useNatureOfArticleById, useNatureOfArticles } from '../../hooks/helpers/useNatureOfArticles'
import FormButtons from '../components/form-buttons'
import { PricingBenne } from 'src/types/models/_services/price-banne'
import { pricingBenneSchema } from 'src/lib/schemas'
import { PricingBenneFormData } from 'src/types/models/_services/pricing-route'

interface PricingBenneFormProps {
  mode: 'create' | 'edit'
  initialData?: PricingBenne
  pricingId?: string
  onSuccess?: () => void
}

export const PricingBenneForm = ({ mode, initialData, pricingId, onSuccess }: PricingBenneFormProps) => {
  const router = useRouter()
  const queryClient = useQueryClient()

  const {
    data: customersData,
    isLoading: isLoadingCustomers,
    isError: isCustomersError
  } = useCustomers({
    limit: 100,
    offset: 0
  })

  // Get services using the existing hook
  const {
    data: productsData,
    isLoading: isLoadingProducts,
    isError: isProductsError
  } = useNatureOfArticles({
    limit: 100,
    offset: 0
  })

  const {
    data: routesData,
    isLoading: isLoadingRoutes,
    isError: isRoutesError
  } = useRoutes({
    limit: 100,
    offset: 0
  })

  const {
    mutateAsync: createPricingBenne,
    isPending: isCreating,
    data: princingBenneData
  } = useCreatePricingBenne({
    onSuccess: () => {
      toast.success('Tarif créé avec succès')
      onSuccess?.()
    },
    onError: () => {
      toast.error("Une erreur s'est produite")
    }
  })

  const { mutateAsync: updatePricingBenne, isPending: isUpdating } = useUpdatePricingBenne(pricingId || '', {
    onSuccess: () => {
      toast.success('Tarif mis à jour avec succès')
      onSuccess?.()
    },
    onError: () => {
      toast.error("Une erreur s'est produite")
    }
  })

  const {
    control,
    handleSubmit,
    reset,
    formState: { errors }
  } = useForm<PricingBenneFormData>({
    resolver: yupResolver(pricingBenneSchema),
    defaultValues: {
      route: initialData?.route?.id,
      product: initialData?.product?.id,
      customer: initialData?.customer?.id,
      unite_price: initialData?.unite_price
    }
  })

  useEffect(() => {
    reset({
      route: initialData?.route?.id,
      product: initialData?.product?.id,
      customer: initialData?.customer?.id,
      unite_price: initialData?.unite_price
    })
  }, [reset, initialData])

  const handleCreate = (data: PricingBenneFormData) => {
    if (mode === 'create') {
      createPricingBenne(data)
    } else {
      updatePricingBenne(data)
    }
  }

  const handleCreateAndSave = (data: PricingBenneFormData) => {
    if (mode === 'create') {
      createPricingBenne(data)
    } else {
      updatePricingBenne(data)
    }
    router.push('/services/pricing-benne')
  }

  const handleCreateAndUpdate = async (data: PricingBenneFormData) => {
    let newId = princingBenneData.id
    if (mode === 'create') {
      createPricingBenne(data)
      newId = princingBenneData.id
    } else {
      updatePricingBenne(data)
    }
    router.push(`/services/pricing-benne/${newId}/edit`)
  }

  const isLoading = isCreating || isUpdating

  return (
    <Card>
      <CardHeader
        title={
          mode === 'create'
            ? 'Créer un tarif'
            : `${initialData?.route.name}/${initialData?.product.name}/${initialData?.unite_price}`
        }
        sx={{
          px: 5,
          py: 5,
          '& .MuiCardHeader-title': {
            fontWeight: 600
          }
        }}
      />
      <CardContent>
        <form>
          <Box sx={{ mb: 4 }}>
            <Controller
              name='route'
              control={control}
              render={({ field: { value, onChange, ...field } }) => {
                return (
                  <CustomAutocomplete
                    {...field}
                    options={routesData?.results || []}
                    loading={isLoadingRoutes}
                    id={`route-autocomplete-${initialData?.id}`}
                    value={routesData?.results?.find((route: any) => route.id === value) || null}
                    onChange={(_, newValue) => onChange(newValue ? newValue.id : '')}
                    getOptionLabel={option => {
                      console.log(option)

                      return option.name || ''
                    }}
                    isOptionEqualToValue={(option, value) => option.id === value.id}
                    renderInput={params => (
                      <CustomTextField
                        {...params}
                        label='Trajet'
                        error={Boolean(errors.route)}
                        helperText={errors.route?.message}
                        InputProps={{
                          ...params.InputProps,
                          endAdornment: (
                            <Fragment>
                              {isLoadingRoutes ? <CircularProgress size={20} /> : null}

                              {params.InputProps.endAdornment}
                            </Fragment>
                          )
                        }}
                      />
                    )}
                  />
                )
              }}
            />
          </Box>

          <Box sx={{ mb: 4 }}>
            <Controller
              name='product'
              control={control}
              render={({ field: { value, onChange, ...field } }) => (
                <CustomAutocomplete
                  fullWidth
                  options={productsData?.results || []}
                  id={`product-autocomplete-${initialData?.id}`}
                  loading={isLoadingProducts}
                  getOptionLabel={option => (typeof option === 'string' ? option : option.name)}
                  renderInput={params => (
                    <CustomTextField
                      {...params}
                      label='Nature du produit'
                      error={Boolean(errors.product)}
                      helperText={errors.product?.message}
                    />
                  )}
                  value={value ? productsData?.results?.find((product: any) => product.id === value) || null : null}
                  onChange={(_, newValue) => {
                    onChange(newValue ? (typeof newValue === 'string' ? newValue : newValue.id) : '')
                  }}
                  isOptionEqualToValue={(option, value) => {
                    return option.id === (typeof value === 'string' ? value : value?.id)
                  }}
                  {...field}
                />
              )}
            />
          </Box>

          <Box sx={{ mb: 4 }}>
            <Controller
              name='customer'
              control={control}
              render={({ field: { value, onChange, ...field } }) => (
                <CustomAutocomplete
                  fullWidth
                  options={customersData?.results || []}
                  id={`customer-autocomplete-${initialData?.id}`}
                  loading={isLoadingCustomers}
                  getOptionLabel={option => (typeof option === 'string' ? option : option.name)}
                  renderInput={params => (
                    <CustomTextField
                      {...params}
                      label='Client'
                      error={Boolean(errors.customer)}
                      helperText={errors.customer?.message}
                    />
                  )}
                  value={value ? customersData?.results?.find((customer: any) => customer.id === value) || null : null}
                  onChange={(_, newValue) => {
                    onChange(newValue ? (typeof newValue === 'string' ? newValue : newValue.id) : '')
                  }}
                  isOptionEqualToValue={(option, value) => {
                    return option.id === (typeof value === 'string' ? value : value?.id)
                  }}
                  {...field}
                />
              )}
            />
          </Box>

          <Box sx={{ mb: 4 }}>
            <Controller
              name='unite_price'
              control={control}
              render={({ field }) => (
                <CustomTextField
                  fullWidth
                  type='number'
                  label='Prix unitaire'
                  {...field}
                  error={Boolean(errors.unite_price)}
                  helperText={errors.unite_price?.message}
                />
              )}
            />
          </Box>

          <FormButtons
            isLoading={isLoading}
            mode={mode}
            cancelPath='/services/pricing-benne'
            onSave={handleSubmit(handleCreate)}
            onSaveAndEdit={handleSubmit(handleCreateAndUpdate)}
            onSaveAndAddNew={handleSubmit(handleCreateAndSave)}
          />
        </form>
      </CardContent>
    </Card>
  )
}
