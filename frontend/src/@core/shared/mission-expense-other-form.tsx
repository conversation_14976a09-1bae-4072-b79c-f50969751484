import { useState, useEffect } from 'react'
import { useRouter } from 'next/router'
import { useForm, useFieldA<PERSON>y, Controller } from 'react-hook-form'
import { yupResolver } from '@hookform/resolvers/yup'
import {
  Box,
  Button,
  Typography,
  Card,
  CardContent,
  Grid,
  FormControl,
  FormLabel,
  IconButton,
  MenuItem,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Divider,
  Alert,
  Radio,
  RadioGroup,
  FormControlLabel,
  CardHeader
} from '@mui/material'
import Icon from 'src/@core/components/icon'
import CustomTextField from 'src/@core/components/mui/text-field'
import FormButtons from 'src/@core/components/form-buttons'
import DatePicker from 'react-datepicker'
import DatePickerWrapper from 'src/@core/styles/libs/react-datepicker'
import CustomInput from 'src/views/forms/form-elements/pickers/PickersCustomInput'
import { useInfiniteInvoiceItems } from 'src/hooks/_services/useInvoiceItems'
import GenericMultiSelect from './components/multiselect-input'
import { missionExpenseOthersSchema } from 'src/lib/schemas'
import { MissionExpenseOtherFormData, MissionExpensesOthers } from 'src/types/models/mission-expense-other'
import {
  useCreateMissionExpenseOther,
  useDeleteMissionExpenseOtherLine,
  useUpdateMissionExpenseOther
} from 'src/hooks/useMissionExpenseOthers'
import toast from 'react-hot-toast'
import { useTranslation } from 'react-i18next'
import ConfirmationModal from '../components/modals/ConfirmationModal'
import { useQueryClient } from '@tanstack/react-query'
import { IsValidator } from './components/is-validator'

interface MissionExpenseOtherFormProps {
  initialData?: MissionExpensesOthers
  mode?: 'create' | 'edit'
  missionExpenseOtherId?: string
  documentNumber?: string
  onSuccess?: () => void
}

export default function MissionExpenseOtherForm({
  initialData,
  mode = 'create',
  missionExpenseOtherId,
  onSuccess
}: MissionExpenseOtherFormProps) {
  const router = useRouter()
  const [totalAmount, setTotalAmount] = useState(0)
  const [submitError, setSubmitError] = useState<string | null>(null)
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [rowToDelete, setRowToDelete] = useState<string | null>(null)
  const queryClient = useQueryClient()
  const { t } = useTranslation()

  const {
    mutateAsync: createMissionExpenseOther,
    isPending: isCreating,
    data: missionExpenseOtherData
  } = useCreateMissionExpenseOther({
    onSuccess: () => {
      toast.success(t('forms.missionExpenseOtherForm.messages.create_success'))
      queryClient.invalidateQueries({ queryKey: ['mission-expenses-other'] })
    },
    onError: () => {
      toast.error(t('forms.missionExpenseOtherForm.messages.create_error'))
    }
  })

  const { mutateAsync: updateMissionExpenseOther, isPending: isUpdating } = useUpdateMissionExpenseOther({
    onSuccess: () => {
      toast.success(t('forms.missionExpenseOtherForm.messages.update_success'))
      queryClient.invalidateQueries({ queryKey: ['mission-expenses-other'] })
    },
    onError: () => {
      toast.error(t('forms.missionExpenseOtherForm.messages.update_error'))
    }
  })

  const { mutateAsync: deleteMissionExpenseOther } = useDeleteMissionExpenseOtherLine({
    onSuccess() {
      setDeleteDialogOpen(false)
      setRowToDelete(null)
      queryClient.invalidateQueries({ queryKey: ['mission-expenses-other'] })
      toast.success(t('forms.missionExpenseOtherForm.messages.delete_success'))
    }
  })

  const {
    data: operationTypes,
    fetchNextPage: fetchNextPageOperations,
    hasNextPage: hasMoreOperations,
    isFetching: isLoadingMoreOperations
  } = useInfiniteInvoiceItems({
    limit: 100,
    offset: 0
  })

  const {
    control,
    handleSubmit,
    formState: { errors },
    watch,
    reset
  } = useForm<MissionExpenseOtherFormData>({
    resolver: yupResolver(missionExpenseOthersSchema),
    defaultValues: {
      priority: 'Normal',
      expenseLines: [],
      validation: 'EN ATTENTE',
      validationReason: ''
    }
  })

  const { fields, append, remove } = useFieldArray({
    control,
    name: 'expenseLines'
  })

  const expenseLines = watch('expenseLines')

  useEffect(() => {
    if (initialData) {
      reset({
        expenseLines: initialData.mission_other_lines.map((line: any) => ({
          _id: line.id,
          designation: line.description,
          date: line.getting_date,
          duration: line.duration,
          price: line.fees,
          quantity: line.qty,
          amount: line.amount,
          operations: line.operations
        })),
        id: initialData.id,
        priority: initialData.priority,
        validation: 'EN ATTENTE',
        validationReason: ''
      })

      setTotalAmount(+initialData.total_amount)
    }
  }, [initialData, reset])

  useEffect(() => {
    if (expenseLines && expenseLines.length > 0) {
      const total = expenseLines.reduce((sum, line) => {
        const price = parseFloat(line.price?.toString() || '0')
        const quantity = parseFloat(line.quantity?.toString() || '0')
        const lineTotal = price * quantity

        return sum + (isNaN(lineTotal) ? 0 : lineTotal)
      }, 0)

      setTotalAmount(total)
    } else {
      setTotalAmount(0)
    }
  }, [expenseLines])

  const handleAddExpenseLine = () => {
    append({
      designation: '',
      date: '',
      duration: '',
      price: 0,
      quantity: 0,
      amount: 0,
      operations: []
    })
  }

  const hadleRemove = (id: string, index: number) => {
    if (mode === 'edit' && id) {
      setRowToDelete(id)

      setDeleteDialogOpen(true)
    }

    remove(index)
  }

  const handleSaveAndReturn = async (data: any) => {
    if (mode === 'create') await createMissionExpenseOther(data)
    if (mode === 'edit')
      await updateMissionExpenseOther({
        id: missionExpenseOtherId!,
        data
      })

    onSuccess?.()
    router.push('/financial/mission-expenses-others')
  }

  const handleSaveAndEdit = async (data: any) => {
    if (mode === 'create') {
      await createMissionExpenseOther(data)
      router.push(`/financial/mission-expenses-others/${missionExpenseOtherData?.id}/edit`)
    }
    if (mode === 'edit')
      await updateMissionExpenseOther({
        id: missionExpenseOtherId!,
        data
      })
    router.push(`/financial/mission-expenses-others/${missionExpenseOtherId}/edit`)
  }

  const handleSaveAndAddNew = async (data: any) => {
    if (mode === 'create') await createMissionExpenseOther(data)
    if (mode === 'edit')
      await updateMissionExpenseOther({
        id: missionExpenseOtherId!,
        data
      })
    reset()
  }

  const handleDeleteConfirm = async () => {
    if (mode === 'edit' && rowToDelete) await deleteMissionExpenseOther({ id: rowToDelete })
  }

  const handleDeleteCancel = () => {
    setDeleteDialogOpen(false)
  }

  return (
    <Card>
      <CardHeader title={initialData ? initialData.code : t(`forms.missionExpenseOtherForm.create_title`)} />
      <CardContent>
        <Box component='form' sx={{ mt: 4 }}>
          {submitError && (
            <Alert severity='error' sx={{ mb: 4 }}>
              {submitError}
            </Alert>
          )}

          <Grid container spacing={4}>
            <Grid item xs={12} md={4}>
              <FormControl fullWidth>
                <FormLabel>{t('forms.missionExpenseOtherForm.fields.priority')}</FormLabel>
                <Controller
                  name='priority'
                  control={control}
                  render={({ field }) => (
                    <CustomTextField
                      {...field}
                      select
                      fullWidth
                      size='small'
                      error={!!errors.priority}
                      helperText={errors.priority?.message}
                    >
                      <MenuItem value='Normal'>{t('forms.missionExpenseOtherForm.priority.normal')}</MenuItem>
                      <MenuItem value='Urgent'>{t('forms.missionExpenseOtherForm.priority.urgent')}</MenuItem>
                      <MenuItem value='Courant'>{t('forms.missionExpenseOtherForm.priority.current')}</MenuItem>
                      <MenuItem value='Important'>{t('forms.missionExpenseOtherForm.priority.important')}</MenuItem>
                    </CustomTextField>
                  )}
                />
              </FormControl>
            </Grid>

            <Grid item xs={12} md={4}>
              <FormControl fullWidth>
                <FormLabel>{t('forms.missionExpenseOtherForm.fields.totalAmount')}</FormLabel>
                <Typography variant='body1' sx={{ fontWeight: 600 }}>
                  {totalAmount} FCFA
                </Typography>
              </FormControl>
            </Grid>
          </Grid>

          <Box sx={{ mb: 4, mt: 4 }}>
            <Typography variant='h6'>{t('forms.missionExpenseOtherForm.lines_title')}</Typography>
          </Box>
          <Divider sx={{ my: 4 }} />

          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 4 }}>
            {fields.map((field, index) => (
              <Card key={field.id} sx={{ p: 4 }}>
                <Grid container spacing={4}>
                  {/* Operations Select */}
                  <Grid item xs={12} md={4}>
                    <Controller
                      name={`expenseLines.${index}.operations`}
                      control={control}
                      render={({ field: { onChange, value } }) => (
                        <GenericMultiSelect
                          options={operationTypes?.pages.flatMap(page => page.results || []) || []}
                          value={value || []}
                          getOptionLabel={type => `${type.order_code}: BE ${type.be_number}`}
                          isOptionEqualToValue={(option, value) => option.id === value.id}
                          onChange={onChange}
                          loading={isLoadingMoreOperations}
                          hasMore={!!hasMoreOperations}
                          loadMore={() => fetchNextPageOperations()}
                        />
                      )}
                    />
                  </Grid>

                  {/* Designation */}
                  <Grid item xs={12} md={4}>
                    <Controller
                      name={`expenseLines.${index}.designation`}
                      control={control}
                      render={({ field }) => (
                        <CustomTextField
                          {...field}
                          fullWidth
                          size='small'
                          error={!!errors.expenseLines?.[index]?.designation}
                          helperText={errors.expenseLines?.[index]?.designation?.message}
                        />
                      )}
                    />
                  </Grid>

                  {/* Date */}
                  <Grid item xs={12} md={4}>
                    <Controller
                      name={`expenseLines.${index}.date`}
                      control={control}
                      render={({ field }) => (
                        <DatePickerWrapper>
                          <DatePicker
                            selected={field.value ? new Date(field.value) : null}
                            onChange={(date: Date | null) => field.onChange(date)}
                            dateFormat='dd/MM/yyyy'
                            customInput={<CustomInput fullWidth />}
                          />
                        </DatePickerWrapper>
                      )}
                    />
                  </Grid>

                  {/* Duration */}
                  <Grid item xs={12} md={4}>
                    <Controller
                      name={`expenseLines.${index}.duration`}
                      control={control}
                      render={({ field }) => (
                        <CustomTextField {...field} fullWidth size='small' type='number' label='Duration' />
                      )}
                    />
                  </Grid>

                  {/* Price */}
                  <Grid item xs={12} md={4}>
                    <Controller
                      name={`expenseLines.${index}.price`}
                      control={control}
                      render={({ field }) => (
                        <CustomTextField
                          {...field}
                          type='number'
                          fullWidth
                          size='small'
                          label='Price'
                          error={!!errors.expenseLines?.[index]?.price}
                          helperText={errors.expenseLines?.[index]?.price?.message}
                        />
                      )}
                    />
                  </Grid>

                  {/* Quantity */}
                  <Grid item xs={12} md={4}>
                    <Controller
                      name={`expenseLines.${index}.quantity`}
                      control={control}
                      render={({ field }) => (
                        <CustomTextField
                          {...field}
                          type='number'
                          fullWidth
                          size='small'
                          label='Quantity'
                          error={!!errors.expenseLines?.[index]?.quantity}
                          helperText={errors.expenseLines?.[index]?.quantity?.message}
                          inputProps={{ min: 0 }}
                        />
                      )}
                    />
                  </Grid>

                  {/* Total */}
                  <Grid item xs={12} md={4}>
                    <Typography variant='body1' sx={{ mt: 2 }}>
                      Total:{' '}
                      {(watch(`expenseLines.${index}.price`) || 0) * (watch(`expenseLines.${index}.quantity`) || 0)}
                    </Typography>
                  </Grid>

                  {/* Delete Button */}
                  <Grid item xs={12} md={4} sx={{ display: 'flex', justifyContent: 'flex-end' }}>
                    <IconButton color='error' onClick={() => hadleRemove(field._id!, index)}>
                      <Icon icon='tabler:trash' />
                    </IconButton>
                  </Grid>
                </Grid>
              </Card>
            ))}
          </Box>

          <Box sx={{ mb: 4 }}>
            <Button
              variant='contained'
              size='small'
              onClick={handleAddExpenseLine}
              startIcon={<Icon icon='tabler:plus' />}
            >
              {t('forms.missionExpenseOtherForm.buttons.add_line')}
            </Button>
          </Box>

          {/* Form Actions */}
          <FormButtons
            isLoading={isCreating || isUpdating}
            mode={mode}
            cancelPath='/financial/mission-expenses-other'
            onSave={handleSubmit(handleSaveAndReturn)}
            onSaveAndEdit={handleSubmit(handleSaveAndEdit)}
            onSaveAndAddNew={handleSubmit(handleSaveAndAddNew)}
          />
        </Box>

        <IsValidator>
          <Box sx={{ mt: 4 }}>
            <FormControl component='fieldset'>
              <FormLabel component='legend'>{t('forms.missionExpenseOtherForm.fields.validation')}</FormLabel>
              <Controller
                name='validation'
                control={control}
                render={({ field }) => (
                  <RadioGroup {...field} row>
                    <FormControlLabel
                      value='EN ATTENTE'
                      control={<Radio />}
                      label={t('forms.missionExpenseOtherForm.validation.status.pending')}
                    />
                    <FormControlLabel
                      value='VALIDEE'
                      control={<Radio />}
                      label={t('forms.missionExpenseOtherForm.validation.status.validated')}
                    />
                    <FormControlLabel
                      value='REJETEE'
                      control={<Radio />}
                      label={t('forms.missionExpenseOtherForm.validation.status.rejected')}
                    />
                  </RadioGroup>
                )}
              />
            </FormControl>
          </Box>

          <Box sx={{ mt: 2 }}>
            <Controller
              name='validationReason'
              control={control}
              render={({ field }) => (
                <CustomTextField
                  {...field}
                  label={t('forms.missionExpenseOtherForm.fields.validationReason')}
                  multiline
                  rows={4}
                  fullWidth
                  disabled={watch('validation') === 'EN ATTENTE'}
                />
              )}
            />
          </Box>
        </IsValidator>
      </CardContent>

      {mode === 'edit' && (
        <ConfirmationModal
          open={deleteDialogOpen}
          title={t('forms.missionExpenseOtherForm.delete_title')}
          message={t('forms.missionExpenseOtherForm.delete_message')}
          onConfirm={handleDeleteConfirm}
          onCancel={handleDeleteCancel}
        />
      )}
    </Card>
  )
}
