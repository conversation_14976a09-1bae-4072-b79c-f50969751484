import React, { useMemo, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { useDeleteSelectedGroupArticles, useGroupArticles } from 'src/hooks/_services/useGroupArticles'
import { BaseFetchParams } from 'src/types/models'
import { ColDef } from 'ag-grid-community'
import BaseTable from 'src/@core/components/tables/BaseTable'
import { useRouter } from 'next/router'
import TableActions from 'src/@core/components/tables/TableActions'
import moment from 'moment'
import toast from 'react-hot-toast'
import CustomDropdown from 'src/@core/shared/components/custom-dropdown'
import { Can } from 'src/layouts/components/acl/Can'
import { MODELS, MODULES } from 'src/layouts/components/acl/can'

export default function ArticleGroupTable() {
  const [queryParams, setQueryParams] = useState<Partial<BaseFetchParams>>({
    limit: 100,
    offset: 0
  })

  const [selectedRows, setSelectedRows] = useState<any[]>([])
  const router = useRouter()
  const { t } = useTranslation()
  const { data, isLoading, isError, isPending } = useGroupArticles(queryParams)

  const { mutateAsync: deleteSelectedGroupArticles } = useDeleteSelectedGroupArticles({
    onSuccess: () => {
      toast.success(t('tables.articleGroup.deleteSuccess'))
    },
    onError: error => {
      toast.error(t('tables.articleGroup.deleteError'))
      console.error(error)
    }
  })

  console.log()

  const columnDefs = useMemo<ColDef[]>(
    () => [
      {
        headerCheckboxSelection: true,
        checkboxSelection: true,
        width: 50,
        pinned: 'left'
      },
      {
        headerName: t('tables.articleGroup.columns.code') as string,
        field: 'id'
      },
      {
        headerName: t('tables.articleGroup.columns.name') as string,
        field: 'name'
      },

      {
        headerName: t('tables.articleGroup.columns.modifiedBy') as string,
        field: 'modified_by',
        valueFormatter: ({ value }) => (value ? `${value.first_name} ${value.last_name}` : '---')
      },
      {
        headerName: t('tables.articleGroup.columns.modifiedAt') as string,
        field: 'modified_at',
        valueFormatter: ({ value }) => (value ? moment(value).format('LL') : '---')
      },
      {
        headerName: t('common.actions.action') as string,
        cellRenderer: (params: any) => {
          return (
            <TableActions
              onDelete={handleDeleteClick}
              module='services'
              model='articlegroup'
              viewAction={false}
              id={params.data.id}
              basePath={'/services/article-groups'}
            />
          )
        }
      }
    ],
    [t]
  )

  const handleSelectionChanged = (selectedRows: any[]) => {
    setSelectedRows(selectedRows)
  }

  const handleDeleteClick = (id: string) => {
    deleteSelectedGroupArticles([id])
  }

  const handleDeleteSelected = async () => {
    const selectedIds = selectedRows.map(row => row.id)
    try {
      await deleteSelectedGroupArticles(selectedIds)
    } catch (error) {
      // Error is handled in the mutation's onError callback
    }
  }

  const customActions = () => {
    return (
      <Can action={'delete'} model={MODELS[MODULES.SERVICES].GROUP_ARTICLE} module={MODULES.SERVICES}>
        <CustomDropdown
          placeholder={t('common.actions.action') as string}
          buttonVariant='contained'
          buttonColor='primary'
          options={[
            {
              label: t('common.actions.deleteSelected'),
              icon: 'mdi:delete',
              onClick: handleDeleteSelected,
              disabled: selectedRows.length === 0
            }
          ]}
        />
      </Can>
    )
  }

  return (
    <BaseTable
      data={data}
      module='services'
      model='articlegroup'
      isLoading={isLoading}
      isPending={isPending}
      isError={isError}
      error={isError}
      queryParams={queryParams}
      setQueryParams={setQueryParams}
      createButtonText={t('tables.articleGroup.createButton') as string}
      onCreateClick={() => router.push('/services/article-groups/create')}
      columnDefs={columnDefs}
      defaultColDef={{
        sortable: true,
        resizable: true,
        filter: false
      }}
      title={t('tables.articleGroup.title') as string}
      searchPlaceholder={t('tables.articleGroup.searchPlaceholder') as string}
      onSelectionChanged={handleSelectionChanged}
      renderCustomActions={customActions}
      rowSelection='multiple'
    />
  )
}
