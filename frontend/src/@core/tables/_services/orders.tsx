import React, { useMemo, useState } from 'react'
import { useRouter } from 'next/router'
import { ColDef } from 'ag-grid-community'
import { useDeleteOrder, useOrders, useStartMissingTrip } from 'src/hooks/_services/useOrders'
import { OrderParams } from 'src/types/models/_services/orders'
import moment from 'moment'
import { Icon } from '@iconify/react'
import BaseTable from 'src/@core/components/tables/BaseTable'
import { useTranslation } from 'react-i18next'
import { Button, FormControl, InputLabel, MenuItem, Select, SelectChangeEvent } from '@mui/material'
import TableActions from 'src/@core/components/tables/TableActions'
import toast from 'react-hot-toast'
import { useSelector } from 'react-redux'
import { selectUser } from 'src/store/auth'

export default function OrdersTable() {
  const { t } = useTranslation()
  const user = useSelector(selectUser)
  const [params, setParams] = useState<Partial<OrderParams>>({
    offset: 0,
    limit: 100,
    prestation: user?.type_of_operation
  })

  const currentUserPrestation = user?.type_of_operation
  const [prestationFilter, setPrestationFilter] = useState('all')
  const [validationFilter, setValidationFilter] = useState('all')
  const [selectedRows, setSelectedRows] = useState<any[]>([])
  const { data, isLoading, isPending, isError, error } = useOrders(params)
  const { mutate: addMissingTrips, isPending: addingMissingTrips } = useStartMissingTrip({
    onSuccess: () => {
      toast.success('Added missing trips with success')
    },
    onError: error => {
      toast.error(error.message)
      console.error(error)
    }
  })
  const router = useRouter()
  const { mutate: deleteOrder } = useDeleteOrder({
    onSuccess: () => {
      toast.success('Order deleted successfully')
    },
    onError: error => {
      toast.error('Error deleting order')
      console.error(error)
    }
  })

  const columnDefs: ColDef[] = useMemo(
    () => [
      {
        headerCheckboxSelection: true,
        checkboxSelection: true,
        width: 50,
        pinned: 'left'
      },
      {
        headerName: t('tables.orders.columns.code', 'N° Document') as string,
        field: 'code',
        minWidth: 130,
        cellStyle: {
          fontWeight: 'bold'
        }
      },
      {
        headerName: t('tables.orders.columns.customer', 'Client') as string,
        field: 'customer',
        minWidth: 150,
        valueFormatter: ({ value }) => {
          return value.name
        }
      },
      {
        headerName: t('tables.orders.columns.prestation', 'Prestation') as string,
        field: 'prestation',
        minWidth: 150,
        valueFormatter: ({ value }) => {
          return value || ''
        }
      },
      {
        headerName: t('tables.orders.columns.modifiedBy', 'Modified by') as string,
        field: 'modified_by',
        valueFormatter: ({ value }) => {
          return value ? value.first_name + ' ' + value.last_name : ''
        }
      },
      {
        headerName: t('tables.orders.columns.validate', 'Validate') as string,
        field: 'validate',
        cellRenderer: ({ value }: { value: boolean }) => {
          return (
            <Icon
              icon={value ? 'mdi:check-circle' : 'mdi:close-circle'}
              style={{
                color: value ? ' #56CA00' : '#FF4C51 ',
                fontSize: '20px'
              }}
            />
          )
        }
      },
      {
        headerName: t('tables.orders.columns.modifiedAt', 'Modified At') as string,
        field: 'modified_at',
        minWidth: 150,
        valueFormatter: ({ value }: { value: string }) => {
          return moment(value).format('ll')
        }
      },
      {
        headerName: t('common.actions.action') as string,
        field: 'actions',
        sortable: false,
        filter: false,
        width: 150,
        pinned: 'right',
        cellRenderer: (params: any) => (
          <TableActions
            id={params.data.id}
            basePath='/services/orders'
            onDelete={handleDeleteClick}
            deleteAction={params.data.validate ? false : true}
            viewAction={false}
            module='services'
            model='order'
          />
        )
      }
    ],
    [t]
  )

  function handleDeleteClick(id: string) {
    deleteOrder({ id })
  }

  const handlePrestationFilterChange = (event: SelectChangeEvent) => {
    const value = event.target.value
    setPrestationFilter(value)
    setParams(prev => ({
      ...prev,
      prestation: value === 'all' ? undefined : value
    }))
  }

  const onSelectionChanged = (selectedRows: any[]) => {
    setSelectedRows(selectedRows)
  }
  const handleAddMissingTrip = () => {
    const orderIds = selectedRows.map(row => row.id)
    addMissingTrips({ order_ids: orderIds })
  }

  const handleValidationFilterChange = (event: SelectChangeEvent) => {
    const value = event.target.value
    setValidationFilter(value)
    setParams(prev => ({
      ...prev,
      validate: value === 'all' ? undefined : value === 'true' ? true : false
    }))
  }

  const CustomFilters = () => (
    <>
      <FormControl sx={{ minWidth: 200 }} size='small'>
        <InputLabel id='prestation-filter-label'>{t('tables.orders.filters.prestation', 'Prestation')}</InputLabel>
        <Select
          labelId='prestation-filter-label'
          value={prestationFilter}
          defaultValue={currentUserPrestation}
          disabled={
            currentUserPrestation === 'CITERNE' ||
            currentUserPrestation === 'PLATEAU' ||
            currentUserPrestation === 'BENNE'
          }
          label={t('tables.orders.filters.prestation', 'Prestation')}
          onChange={handlePrestationFilterChange}
        >
          <MenuItem value='all'>{t('tables.orders.filters.all', 'Tous')}</MenuItem>
          <MenuItem value='CITERNE'>{t('tables.orders.filters.citerne', 'Citerne')}</MenuItem>
          <MenuItem value='PLATEAU'>{t('tables.orders.filters.plateau', 'Plateau')}</MenuItem>
          <MenuItem value='BENNE'>{t('tables.orders.filters.benne', 'Benne')}</MenuItem>
        </Select>
      </FormControl>
      <FormControl sx={{ minWidth: 200, ml: 2 }} size='small'>
        <InputLabel id='validation-filter-label'>{t('tables.orders.filters.validation', 'Validation')}</InputLabel>
        <Select
          labelId='validation-filter-label'
          value={validationFilter}
          label={t('tables.orders.filters.validation', 'Validation')}
          onChange={handleValidationFilterChange}
        >
          <MenuItem value='all'>{t('tables.orders.filters.all', 'Tous')}</MenuItem>
          <MenuItem value='true'>{t('tables.orders.filters.validated', 'Validé')}</MenuItem>
          <MenuItem value='false'>{t('tables.orders.filters.notValidated', 'Non validé')}</MenuItem>
        </Select>
      </FormControl>
      {selectedRows?.length > 0 && (
        <Button
          variant='contained'
          color='primary'
          onClick={handleAddMissingTrip}
          sx={{ ml: 2 }}
          disabled={addingMissingTrips}
        >
          {t('tables.orders.actions.missingTrip')}
        </Button>
      )}
    </>
  )

  return (
    <BaseTable
      model='order'
      module='services'
      data={data}
      isLoading={isLoading}
      isPending={isPending}
      isError={isError}
      error={error}
      queryParams={params}
      defaultColDef={{
        sortable: true,
        resizable: true,
        filter: false
      }}
      setQueryParams={setParams}
      columnDefs={columnDefs}
      onSelectionChanged={onSelectionChanged}
      rowSelection='multiple'
      onCreateClick={() => router.push('/services/orders/create')}
      createButtonText={t('tables.orders.createButton', 'Create Order') as string}
      title={t('tables.orders.title', 'Orders') as string}
      searchPlaceholder={t('tables.orders.searchPlaceholder', 'Enter order ID...') as string}
      searchField='id'
      customFilters={<CustomFilters />}
    />
  )
}
