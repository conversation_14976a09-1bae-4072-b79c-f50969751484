import React, { useMemo, useState } from 'react'
import { useRouter } from 'next/navigation'
import { useTranslation } from 'react-i18next'
import { useProducts } from 'src/hooks/_services/useProduct'
import { ProductParams } from 'src/types/models/_services/products'
import { ColDef } from 'ag-grid-community'
import BaseTable from 'src/@core/components/tables/BaseTable'
import moment from 'moment'
import TableActions from 'src/@core/components/tables/TableActions'
import { MODELS, MODULES } from 'src/layouts/components/acl/can'

export default function ProductTable() {
  const [queryParams, setQueryParams] = useState<Partial<ProductParams>>({
    limit: 100,
    offset: 0
  })
  const { data, isLoading, isError, error, isPending } = useProducts(queryParams)
  const router = useRouter()
  const { t } = useTranslation()

  const columnDefs = useMemo<ColDef[]>(
    () => [
      {
        headerName: t('tables.product.columns.label')!,
        field: 'name',
        flex: 1,
        cellStyle: {
          fontWeight: 'bold'
        }
      },
      {
        headerName: t('tables.product.columns.service')!,
        field: 'service_info',
        valueFormatter: ({ value }) => {
          return ` ${value.label} (${value.code})`
        },
        flex: 1
      },
      {
        headerName: t('tables.product.columns.modifiedBy')!,
        field: 'modified_by',
        flex: 1,
        valueFormatter: ({ value }) => {
          return `${value.first_name} ${value.last_name}`
        }
      },
      {
        headerName: t('tables.product.columns.modifiedAt')!,
        field: 'modified_at',
        flex: 1,
        valueFormatter: params => (params.value ? moment(params.value).format('LL') : '')
      },
      {
        headerName: t('common.actions.action')!,

        width: 150,
        cellRenderer: (params: any) => (
          <TableActions
            id={params.data.id}
            basePath='/services/products'
            editAction={true}
            deleteAction={false}
            viewAction={false}
            module='services'
            model='product'
          />
        )
      }
    ],
    []
  )

  const defaultColDef = useMemo(
    () => ({
      minWidth: 20,
      filter: false,
      sortable: true,
      resizable: true
    }),
    []
  )

  const handleCreateClick = () => {
    router.push('/services/products/create')
  }

  return (
    <BaseTable
      module={MODULES.SERVICES}
      model={MODELS[MODULES.SERVICES].PRODUCT}
      data={data}
      isLoading={isLoading}
      isPending={isPending}
      defaultColDef={defaultColDef}
      isError={isError}
      error={error}
      queryParams={queryParams}
      setQueryParams={setQueryParams}
      columnDefs={columnDefs}
      title={t('tables.product.title')!}
      searchPlaceholder={t('tables.product.searchPlaceholder')!}
      createButtonText={t('tables.product.createButton')!}
      onCreateClick={handleCreateClick}
      rowSelection='multiple'
    />
  )
}
