import React, { useState, useMemo } from 'react'
import { useRouter } from 'next/navigation'
import { useTranslation } from 'react-i18next'
import { useArticles, useDeleteArticle, useDeleteSelectedArticles } from 'src/hooks/_services/useArticles'
import { Article, FetchArticlesParams } from 'src/types/models/_services/article'
import { ColDef } from 'ag-grid-community'
import BaseTable from 'src/@core/components/tables/BaseTable'
import TableActions from 'src/@core/components/tables/TableActions'
import moment from 'moment'
import toast from 'react-hot-toast'
import CustomDropdown from 'src/@core/shared/components/custom-dropdown'
import { Can } from 'src/layouts/components/acl/Can'

export default function ArticlesTable() {
  const [queryParams, setQueryParams] = useState<Partial<FetchArticlesParams>>({
    limit: 100,
    offset: 0
  })
  const { data, isLoading, isError, error, isPending, refetch } = useArticles(queryParams)

  const router = useRouter()
  const { t } = useTranslation()

  const [selectedRows, setSelectedRows] = useState<any[]>([])

  const { mutateAsync: deleteArticle, isPending: deletingArticles } = useDeleteSelectedArticles({
    onSuccess: () => {
      toast.success(t('tables.articles.deleteSuccess'))
      refetch()
    },
    onError: error => {
      toast.error(t('tables.articles.deleteError'))
      console.error(error)
    }
  })

  const handleDeleteSelected = () => {
    const selectedIds = selectedRows.map(row => row.id)
    try {
      deleteArticle(selectedIds)
    } catch (error) {
      // Error is handled in the mutation's onError callback
    }
  }

  const customActions = () => {
    return (
      <Can action={'delete'} model={'article'} module={'services'}>
        <CustomDropdown
          placeholder={t('common.actions.action') as string}
          buttonVariant='contained'
          disabled={deletingArticles}
          buttonColor='primary'
          options={[
            {
              label: t('common.actions.deleteSelected'),
              icon: 'mdi:delete',
              onClick: handleDeleteSelected,
              disabled: selectedRows.length === 0
            }
          ]}
        />
      </Can>
    )
  }

  const handleDeleteClick = (id: string) => {
    deleteArticle([id])
  }

  const columnDefs = useMemo<ColDef[]>(
    () => [
      {
        headerCheckboxSelection: true,
        checkboxSelection: true,
        width: 50,
        pinned: 'left'
      },
      {
        headerName: t('tables.articles.columns.code') as string,
        field: 'code',
        minWidth: 130,
        cellStyle: {
          fontWeight: 'bold'
        }
      },
      {
        headerName: t('tables.articles.columns.label') as string,
        field: 'label',
        valueFormatter: ({ value }: { value: number }) => value?.toLocaleString(),
        minWidth: 130
      },
      {
        headerName: t('tables.articles.columns.operationType') as string,
        field: 'operation_type',
        width: 100,
        valueFormatter: ({ value }) => {
          return value ? value : '---'
        }
      },
      {
        headerName: t('tables.articles.columns.group') as string,
        field: 'group.name',
        minWidth: 150
      },
      {
        headerName: t('tables.articles.columns.groupCode') as string,
        field: 'group.id',
        minWidth: 180
      },
      {
        headerName: t('tables.articles.columns.measureUnit') as string,
        field: 'measure_unit',
        width: 100,
        valueFormatter: ({ value }) => {
          return value
            ? value == 'L'
              ? t('tables.articles.units.liters')
              : value == 'KG'
              ? t('tables.articles.units.kilograms')
              : value === 'U'
              ? t('tables.articles.units.units')
              : value == 'M3'
              ? t('tables.articles.units.cubicMeters')
              : value
            : '---'
        }
      },
      {
        headerName: t('tables.articles.columns.modifiedBy') as string,
        field: 'modified_by',
        minWidth: 200,
        valueFormatter: ({ value }) => {
          return value ? value.first_name + ' ' + value.last_name : '---'
        }
      },
      {
        headerName: t('tables.articles.columns.modifiedAt') as string,
        field: 'modified_at',
        minWidth: 150,
        valueFormatter: ({ value }: { value: string }) => {
          return moment(value).format('LL')
        }
      },
      {
        headerName: t('common.actions.action') as string,
        field: 'actions',
        sortable: false,
        filter: false,
        width: 150,
        cellRenderer: (params: any) => (
          <TableActions
            id={params.data.id}
            basePath='/services/articles'
            onDelete={handleDeleteClick}
            viewAction={false}
            module='services'
            model='article'
          />
        )
      }
    ],
    [t]
  )

  const defaultColDef = useMemo(
    () => ({
      minWidth: 20,
      filter: false,
      sortable: true,
      resizable: true
    }),
    []
  )

  const handleCreateClick = () => {
    router.push('/services/articles/create')
  }

  return (
    <>
      <BaseTable
        data={data}
        isLoading={isLoading}
        isPending={isPending}
        isError={isError}
        error={error}
        queryParams={queryParams}
        setQueryParams={setQueryParams}
        columnDefs={columnDefs}
        onSelectionChanged={setSelectedRows}
        renderCustomActions={customActions}
        module='services'
        model='article'
        onExportClick={api => {
          api.exportDataAsCsv({
            fileName: `articles-${new Date().toISOString()}.csv`
          })
        }}
        defaultColDef={defaultColDef}
        title={t('tables.articles.title') as string}
        searchPlaceholder={t('tables.articles.searchPlaceholder') as string}
        createButtonText={t('tables.articles.createButton') as string}
        onCreateClick={handleCreateClick}
        rowSelection='multiple'
      />
    </>
  )
}
