import React, { useMemo, useState } from 'react'
import { ColDef } from 'ag-grid-community'
import BaseTable from 'src/@core/components/tables/BaseTable'
import { useTranslation } from 'react-i18next'
import { useExtraction } from 'src/hooks/_services/useExtraction'
import ExtractionFilterDialog from 'src/@core/components/dialogs/ExtractionFilterDialog'
import { Button, Stack, Menu, MenuItem } from '@mui/material'
import { Icon } from '@iconify/react'
import moment from 'moment'
import { useCustomers } from 'src/hooks/useCustomer'

export default function ExtractionTable() {
  const [queryParams, setQueryParams] = useState<Partial<any>>({
    limit: 100,
    offset: 0
  })

  const [filterDialogOpen, setFilterDialogOpen] = useState(false)

  const { t } = useTranslation()

  const { data, isLoading, isError, error, isPending } = useExtraction(queryParams)
  const { data: customers } = useCustomers({
    limit: 100,
    offset: 1
  })

  const defaultColDef = useMemo(
    () => ({
      minWidth: 20,
      filter: false,
      sortable: true,
      resizable: true
    }),
    []
  )

  const columnDefs = useMemo<ColDef[]>(
    () => [
      {
        headerCheckboxSelection: true,
        checkboxSelection: true,
        width: 50,
        pinned: 'left'
      },
      {
        field: 'order',
        headerName: t('tables.extraction.columns.operation') as string,
        minWidth: 100,
        cellStyle: {
          fontWeight: 'bold'
        }
      },
      {
        field: 'travel_number',
        headerName: t('tables.extraction.columns.journey') as string,
        minWidth: 100,
        valueFormatter: ({ value }) => (value ? value : '---')
      },
      {
        field: 'prestation',
        headerName: t('tables.extraction.columns.service') as string,
        minWidth: 100
      },
      {
        field: 'driver',
        headerName: t('tables.extraction.columns.driver') as string,
        minWidth: 100
      },

      {
        field: 'path',
        headerName: t('tables.extraction.columns.route') as string,
        minWidth: 100
      },
      {
        field: 'tractor',
        headerName: t('tables.extraction.columns.tractor') as string,
        minWidth: 100,
        valueFormatter: ({ value }) => (value ? value : '---')
      },
      {
        field: 'trailer',
        headerName: t('tables.extraction.columns.trailer') as string,
        minWidth: 100,
        valueFormatter: ({ value }) => (value ? value : '---')
      },
      {
        field: 'product',
        headerName: t('tables.extraction.columns.products') as string,
        minWidth: 100
      },
      {
        field: 'nature_product',
        headerName: t('tables.extraction.columns.productType') as string,
        minWidth: 100,
        valueFormatter: ({ value }) => (value ? value : '---')
      },
      {
        field: 'customer',
        headerName: t('tables.extraction.columns.customer') as string,
        minWidth: 100,
        cellStyle: {
          fontWeight: 'bold'
        }
      },
      {
        field: 'price',
        headerName: t('tables.extraction.columns.pu') as string,
        minWidth: 100
      },
      {
        field: 'qty',
        headerName: t('tables.extraction.columns.quantity') as string,
        minWidth: 100
      },
      {
        field: 'qty_end',
        headerName: t('tables.extraction.columns.diliveryQuantity') as string,
        minWidth: 100
      },
      {
        field: 'qty',
        headerName: t('tables.extraction.columns.lossOnDilivery') as string,
        minWidth: 100,
        valueFormatter: ({ value, data }) => (value ? data.qty_end - value : '---')
      },
      {
        field: 'road_fees',
        headerName: t('tables.extraction.columns.tripprice') as string,
        minWidth: 100,
        valueFormatter: ({ value }) => (value ? `${value} FCFA` : '---')
      },
      {
        field: 'petrol_volume',
        headerName: t('tables.extraction.columns.fuelAmmount') as string,
        minWidth: 100
      },
      {
        field: 'charges',
        headerName: t('tables.extraction.columns.fuelCharges') as string,
        minWidth: 100,
        valueFormatter: ({ value }) => (value ? `${value} FCFA` : '---')
      },
      {
        field: 'turnover',
        headerName: t('tables.extraction.columns.ca') as string,
        minWidth: 100,
        valueFormatter: ({ value }) => (value ? `${value} FCFA` : '---')
      },

      {
        field: 'petrol_fee',
        headerName: t('tables.extraction.columns.fuelNeeded') as string,
        minWidth: 100,
        valueFormatter: ({ value }) => (value ? `${value} FCFA` : '---')
      },
      {
        field: 'charges_other',
        headerName: t('tables.extraction.columns.dafCharges') as string,
        minWidth: 100,
        valueFormatter: ({ value }) => (value ? `${value} FCFA` : '---')
      },
      {
        field: 'margin',
        headerName: t('tables.extraction.columns.grossMargin') as string,
        minWidth: 100
      },
      {
        field: 'is_mobilised',
        headerName: t('tables.extraction.columns.immobilisation') as string,
        minWidth: 100
      },
      {
        field: 'immobilization_duration',
        headerName: t('tables.extraction.columns.immobilisationDuration') as string,
        minWidth: 100,
        valueFormatter: ({ value }) => (value ? `${value}h` : '---')
      },
      {
        field: 'has_charged',
        headerName: t('tables.extraction.columns.roadFees') as string,
        minWidth: 100
      },

      {
        field: 'charged_date',
        headerName: t('tables.extraction.columns.otherCharges') as string,
        minWidth: 100,
        valueFormatter: ({ value }) => (value ? moment(value).format('LLL') : '-------')
      },

      {
        field: 'charged_at',
        headerName: t('tables.extraction.columns.otherChargesDate') as string,
        minWidth: 100,
        valueFormatter: ({ value }) => (value ? moment(value).format('LLL') : '-------')
      },
      {
        field: 'is_started',
        headerName: t('tables.extraction.columns.started') as string,
        minWidth: 100
      },
      {
        field: 'is_ended',
        headerName: t('tables.extraction.columns.ended') as string,
        minWidth: 100
      },
      {
        field: 'has_unloaded',
        headerName: t('tables.extraction.columns.unloaded') as string,
        minWidth: 100
      },
      {
        field: 'da_mission',
        headerName: t('tables.extraction.columns.missionExpense') as string,
        minWidth: 100,
        valueFormatter: ({ value }) => (value ? value : '------')
      },
      {
        field: 'da_mission_other',
        headerName: t('tables.extraction.columns.missionExpenseOther') as string,
        minWidth: 100,
        valueFormatter: ({ value }) => (value ? value : '------')
      },
      {
        field: 'unloaded_at',
        headerName: t('tables.extraction.columns.unloadedAt') as string,
        minWidth: 100,
        valueFormatter: ({ value }) => (value ? moment(value).format('LLL') : '-------')
      },
      {
        field: 'created_at',
        headerName: t('tables.extraction.columns.createdAt') as string,
        minWidth: 100,
        valueFormatter: ({ value }) => (value ? moment(value).format('LLL') : '-------')
      },

      {
        field: 'modified_by',
        headerName: t('tables.extraction.columns.modifiedBy') as string,
        minWidth: 100,
        valueFormatter: ({ value }) => (value ? value.first_name + ' ' + value.last_name : '-------')
      },
      {
        field: 'is_ended',
        headerName: t('tables.extraction.columns.finished') as string,
        minWidth: 100
      },

      {
        field: 'billed',
        headerName: t('tables.extraction.columns.invoice') as string,
        minWidth: 100
      },
      {
        field: 'agent',
        headerName: t('tables.extraction.columns.agent') as string,
        minWidth: 100
      }
    ],
    [t]
  )

  const handleFilterApply = (filters: any) => {
    setQueryParams(prev => ({
      ...prev,
      order__prestation: filters.order__prestation === 'all' ? undefined : filters.order__prestation,
      order__customer: filters.order__customer === 'all' ? undefined : filters.order__customer,
      billed: filters.billed === 'all' ? undefined : filters.billed,
      created_at_gte: filters.created_at_gte,
      created_at_lte: filters.created_at_lte
    }))
  }

  const renderCustomActions = () => {
    return (
      <Stack direction='row' spacing={2}>
        <Button variant='outlined' startIcon={<Icon icon='mdi:filter' />} onClick={() => setFilterDialogOpen(true)}>
          {t('tables.extraction.filters.title', 'Filtres')}
        </Button>

        {/* <Button variant='outlined' startIcon={<Icon icon='mdi:export' />} onClick={handleExportClick}>
          {t('tables.extraction.export', 'Exporter')}
        </Button> */}

        {/* <Menu
          anchorEl={anchorEl}
          open={Boolean(anchorEl)}
          onClose={handleExportClose}
          PaperProps={{
            sx: {
              minWidth: '120px'
            }
          }}
        >
          <MenuItem onClick={handleExportCSV}>CSV</MenuItem>
          <MenuItem onClick={handleExportExcel}>Excel</MenuItem>
        </Menu> */}

        <ExtractionFilterDialog
          open={filterDialogOpen}
          onClose={() => setFilterDialogOpen(false)}
          onApply={handleFilterApply}
          clients={customers?.results} // Add your clients data here
        />
      </Stack>
    )
  }

  return (
    <>
      <BaseTable
        columnDefs={columnDefs}
        defaultColDef={defaultColDef}
        renderCustomActions={renderCustomActions}
        data={data}
        isLoading={isLoading}
        model='extraction'
        module='services'
        isPending={isPending}
        isError={isError}
        onExportClick={api => {
          api.exportDataAsCsv({
            fileName: `extractions-${new Date().toISOString()}.csv`
          })
        }}
        error={error}
        title={t('tables.extraction.title') as string}
        searchPlaceholder={t('tables.extraction.searchPlaceholder') as string}
        queryParams={queryParams}
        setQueryParams={setQueryParams}
      />
    </>
  )
}
