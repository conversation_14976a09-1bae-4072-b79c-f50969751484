import React, { useMemo, useState } from 'react'
import { useTravels } from 'src/hooks/_services/useTravelmanagement'
import { TravelParams } from 'src/types/models/_services/travel'
import BaseTable from 'src/@core/components/tables/BaseTable'
import { ColDef } from 'ag-grid-community'
import { useRouter } from 'next/router'
import { useTranslation } from 'react-i18next'
import moment from 'moment'
import TravelActions from 'src/@core/components/travel/TravelActions'
import { Icon } from '@iconify/react'
import { Box, Chip, Button, Stack } from '@mui/material'
import TravelFilterDialog from 'src/@core/components/dialogs/TravelFilterDialog'
import TableActions from 'src/@core/components/tables/TableActions'
import { useDeleteSelectedTravels } from 'src/hooks/_services/useTravelmanagement'
import toast from 'react-hot-toast'
import CustomDropdown from 'src/@core/shared/components/custom-dropdown'
import { Can } from 'src/layouts/components/acl/Can'

const TravelManagementTable = () => {
  const { t } = useTranslation()
  const [filterDialogOpen, setFilterDialogOpen] = useState(false)
  const [selectedRow, setSelectedRow] = useState<any[]>([])
  const [queryParams, setQueryParams] = useState<Partial<TravelParams>>({
    limit: 100,
    offset: 0
  })
  const { data, isLoading, isError, error, isPending } = useTravels(queryParams)
  const router = useRouter()

  const { mutate: deleteSelectedTravels } = useDeleteSelectedTravels({
    onSuccess: () => {
      toast.success(t('tables.travel.deleteSuccess'))
    },
    onError: (error: any) => {
      toast.error(error.response.data.message)
    }
  })

  const handleFilterApply = (filters: any) => {
    setQueryParams(prev => ({
      ...prev,
      date_from: filters.dateFrom,
      date_to: filters.dateTo,
      prestation: filters.prestation === 'all' ? undefined : filters.prestation,
      is_started: filters.isStarted === 'all' ? undefined : filters.isStarted === true,
      is_ended: filters.isEnded === 'all' ? undefined : filters.isEnded === true,
      has_charged: filters.hasCharged === 'all' ? undefined : filters.hasCharged === true,
      has_unloaded: filters.hasUnloaded === 'all' ? undefined : filters.hasUnloaded === true
    }))
  }

  const columnDefs = useMemo(
    (): ColDef[] => [
      {
        headerCheckboxSelection: true,
        checkboxSelection: true,
        width: 50,
        pinned: 'left'
      },
      {
        headerName: t('tables.travel.columns.number') as string,
        field: 'code',
        minWidth: 160,
        flex: 1,
        tooltipField: 'code',
        cellStyle: {
          fontWeight: 'bold'
        }
      },
      {
        headerName: t('tables.travel.columns.loadedAt') as string,
        field: 'charged_date',
        minWidth: 200,
        flex: 1,
        valueFormatter: params => (params.value ? moment(params.value).format('LLL') : '---'),
        tooltipValueGetter: params => (params.value ? moment(params.value).format('LLL') : '---')
      },
      {
        headerName: t('tables.travel.columns.loading') as string,
        field: 'has_charged',
        minWidth: 100,
        flex: 1
      },
      {
        headerName: t('tables.travel.columns.loadingRecordedAt') as string,
        field: 'charged_at',
        minWidth: 180,
        flex: 1,
        valueFormatter: params => (params.value ? moment(params.value).format('LLL') : '---'),
        tooltipValueGetter: params => (params.value ? moment(params.value).format('LLL') : '---')
      },
      {
        headerName: t('tables.travel.columns.unloadedAt') as string,
        field: 'unloaded_date',
        minWidth: 180,
        flex: 1,
        valueFormatter: params => (params.value ? moment(params.value).format('LLL') : '---'),
        tooltipValueGetter: params => (params.value ? moment(params.value).format('LLL') : '---')
      },
      {
        headerName: t('tables.travel.columns.unloadingRecordedAt') as string,
        field: 'unloaded_at',
        minWidth: 180,
        flex: 1,
        valueFormatter: params => (params.value ? moment(params.value).format('LLL') : '---'),
        tooltipValueGetter: params => (params.value ? moment(params.value).format('LLL') : '---')
      },
      {
        headerName: t('tables.travel.columns.immobilization') as string,
        field: 'immo',
        minWidth: 180,
        flex: 1,
        cellStyle: {
          fontWeight: 'bold'
        },
        cellRenderer: (params: any) => {
          return params.value ? 'TRUE' : 'FALSE'
        }
      },
      {
        headerName: t('tables.travel.columns.immobilizationDuration') as string,
        field: 'immo_duration',
        minWidth: 150,
        flex: 1,
        tooltipField: 'immo_duration'
      },
      {
        headerName: t('tables.travel.columns.travel') as string,
        field: 'travel_type',
        minWidth: 130,
        flex: 1,
        tooltipField: 'travel_type'
      },
      {
        headerName: t('tables.travel.columns.order') as string,
        field: 'process.order.code',
        minWidth: 180,
        flex: 1,
        tooltipField: 'process.order.code'
      },
      {
        headerName: t('tables.travel.columns.operation') as string,
        field: 'process.code',
        minWidth: 250,
        flex: 1,
        valueGetter: params => {
          return `${params.data.process.order.code} - ${params.data.process.be_number}`
        },
        tooltipField: 'process.code '
      },
      {
        headerName: t('tables.travel.columns.route') as string,
        field: 'trajet',
        minWidth: 300,
        flex: 1,
        tooltipField: 'trajet'
      },
      {
        headerName: t('tables.travel.columns.truckDriver') as string,
        field: 'camion',
        minWidth: 500,
        flex: 1,
        tooltipField: 'camion'
      },
      {
        headerName: t('tables.travel.columns.duration') as string,
        field: 'duration',
        minWidth: 130,
        flex: 1,
        valueFormatter: params => `${params.value} ${t('common.hours')}`,
        tooltipValueGetter: params => `${params.value} ${t('common.hours')}`
      },
      {
        headerName: t('tables.travel.columns.status') as string,
        field: 'status',
        minWidth: 150,
        flex: 1,
        valueGetter: params => {
          if (params.data.is_canceled) return 'canceled'
          if (params.data.is_ended) return 'completed'
          if (params.data.has_unloaded) return 'unloaded'
          if (params.data.has_charged) return 'charged'
          if (params.data.is_started) return 'started'

          return 'pending'
        },
        cellRenderer: (params: any) => {
          const statusMap = {
            canceled: { color: 'error', label: t('common.status.canceled') },
            completed: { color: 'success', label: t('common.status.completed') },
            unloaded: { color: 'warning', label: t('common.status.unloaded') },
            charged: { color: 'info', label: t('common.status.charged') },
            started: { color: 'primary', label: t('common.status.started') },
            pending: { color: 'secondary', label: t('common.status.pending') }
          }
          const status = statusMap[params.value as keyof typeof statusMap]

          return <Chip size='small' color={status.color as any} label={status.label} />
        }
      },
      {
        headerName: t('tables.travel.columns.startedAt') as string,
        field: 'started_date',
        minWidth: 180,
        flex: 1,
        valueFormatter: params => (params.value ? moment(params.value).format('LLL') : '---'),
        tooltipValueGetter: params => (params.value ? moment(params.value).format('LLL') : '---')
      },
      {
        headerName: t('tables.travel.columns.startRecordedAt') as string,
        field: 'started_at',
        minWidth: 180,
        flex: 1,
        valueFormatter: params => (params.value ? moment(params.value).format('LLL') : '---'),
        tooltipValueGetter: params => (params.value ? moment(params.value).format('LLL') : '---')
      },
      {
        headerName: t('tables.travel.columns.estimatedDate') as string,
        field: 'date_estimee',
        minWidth: 180,
        flex: 1
      },
      {
        headerName: t('tables.travel.columns.delay') as string,
        field: 'retard',
        cellStyle: () => ({
          fontWeight: 'bold',
          textTransform: 'capitalize',
          fontStyle: 'italic',
          color: 'red'
        }),
        minWidth: 130,
        flex: 1,
        valueFormatter: params => `${params.value} ${t('common.hours')}`,
        tooltipValueGetter: params => `${params.value} ${t('common.hours')}`
      },
      {
        headerName: t('tables.travel.columns.endedAt') as string,
        field: 'ended_date',
        minWidth: 180,
        flex: 1,
        valueFormatter: params => (params.value ? moment(params.value).format('LLL') : '---'),
        tooltipValueGetter: params => (params.value ? moment(params.value).format('LLL') : '---')
      },
      {
        headerName: t('tables.travel.columns.endRecordedAt') as string,
        field: 'ended_at',
        minWidth: 180,
        flex: 1,
        valueFormatter: params => (params.value ? moment(params.value).format('LLL') : '---'),
        tooltipValueGetter: params => (params.value ? moment(params.value).format('LLL') : '---')
      },
      {
        headerName: t('tables.travel.columns.createdAt') as string,
        field: 'created_at',
        minWidth: 180,
        flex: 1,
        valueFormatter: params => (params.value ? moment(params.value).format('LLL') : '---'),
        tooltipValueGetter: params => (params.value ? moment(params.value).format('LLL') : '---')
      },
      {
        headerName: t('tables.travel.columns.createdBy') as string,
        field: 'created_by',
        minWidth: 150,
        flex: 1,
        valueFormatter: params => (params.value ? `${params.value.first_name} ${params.value.last_name}` : '---'),
        tooltipValueGetter: params => (params.value ? `${params.value.first_name} ${params.value.last_name}` : '---')
      },
      {
        headerName: t('tables.travel.columns.modifiedBy') as string,
        field: 'modified_by',
        minWidth: 150,
        flex: 1,
        valueFormatter: params => (params.value ? `${params.value.first_name} ${params.value.last_name}` : '---'),
        tooltipValueGetter: params => (params.value ? `${params.value.first_name} ${params.value.last_name}` : '---')
      },
      {
        headerName: t('tables.travel.columns.modifiedAt') as string,
        field: 'modified_at',
        minWidth: 180,
        flex: 1,
        valueFormatter: params => (params.value ? moment(params.value).format('LLL') : '---'),
        tooltipValueGetter: params => (params.value ? moment(params.value).format('LLL') : '---')
      },
      {
        headerName: t('common.actions.action') as string,
        field: 'actions',
        sortable: false,
        filter: false,
        pinned: 'right',
        width: 150,
        cellRenderer: (params: any) => (
          <TableActions
            id={params.data.id}
            basePath='/services/travel'
            editAction={true}
            onDelete={handleDeleteClick}
            viewAction={false}
            module='services'
            model='travel'
          />
        )
      }
    ],
    [t]
  )

  function handleDeleteClick(id: string) {
    deleteSelectedTravels([id])
  }

  const handleDeleteSelected = () => {
    const selectedIds = selectedRow.map(row => row.id)
    try {
      deleteSelectedTravels(selectedIds)
    } catch (error) {
      // Error is handled in the mutation's onError callback
    }
  }

  const renderCustomActions = () => {
    return (
      <Stack direction='row' spacing={2}>
        <Button variant='outlined' startIcon={<Icon icon='mdi:filter' />} onClick={() => setFilterDialogOpen(true)}>
          {t('tables.travel.filters.title')}
        </Button>
        {selectedRow && <TravelActions selectedRow={selectedRow} />}
        <TravelFilterDialog
          open={filterDialogOpen}
          onClose={() => setFilterDialogOpen(false)}
          onApply={handleFilterApply}
        />
        <Can action={'delete'} model={'travel'} module={'services'}>
          <CustomDropdown
            placeholder={t('common.actions.action') as string}
            buttonVariant='contained'
            buttonColor='primary'
            options={[
              {
                label: t('common.actions.deleteSelected') as string,
                icon: 'mdi:delete',
                onClick: handleDeleteSelected,
                disabled: selectedRow.length === 0
              }
            ]}
          />
        </Can>
      </Stack>
    )
  }

  return (
    <Box>
      <BaseTable
        data={data}
        isLoading={isLoading}
        isPending={isPending}
        isError={isError}
        module='services'
        model='travel'
        error={error}
        queryParams={queryParams}
        setQueryParams={setQueryParams}
        columnDefs={columnDefs}
        onCreateClick={() => router.push('/services/travel/create')}
        defaultColDef={{
          resizable: true,
          flex: 1,
          tooltipComponent: 'customTooltip',
          suppressMovable: true
        }}
        title={t('tables.travel.title') as string}
        onSelectionChanged={(rows: any) => {
          setSelectedRow(rows)
        }}
        rowSelection='single'
        searchPlaceholder={t('tables.travel.searchPlaceholder') as string}
        createButtonText={t('tables.travel.createButton') as string}
        renderCustomActions={renderCustomActions}
        onExportClick={api => {
          api.exportDataAsCsv({
            fileName: `travels-${new Date().toISOString()}.csv`
          })
        }}
      />
    </Box>
  )
}
export default TravelManagementTable
