import moment from 'moment'
import React, { useMemo, useState } from 'react'
import { ColDef } from 'ag-grid-community'
import BaseTable from 'src/@core/components/tables/BaseTable'
import { useDeleteSelectedImmobilizations, useImmobilizations } from 'src/hooks/_services/useImmobilization'
import { FetchImmobilizationParams } from 'src/types/models/_services/imobilization'
import { useRouter } from 'next/navigation'
import { MODELS, MODULES } from 'src/layouts/components/acl/can'
import toast from 'react-hot-toast'
import TableActions from 'src/@core/components/tables/TableActions'
import CustomDropdown from 'src/@core/shared/components/custom-dropdown'
import { Can } from 'src/layouts/components/acl/Can'
import { useTranslation } from 'react-i18next'

export default function ImmobilizationsTable() {
  const [queryParams, setQueryParams] = useState<Partial<FetchImmobilizationParams>>({
    limit: 100,
    offset: 0
  })

  const router = useRouter()

  const { data, isLoading, isError, error, isPending } = useImmobilizations(queryParams)
  const [selectedRows, setSelectedRows] = useState<any[]>([])
  const { t } = useTranslation()
  const { mutate: deleteSelectedImmobilizations } = useDeleteSelectedImmobilizations({
    onSuccess: () => {
      toast.success('Success in deleting immobilizations')
    },
    onError: error => {
      toast.error(error.message)
    }
  })

  const colDef = useMemo<ColDef[]>(
    () => [
      {
        headerCheckboxSelection: true,
        checkboxSelection: true,
        width: 50,
        pinned: 'left'
      },

      {
        field: 'type',
        headerName: 'Type',

        cellStyle: {
          fontWeight: 'bold',
          textTransform: 'capitalize'
        },
        flex: 1,
        tooltipField: 'type'
      },
      {
        field: 'comment',
        headerName: 'Commentaire',
        tooltipField: 'comment',
        valueFormatter: ({ value }: { value: string }) => {
          return value ? value : '-------'
        },

        flex: 1
      },
      {
        field: 'created_at',
        headerName: 'Date de création',
        valueFormatter: ({ value }: { value: string }) => {
          return moment(value).format('LL')
        },
        flex: 1
      },
      {
        field: 'actions',
        headerName: 'Actions',
        sortable: false,
        filter: false,
        width: 150,
        cellRenderer: (params: any) => (
          <TableActions
            id={params.data.id}
            basePath='/services/immobilizations'
            onDelete={handleDeleteClick}
            viewAction={false}
            module='services'
            model='immobilization'
          />
        )
      }
    ],
    []
  )

  function handleDeleteClick(id: string) {
    deleteSelectedImmobilizations([id])
  }

  const handleDeleteSelected = () => {
    const selectedIds = selectedRows.map(row => row.id)
    try {
      deleteSelectedImmobilizations(selectedIds)
    } catch (error) {
      // Error is handled in the mutation's onError callback
    }
  }

  const customActions = () => {
    return (
      <Can action={'delete'} model={MODELS[MODULES.SERVICES].IMMOBILIZATION} module={MODULES.SERVICES}>
        <CustomDropdown
          placeholder={t('common.actions.action') as string}
          buttonVariant='contained'
          buttonColor='primary'
          options={[
            {
              label: t('common.actions.deleteSelected'),
              icon: 'mdi:delete',
              onClick: handleDeleteSelected,
              disabled: selectedRows.length === 0
            }
          ]}
        />
      </Can>
    )
  }

  return (
    <BaseTable
      module={MODULES.SERVICES}
      model={MODELS[MODULES.SERVICES].IMMOBILIZATION}
      columnDefs={colDef}
      isLoading={isLoading}
      isPending={isPending}
      defaultColDef={{
        sortable: true,
        resizable: true,
        filter: false
      }}
      isError={isError}
      title='Immobilisations'
      searchPlaceholder='Rechercher une immobilisation...'
      createButtonText='Ajouter une immobilisation'
      rowSelection='multiple'
      onCreateClick={() => {
        router.push('/services/immobilizations/create')
      }}
      error={error}
      renderCustomActions={customActions}
      data={data}
      queryParams={queryParams}
      setQueryParams={setQueryParams}
    />
  )
}
