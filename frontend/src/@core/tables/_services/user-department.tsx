import { useTranslation } from 'react-i18next'
import { useDeleteSelectedUserDepartments, useUserDepartments } from 'src/hooks/_services/useUserDepartment'
import { useState, useMemo } from 'react'
import { useRouter } from 'next/router'
import TableActions from 'src/@core/components/tables/TableActions'
import { BaseFetchParams } from 'src/types/models'
import { ColDef } from 'ag-grid-community'
import BaseTable from 'src/@core/components/tables/BaseTable'
import { MODELS, MODULES } from 'src/layouts/components/acl/can'
import moment from 'moment'
import toast from 'react-hot-toast'
import CustomDropdown from 'src/@core/shared/components/custom-dropdown'
import { Can } from 'src/layouts/components/acl/Can'

export default function UserDepartmentTable() {
  const [queryParams, setQueryParams] = useState<Partial<BaseFetchParams>>({
    limit: 100,
    offset: 0
  })

  const [selectedRows, setSelectedRows] = useState<any[]>([])
  const router = useRouter()
  const { t } = useTranslation()
  const { data, isLoading, isError, isPending } = useUserDepartments(queryParams)

  const { mutateAsync: deleteUserDepartment, isPending: deletingUserDepartment } = useDeleteSelectedUserDepartments({
    onSuccess: () => {
      toast.success(t('tables.userDepartment.deleteSuccess'))
    },
    onError: error => {
      toast.error(t('tables.userDepartment.deleteError'))
      console.error(error)
    }
  })

  const columns = useMemo<ColDef[]>(
    () => [
      {
        headerCheckboxSelection: true,
        checkboxSelection: true,
        width: 50,
        pinned: 'left'
      },

      {
        field: 'name',
        headerName: t('tables.userDepartment.columns.name') as string,
        flex: 1
      },
      {
        field: 'created_by',
        headerName: t('tables.userDepartment.columns.createdBy') as string,
        flex: 1,
        valueFormatter: params => {
          return params.value ? `${params.value.first_name} ${params.value.last_name}` : '---'
        }
      },
      {
        field: 'modified_by',
        headerName: t('tables.userDepartment.columns.modifiedBy') as string,
        flex: 1,
        valueFormatter: params => {
          return params.value ? `${params.value.first_name} ${params.value.last_name}` : '---'
        }
      },
      {
        field: 'created_at',
        headerName: t('tables.userDepartment.columns.createdAt') as string,
        flex: 1,
        valueFormatter: params => {
          return params.value ? moment(params.value).format('LL') : '---'
        }
      },

      {
        field: 'actions',
        headerName: t('common.actions.action') as string,
        flex: 1,
        cellRenderer: (params: any) => (
          <TableActions
            id={params.data.id}
            basePath='/user-management/departments'
            onDelete={handleDeleteClick}
            transLationKey='userDepartment'
            module={MODULES.APP_AUTHENTICATION}
            model={MODELS[MODULES.APP_AUTHENTICATION].USER_DEPARTMENT}
          />
        )
      }
    ],
    [t]
  )

  const handleDeleteClick = async (id: string) => {
    try {
      await deleteUserDepartment([id])
    } catch (error) {
      // Error is handled in the mutation's onError callback
    }
  }

  const handleDeleteSelected = () => {
    const selectedIds = selectedRows.map(row => row.id)
    try {
      deleteUserDepartment(selectedIds)
    } catch (error) {
      // Error is handled in the mutation's onError callback
    }
  }

  const customActions = () => {
    return (
      <Can
        action={'delete'}
        model={MODELS[MODULES.APP_AUTHENTICATION].USER_DEPARTMENT}
        module={MODULES.APP_AUTHENTICATION}
      >
        <CustomDropdown
          placeholder={t('common.actions.action') as string}
          buttonVariant='contained'
          buttonColor='primary'
          options={[
            {
              label: t('tables.userDepartment.deleteSelected'),
              icon: 'mdi:delete',
              onClick: handleDeleteSelected,
              disabled: selectedRows.length === 0
            }
          ]}
        />
      </Can>
    )
  }

  return (
    <BaseTable
      data={data}
      module={MODULES.APP_AUTHENTICATION}
      model={MODELS[MODULES.APP_AUTHENTICATION].USER_DEPARTMENT}
      isLoading={isLoading}
      isPending={isPending}
      isError={isError}
      error={isError}
      queryParams={queryParams}
      setQueryParams={setQueryParams}
      createButtonText={t('tables.userDepartment.createButton') as string}
      columnDefs={columns}
      onSelectionChanged={setSelectedRows}
      renderCustomActions={customActions}
      defaultColDef={{
        resizable: true,
        flex: 1,
        tooltipComponent: 'customTooltip',
        suppressMovable: true
      }}
      title={t('tables.userDepartment.title') as string}
      searchPlaceholder={t('tables.userDepartment.searchPlaceholder') as string}
      onCreateClick={() => router.push('/user-management/departments/create')}
    />
  )
}
