import React, { useMemo, useState } from 'react'
import { useInvoiceItems } from 'src/hooks/_services/useInvoiceItems'
import { FetchInvoiceItemsParams, InvoiceItemData } from 'src/types/models/_services/invoice-items'
import { ColDef } from 'ag-grid-community'
import moment from 'moment'
import { useRouter } from 'next/navigation'
import { Icon } from '@iconify/react'
import { IconButton, Box, Chip } from '@mui/material'
import Menu from '@mui/material/Menu'
import { Button, MenuItem } from '@mui/material'
import { useTranslation } from 'react-i18next'
import BaseTable from 'src/@core/components/tables/BaseTable'
import { useCreateInvoice } from 'src/hooks/_services/useInvoiceItems'
import toast from 'react-hot-toast'
import { useQueryClient } from '@tanstack/react-query'

import OperationFilterDialog from 'src/@core/components/dialogs/OperationFilter'
import { Can } from 'src/layouts/components/acl/Can'

export default function InvoiceItemsTable() {
  const queryClient = useQueryClient()
  const [queryParams, setQueryParams] = useState<Partial<FetchInvoiceItemsParams>>({
    limit: 100,
    offset: 0
  })
  const { mutate: createInvoice, isPending: isCreating } = useCreateInvoice({
    onSuccess: () => {
      toast.success('Invoice created successfully')
      queryClient.invalidateQueries({ queryKey: ['invoice-items'] })
    },
    onError: error => {
      toast.error('Error creating invoice')
    }
  })
  const { data, isError, isLoading, isPending, error } = useInvoiceItems(queryParams)
  const { t } = useTranslation()

  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null)
  const open = Boolean(anchorEl)

  const [openFilters, setOpenFilters] = useState(false)

  const [selectedRows, setSelectedRows] = useState<InvoiceItemData[]>([])

  const getValidationStatusStyle = (status: string): { color: 'success' | 'default'; style?: React.CSSProperties } => {
    switch (status?.toLowerCase()) {
      case 'validée':
      case 'validee':
        return {
          color: 'success',
          style: {
            backgroundColor: '#28C76F',
            color: '#fff'
          }
        }
      case 'en attente':
      case 'waiting':
        return {
          color: 'default',
          style: {
            backgroundColor: '#4B4B4B',
            color: '#fff'
          }
        }
      default:
        return {
          color: 'default'
        }
    }
  }

  const columnDefs = useMemo<ColDef[]>(
    () => [
      {
        headerCheckboxSelection: true,
        checkboxSelection: true,
        width: 50,
        pinned: 'left'
      },
      {
        field: 'order_code',
        headerName: t('tables.invoiceItems.columns.operation') as string,
        minWidth: 120,
        tooltipField: 'order_code',
        cellStyle: {
          fontWeight: 'bold'
        }
      },
      {
        field: 'completed',
        headerName: t('tables.invoiceItems.columns.completed') as string,
        width: 100,
        cellRenderer: (params: any) => (
          <Icon
            icon={params.value ? 'mdi:check-circle' : 'mdi:close-circle'}
            color={params.value ? '#4CAF50' : '#F44336'}
          />
        )
      },
      {
        field: 'validation',
        headerName: t('tables.invoiceItems.columns.status') as string,
        minWidth: 120,
        cellRenderer: (params: any) => {
          const status = params.value
          const { color, style } = getValidationStatusStyle(status)

          return (
            <Chip
              label={status}
              color={color}
              size='small'
              sx={{
                borderRadius: '4px',
                ...style,
                '& .MuiChip-label': {
                  textTransform: 'lowercase',
                  px: 2,
                  py: 0.5
                }
              }}
            />
          )
        }
      },
      {
        field: 'customer.name',
        headerName: t('tables.invoiceItems.columns.client') as string,
        minWidth: 150
      },
      {
        field: 'agence.name',
        headerName: t('tables.invoiceItems.columns.clientAgency') as string,
        minWidth: 150
      },
      {
        field: 'billed',
        headerName: t('tables.invoiceItems.columns.billed') as string,
        width: 100,
        cellRenderer: (params: any) => (
          <Icon
            icon={params.value ? 'mdi:check-circle' : 'mdi:close-circle'}
            color={params.value ? '#4CAF50' : '#F44336'}
          />
        )
      },
      {
        field: 'driver',
        headerName: t('tables.invoiceItems.columns.driver') as string,
        minWidth: 150,
        valueFormatter: ({ value }) => {
          return value ? `${value.first_name} ${value.last_name}` : '---'
        }
      },
      {
        field: 'tractor.number_plate',
        headerName: t('tables.invoiceItems.columns.tractor') as string,
        minWidth: 150,
        valueFormatter: ({ value }) => {
          return value ? value.number_plate : '---'
        }
      },
      {
        field: 'trailer',
        headerName: t('tables.invoiceItems.columns.trailer') as string,
        minWidth: 120,
        valueFormatter: ({ value }) => {
          return value ? value.number_plate : '---'
        }
      },
      {
        field: 'path.name',
        headerName: t('tables.invoiceItems.columns.path') as string,
        minWidth: 400
      },
      {
        field: 'qty',
        headerName: t('tables.invoiceItems.columns.quantity') as string,
        minWidth: 120,
        valueFormatter: params => params.value?.toLocaleString()
      },
      {
        field: 'price',
        headerName: t('tables.invoiceItems.columns.unitPrice') as string,
        minWidth: 120,
        valueFormatter: params => params.value?.toLocaleString()
      },
      {
        field: 'amount_ht',
        headerName: t('tables.invoiceItems.columns.amount') as string,
        minWidth: 120,
        valueFormatter: params => params.value?.toLocaleString()
      },
      {
        field: 'road_fees',
        headerName: t('tables.invoiceItems.columns.totalCharges') as string,
        minWidth: 120,
        valueFormatter: params => params.value?.toLocaleString()
      },
      {
        field: 'amount_ht',
        headerName: t('tables.invoiceItems.columns.margin') as string,
        minWidth: 120,
        valueFormatter: params => params.value?.toLocaleString()
      },
      {
        field: 'modified_by',
        headerName: t('tables.invoiceItems.columns.modifiedBy') as string,
        minWidth: 150,
        valueFormatter: ({ value }) => {
          return value ? `${value.first_name} ${value.last_name}` : '---'
        }
      },
      {
        field: 'created_at',
        headerName: t('tables.invoiceItems.columns.date') as string,
        sortable: true,
        minWidth: 120,
        valueFormatter: ({ value }) => moment(value).format('LL')
      },
      {
        field: 'actions',
        headerName: t('tables.invoiceItems.columns.actions') as string,
        minWidth: 100,
        sortable: false,
        filter: false,
        cellRenderer: () => (
          <IconButton onClick={() => alert('Action clicked')}>
            <Icon icon='mdi:alert' style={{ fontSize: '20px', color: '#FFA500' }} />
          </IconButton>
        )
      }
    ],
    [t]
  )

  const defaultColDef = useMemo(
    () => ({
      sortable: false,
      filter: false,
      resizable: true
    }),
    []
  )

  const handleExportClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget)
  }

  const handleExportClose = () => {
    setAnchorEl(null)
  }

  const handleExportCSV = () => {
    // Export functionality will be handled through gridApi reference
    handleExportClose()
  }

  const handleExportExcel = () => {
    // Export functionality will be handled through gridApi reference
    handleExportClose()
  }

  const handleSelectionChanged = (selectedRows: InvoiceItemData[]) => {
    setSelectedRows(selectedRows)
  }

  const handleOpenFilters = () => {
    setOpenFilters(true)
  }

  const handleCloseFilters = () => {
    setOpenFilters(false)
  }

  const handleApplyFilters = () => {
    setQueryParams(prev => ({
      ...prev,
      offset: 0
    }))
    setOpenFilters(false)
  }

  const handleCreateInvoice = () => {
    const operationIds = selectedRows.map(row => row.id)

    createInvoice({ operation_ids: operationIds })
  }

  const renderCustomActions = () => {
    return (
      <Box sx={{ display: 'flex', gap: 2 }}>
        <Button variant='outlined' startIcon={<Icon icon='mdi:filter' />} onClick={handleOpenFilters}>
          {t('tables.invoiceItems.filter')}
        </Button>

        <Can action={'create'} model='services' module='invoiceitems'>
          {selectedRows.length > 0 && (
            <Button
              variant='contained'
              color='primary'
              disabled={isCreating}
              startIcon={<Icon icon='mdi:file-document-plus' />}
              onClick={handleCreateInvoice}
            >
              {t('tables.invoiceItems.createInvoive')}
            </Button>
          )}
        </Can>

        <Button variant='outlined' startIcon={<Icon icon='mdi:export' />} onClick={handleExportClick}>
          {t('tables.invoiceItems.export')}
        </Button>

        <Menu anchorEl={anchorEl} open={open} onClose={handleExportClose}>
          <MenuItem onClick={handleExportCSV}>CSV</MenuItem>
          <MenuItem onClick={handleExportExcel}>Excel</MenuItem>
        </Menu>
      </Box>
    )
  }

  return (
    <>
      <BaseTable
        data={data}
        isLoading={isLoading}
        model='services'
        module='invoiceitems'
        isPending={isPending}
        isError={isError}
        error={error}
        queryParams={queryParams}
        setQueryParams={setQueryParams}
        columnDefs={columnDefs}
        defaultColDef={defaultColDef}
        title={t('tables.invoiceItems.title') as string}
        searchPlaceholder={t('tables.invoiceItems.searchPlaceholder') as string}
        onSelectionChanged={handleSelectionChanged}
        rowSelection='multiple'
        renderCustomActions={renderCustomActions}
      />

      <OperationFilterDialog
        open={openFilters}
        onClose={handleCloseFilters}
        onApply={handleApplyFilters}
        setQueryParams={setQueryParams}
        setOpenFilters={setOpenFilters}
      />
    </>
  )
}
