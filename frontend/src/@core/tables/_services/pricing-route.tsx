import React, { useMemo, useState } from 'react'
import { useRouter } from 'next/navigation'
import { usePricingRoute, useDeletePricingRoute } from 'src/hooks/_services/usePricingRoute'
import { FetchPricingRouteParams } from 'src/types/models/_services/pricing-route'
import { ColDef } from 'ag-grid-community'
import BaseTable from 'src/@core/components/tables/BaseTable'
import { useTranslation } from 'react-i18next'

import { toast } from 'react-hot-toast'
import TableActions from 'src/@core/components/tables/TableActions'

export default function PricingRouteTable() {
  const { t } = useTranslation()
  const router = useRouter()
  const [queryParams, setQueryParams] = useState<Partial<FetchPricingRouteParams>>({ limit: 100, offset: 0 })
  const { data, isLoading, isError, error, isPending, refetch } = usePricingRoute(queryParams)

  // Delete dialog state
  const [deleteDialogOpen, setDeleteDialogOpen] = useState<boolean>(false)
  const [pricingRouteToDelete, setPricingRouteToDelete] = useState<string>('')

  // Delete mutation
  const { mutateAsync: deletePricingRoute } = useDeletePricingRoute({
    onSuccess: () => {
      toast.success(t('tables.pricingRoute.deleteSuccess'))
      refetch()
    },
    onError: error => {
      toast.error(t('tables.pricingRoute.deleteError'))
      console.error(error)
    }
  })

  const columnDefs = useMemo<ColDef[]>(
    () => [
      {
        headerCheckboxSelection: true,
        checkboxSelection: true,
        width: 50,
        pinned: 'left'
      },
      {
        headerName: t('tables.pricingRoute.columns.route') as string,
        field: 'route.name',
        flex: 1,
        cellStyle: {
          fontWeight: 'bold'
        }
      },
      {
        headerName: t('tables.pricingRoute.columns.good') as string,
        field: 'good_type.label',
        flex: 1
      },
      {
        headerName: t('tables.pricingRoute.columns.unitePrice') as string,
        field: 'unite_price',
        flex: 1,
        valueFormatter: params => `${params.value} `
      },

      {
        headerName: t('common.actions.action') as string,
        field: 'actions',
        sortable: false,
        filter: false,
        width: 150,
        pinned: 'right',
        cellRenderer: (params: any) => (
          <TableActions
            id={params.data.id}
            basePath='/services/pricing'
            editAction={true}
            onDelete={handleDeleteClick}
            viewAction={false}
            module='services'
            model='pricingRoute'
          />
        )
      }
    ],
    [t, router]
  )

  const handleCreateClick = () => {
    router.push('/services/pricing/create')
  }

  // Delete handlers
  const handleDeleteClick = async (id: string) => {
    await deletePricingRoute(id)
  }

  const defaultColDef = useMemo(
    () => ({
      minWidth: 20,
      filter: false,
      sortable: true,
      resizable: true
    }),
    []
  )

  return (
    <>
      <BaseTable
        data={data}
        module='services'
        model='pricingroute'
        isLoading={isLoading}
        isPending={isPending}
        defaultColDef={defaultColDef}
        isError={isError}
        error={error}
        queryParams={queryParams}
        setQueryParams={setQueryParams}
        columnDefs={columnDefs}
        title={t('tables.pricingRoute.title') as string}
        searchPlaceholder={t('tables.pricingRoute.searchPlaceholder') as string}
        createButtonText={t('tables.pricingRoute.createButton') as string}
        onCreateClick={handleCreateClick}
        rowSelection='single'
      />
    </>
  )
}
