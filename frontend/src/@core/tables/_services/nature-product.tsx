import React, { useMemo, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { BaseFetchParams } from 'src/types/models'
import { ColDef } from 'ag-grid-community'
import BaseTable from 'src/@core/components/tables/BaseTable'
import { useRouter } from 'next/router'
import TableActions from 'src/@core/components/tables/TableActions'
import { useDeleteSelectedNatureOfArticles, useNatureOfArticles } from 'src/hooks/helpers/useNatureOfArticles'
import moment from 'moment'
import { useDeleteSelectedGroupArticles } from 'src/hooks/_services/useGroupArticles'
import toast from 'react-hot-toast'
import { Can } from 'src/layouts/components/acl/Can'
import { MODELS, MODULES } from 'src/layouts/components/acl/can'
import CustomDropdown from 'src/@core/shared/components/custom-dropdown'
import { useQueryClient } from '@tanstack/react-query'

export default function NatureProductTable() {
  const [queryParams, setQueryParams] = useState<Partial<BaseFetchParams>>({
    limit: 100,
    offset: 0
  })

  const [selectedRows, setSelectedRows] = useState<any[]>([])
  const router = useRouter()
  const { t } = useTranslation()
  const { data, isLoading, isError, isPending } = useNatureOfArticles(queryParams)
  const queryClient = useQueryClient()
  const { mutate } = useDeleteSelectedNatureOfArticles({
    onSuccess: () => {
      toast.success(t('tables.natureProduct.deleteSuccess'))
      queryClient.invalidateQueries({ queryKey: ['nature-of-articles'] })
    },
    onError: error => {
      toast.error(t('tables.natureProduct.deleteError'))
      console.error(error)
    }
  })

  const columnDefs = useMemo<ColDef[]>(
    () => [
      {
        headerCheckboxSelection: true,
        checkboxSelection: true,
        width: 50,
        pinned: 'left'
      },
      {
        headerName: t('tables.natureProduct.columns.name') as string,
        field: 'name'
      },
      {
        headerName: t('tables.natureProduct.columns.createdBy') as string,
        field: 'created_by',
        valueFormatter: ({ value }: { value: any }) => {
          return value ? value.first_name + ' ' + value.last_name : ''
        }
      },
      {
        headerName: t('tables.natureProduct.columns.createdAt') as string,
        field: 'created_at',
        valueFormatter: ({ value }: { value: string }) => {
          return moment(value).format('LL')
        }
      },
      {
        headerName: t('tables.natureProduct.columns.modifiedAt') as string,
        field: 'modified_at',
        valueFormatter: ({ value }: { value: string }) => {
          return moment(value).format('LL')
        }
      },
      {
        headerName: t('tables.natureProduct.columns.modifiedBy') as string,
        field: 'modified_by',
        valueFormatter: ({ value }: { value: any }) => {
          return value ? value.first_name + ' ' + value.last_name : ''
        }
      },
      {
        headerName: t('common.actions.action') as string,
        cellRenderer: (params: any) => {
          return (
            <TableActions
              onDelete={handleDeleteClick}
              module={MODULES.SERVICES}
              model={MODELS[MODULES.SERVICES].NATURE_PRODUCT}
              viewAction={false}
              id={params.data.id}
              basePath={'/services/nature-of-articles'}
            />
          )
        }
      }
    ],
    [t]
  )

  const handleSelectionChanged = (selectedRows: any[]) => {
    setSelectedRows(selectedRows)
  }

  const handleDeleteClick = (id: string) => {
    mutate([id])
  }

  const handleSelectedDelete = () => {
    const selectedIds = selectedRows.map(row => row.id)
    if (selectedIds.length > 0) {
      mutate(selectedIds)
    }
  }

  const customActions = () => {
    return (
      <Can action={'delete'} model={MODELS[MODULES.SERVICES].NATURE_PRODUCT} module={MODULES.SERVICES}>
        <CustomDropdown
          placeholder={t('common.actions.action') as string}
          buttonVariant='contained'
          buttonColor='primary'
          options={[
            {
              label: t('common.actions.deleteSelected'),
              icon: 'mdi:delete',
              onClick: handleSelectedDelete,
              disabled: selectedRows.length === 0
            }
          ]}
        />
      </Can>
    )
  }

  return (
    <BaseTable
      data={data}
      module={MODELS[MODULES.SERVICES].NATURE_PRODUCT}
      model={MODULES.SERVICES}
      isLoading={isLoading}
      isPending={isPending}
      isError={isError}
      error={isError}
      queryParams={queryParams}
      setQueryParams={setQueryParams}
      createButtonText={t('tables.natureProduct.createButton') as string}
      onCreateClick={() => router.push('/services/nature-of-articles/create')}
      columnDefs={columnDefs}
      defaultColDef={{
        minWidth: 20,

        filter: false
      }}
      title={t('tables.natureProduct.title') as string}
      searchPlaceholder={t('tables.natureProduct.searchPlaceholder') as string}
      onSelectionChanged={handleSelectionChanged}
      rowSelection='multiple'
      renderCustomActions={customActions}
    />
  )
}
