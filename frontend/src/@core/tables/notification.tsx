import React, { useMemo, useState, useEffect } from 'react'
import { useTranslation } from 'react-i18next'
import { useNotifications, useDeleteNotification, useDeleteSelectedNotifications } from 'src/hooks/useNotification'
import BaseTable from 'src/@core/components/tables/BaseTable'
import { ColDef } from 'ag-grid-community'
import moment from 'moment'
import TableActions from 'src/@core/components/tables/TableActions'

import { FetchNotificationParams } from 'src/types/models/notification'
import { useRouter } from 'next/navigation'
import toast from 'react-hot-toast'
import CustomDropdown from '../shared/components/custom-dropdown'
import { Can } from 'src/layouts/components/acl/Can'
import { MODELS, MODULES } from 'src/layouts/components/acl/can'

export default function NotificationTable() {
  const { t } = useTranslation()
  const [queryParams, setQueryParams] = useState<Partial<FetchNotificationParams>>({ limit: 100, offset: 0 })
  const { data, isLoading, isError, error, isPending } = useNotifications(queryParams)
  const [selectedRows, setSelectedRows] = useState<any[]>([])
  const router = useRouter()
  const { mutate: deleteSelectedNotifications } = useDeleteSelectedNotifications({
    onSuccess: () => {
      toast.success(t('tables.notification.deleteSuccess'))
    },
    onError: error => {
      toast.error(t('tables.notification.deleteError'))
      console.error(error)
    }
  })

  const columns = useMemo<ColDef[]>(
    () => [
      {
        headerName: t('tables.notification.columns.user') as string,
        field: 'created_by',
        valueFormatter: ({ value }) => {
          return value ? value.first_name + ' ' + value.last_name : '___'
        }
      },
      {
        headerName: t('tables.notification.columns.title') as string,
        field: 'title'
      },
      {
        headerName: t('tables.notification.columns.type') as string,
        field: 'type',
        valueFormatter: ({ value }) => {
          return value === 'NEW' ? t('tables.notification.new') : t('tables.notification.update')
        }
      },
      {
        headerName: t('tables.notification.columns.modifiedBy') as string,
        field: 'modified_by',
        valueFormatter: ({ value }) => {
          return value ? value.first_name + ' ' + value.last_name : '___'
        }
      },
      {
        headerName: t('tables.notification.columns.modifiedAt') as string,
        field: 'modified_at',
        valueFormatter: ({ value }: { value: string }) => {
          return moment(value).format('LL')
        }
      },
      {
        headerName: t('common.actions.action') as string,
        field: 'actions',
        sortable: false,
        filter: false,
        width: 150,
        cellRenderer: (params: any) => (
          <TableActions
            id={params.data.id}
            basePath='/notifications'
            onDelete={handleDeleteClick}
            module={MODULES.NOTIFICATION}
            model={MODELS[MODULES.NOTIFICATION].NOTIFICATION}
          />
        )
      }
    ],
    [t]
  )

  const handleDeleteClick = (id: string) => {
    deleteSelectedNotifications([id])
  }

  const handleCreateNotification = () => {
    router.push('/notifications/create')
  }

  const handleDeleteSelected = () => {
    const selectedIds = selectedRows.map(row => row.id)
    try {
      deleteSelectedNotifications(selectedIds)
    } catch (error) {
      // Error is handled in the mutation's onError callback
    }
  }

  const customActions = () => {
    return (
      <Can action={'delete'} model={MODELS[MODULES.NOTIFICATION].NOTIFICATION} module={MODULES.NOTIFICATION}>
        <CustomDropdown
          placeholder={t('common.actions.action') as string}
          buttonVariant='contained'
          buttonColor='primary'
          options={[
            {
              label: t('common.actions.deleteSelected'),
              icon: 'mdi:delete',
              onClick: handleDeleteSelected,
              disabled: selectedRows.length === 0
            }
          ]}
        />
      </Can>
    )
  }

  return (
    <>
      <BaseTable
        data={data}
        module={MODULES.NOTIFICATION}
        model={MODELS[MODULES.NOTIFICATION].NOTIFICATION}
        isLoading={isLoading}
        isPending={isPending}
        isError={isError}
        error={error}
        defaultColDef={{
          sortable: true,
          flex: 1,
          resizable: true,
          filter: false
        }}
        queryParams={queryParams}
        onSelectionChanged={setSelectedRows}
        setQueryParams={setQueryParams}
        onCreateClick={handleCreateNotification}
        columnDefs={columns}
        title={t('tables.notification.title') as string}
        searchPlaceholder={t('tables.notification.searchPlaceholder') as string}
        rowSelection='multiple'
        renderCustomActions={customActions}

        // createButtonText={t('tables.notification.createButton') as string}
      />
    </>
  )
}
