import React, { useState, useMemo } from 'react'
import { useTranslation } from 'react-i18next'
import { ColDef } from 'ag-grid-community'
import {
  useDestinations,
  useDeleteDestination,
  useDeleteSelectedDestinations
} from 'src/hooks/_services/useDestinations'
import { BaseTable } from 'src/@core/components/tables/BaseTable'
import { Icon } from '@iconify/react'
import moment from 'moment'
import { useRouter } from 'next/router'
import { DestinationParams } from 'src/types/models/_services/destinations'

import toast from 'react-hot-toast'
import TableActions from '../components/tables/TableActions'
import { Can } from 'src/layouts/components/acl/Can'
import CustomDropdown from '../shared/components/custom-dropdown'
import { MODELS, MODULES } from 'src/layouts/components/acl/can'

export default function DestinationsTable() {
  const { t } = useTranslation()
  const router = useRouter()

  const [selectedRows, setSelectedRows] = useState<any[]>([])

  const [queryParams, setQueryParams] = useState<Partial<DestinationParams>>({
    offset: 0,
    limit: 100,
    search: ''
  })

  const { data, isLoading, isPending, isError, error, refetch } = useDestinations(queryParams)

  const { mutateAsync: deleteSelectedDestinations } = useDeleteSelectedDestinations({
    onSuccess: () => {
      toast.success(t('tables.destination.deleteSuccess'))
      refetch()
    },
    onError: error => {
      toast.error(t('tables.destination.deleteError'))
      console.error(error)
    }
  })

  const columnDefs = useMemo<ColDef[]>(
    () => [
      {
        headerCheckboxSelection: true,
        checkboxSelection: true,
        width: 50,
        pinned: 'left'
      },
      {
        field: 'name',
        headerName: t('tables.destination.columns.name') as string
      },
      {
        field: 'type',
        headerName: t('tables.destination.columns.type') as string,
        valueFormatter: ({ value }) => {
          return value === 'NATIONAL'
            ? t('tables.destination.types.national')
            : value === 'INTERNATIONAL'
            ? t('tables.destination.types.international')
            : value
        }
      },
      {
        field: 'lat',
        headerName: t('tables.destination.columns.latitude') as string
      },
      {
        field: 'long',
        headerName: t('tables.destination.columns.longitude') as string
      },
      {
        field: 'is_active',
        headerName: t('tables.destination.columns.status') as string,
        cellRenderer: ({ value }: { value: boolean }) => {
          return <Icon icon={value ? 'mdi:check-circle' : 'mdi:close-circle'} color={value ? '#56CA00' : '#FF4C51'} />
        }
      },
      {
        field: 'modified_by',
        headerName: t('tables.destination.columns.modifiedBy') as string,
        valueFormatter: ({ value }) => {
          return value ? value.first_name + ' ' + value.last_name : ''
        }
      },
      {
        field: 'modified_at',
        headerName: t('tables.destination.columns.modifiedAt') as string,
        valueFormatter: ({ value }) => {
          return value ? moment(value).format('LL') : '__'
        }
      },
      {
        headerName: t('common.actions.action') as string,
        field: 'actions',
        width: 150,
        cellRenderer: (params: any) => {
          return (
            <TableActions
              id={params.data.id}
              basePath='settings'
              model='destinations'
              module='settings'
              onDelete={handleDelete}
            />
          )
        }
      }
    ],
    [t]
  )
  function handleDelete(id: string) {
    deleteSelectedDestinations([id])
  }

  const handleDeleteSelected = async () => {
    const selectedIds = selectedRows.map(row => row.id)
    try {
      await deleteSelectedDestinations(selectedIds)
    } catch (error) {
      // Error is handled in the mutation's onError callback
    }
  }

  const handleCreateClick = () => {
    router.push('/settings/destinations/create')
  }

  const customActions = () => {
    return (
      <Can action={'delete'} model={MODELS[MODULES.SETTINGS].DESTINATIONS} module={MODULES.SETTINGS}>
        <CustomDropdown
          placeholder={t('common.actions.action') as string}
          buttonVariant='contained'
          buttonColor='primary'
          options={[
            {
              label: t('tables.destination.deleteSelected'),
              icon: 'mdi:delete',
              onClick: handleDeleteSelected,
              disabled: selectedRows.length === 0
            }
          ]}
        />
      </Can>
    )
  }

  return (
    <>
      <BaseTable
        data={data}
        model='destinations'
        module='settings'
        isLoading={isLoading}
        isPending={isPending}
        isError={isError}
        error={error}
        queryParams={queryParams}
        setQueryParams={setQueryParams}
        columnDefs={columnDefs}
        defaultColDef={{
          minWidth: 20,
          resizable: true
        }}
        onSelectionChanged={setSelectedRows}
        renderCustomActions={customActions}
        title={t('tables.destination.title') as string}
        searchPlaceholder={t('tables.destination.searchPlaceholder') as string}
        createButtonText={t('tables.destination.createButton') as string}
        onCreateClick={handleCreateClick}
        rowSelection='multiple'
      />
    </>
  )
}
