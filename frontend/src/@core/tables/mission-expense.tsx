import React, { useState, useMemo } from 'react'
import {
  useCancelMissionExpenses,
  useDeleteSelectedMissionExpenses,
  useMissionExpenses
} from 'src/hooks/useMissionExpenses'
import { MissionExpenseParams } from 'src/types/models/mission-expense'
import { ColDef } from 'ag-grid-community'
import moment from 'moment'
import { useTranslation } from 'react-i18next'
import { useRouter } from 'next/navigation'
import BaseTable from 'src/@core/components/tables/BaseTable'
import {
  Box,
  Button,
  FormControl,
  IconButton,
  InputLabel,
  Menu,
  MenuItem,
  Select,
  SelectChangeEvent,
  Stack
} from '@mui/material'
import { Icon } from '@iconify/react'
import TableActions from '../components/tables/TableActions'
import toast from 'react-hot-toast'
import { useQueryClient } from '@tanstack/react-query'

export default function MissionExpenseTable() {
  const [queryParams, setQueryParams] = useState<Partial<MissionExpenseParams>>({
    limit: 100,
    offset: 0
  })
  const [validateFilter, setValidateFilter] = useState<string>('all')
  const [prestationFilter, setPrestationFilter] = useState<string>('all')
  const [selectedRows, setSelectedRows] = useState<any[]>([])
  const [actionAnchorEl, setActionAnchorEl] = useState<null | HTMLElement>(null)
  const queryClient = useQueryClient()
  const { mutate: cancelMissionExpenses, isPending: cancelingExpeses } = useCancelMissionExpenses({
    onSuccess() {
      toast.success('Success in canceling missing expense')
      queryClient.invalidateQueries({ queryKey: ['mission-expenses'] })
    },
    onError(error) {
      toast.error(error.message)
    }
  })

  const { mutate: deleteSelected, isPending: deletingSelectedExpenses } = useDeleteSelectedMissionExpenses({
    onSuccess() {
      toast.success('Success in deleting missing expenses')
      queryClient.invalidateQueries({ queryKey: ['mission-expenses'] })
    },
    onError(error) {
      toast.error(error.message)
    }
  })
  const { data, isLoading, isError, error, isPending } = useMissionExpenses(queryParams)

  const router = useRouter()
  const { t } = useTranslation()

  const columnDefs = useMemo<ColDef[]>(
    () => [
      {
        headerCheckboxSelection: true,
        checkboxSelection: true,
        width: 50,
        pinned: 'left'
      },
      {
        field: 'code',
        headerName: t('tables.missionExpenses.columns.id') as string,
        minWidth: 130,
        cellStyle: {
          fontWeight: 'bold'
        }
      },
      {
        field: 'customer',
        headerName: t('tables.missionExpenses.columns.client') as string,
        valueFormatter: ({ value }: { value: string }) => value,
        minWidth: 130,
        tooltipField: 'customer',
        cellStyle: {
          fontWeight: 'bold'
        }
      },
      {
        field: 'validate',
        headerName: t('tables.missionExpenses.columns.validate') as string,
        width: 100,
        cellRenderer: ({ value }: { value: boolean }) => {
          return (
            <Icon
              icon={value ? 'mdi:check-circle' : 'mdi:close-circle'}
              style={{
                color: value ? '#56CA00' : '',
                fontSize: '20px'
              }}
            />
          )
        }
      },
      {
        field: 'validation_info',
        headerName: t('tables.missionExpenses.columns.validationState') as string,
        minWidth: 150,
        cellRenderer: ({ value }: { value: any }) => {
          return (
            <Box sx={{ display: 'flex', gap: 1 }}>
              {[...Array(value.total_validations_needed + 1)].map((_, index) => (
                <Box key={index}>
                  {index <= value.validation_count ? (
                    <Icon
                      icon='mdi:check-circle'
                      style={{
                        color: '#56CA00',
                        fontSize: '20px'
                      }}
                    />
                  ) : (
                    <Icon
                      icon='mdi:circle'
                      style={{
                        color: 'gray',
                        fontSize: '20px'
                      }}
                    />
                  )}
                </Box>
              ))}
            </Box>
          )
        }
      },
      {
        field: 'validation_info.next_validator',
        headerName: t('tables.missionExpenses.columns.nextValidator') as string,
        minWidth: 180,
        valueFormatter: ({ value }) => {
          return value ? value.first_name + ' ' + value.last_name : ''
        }
      },
      {
        field: 'canceled',
        headerName: t('tables.missionExpenses.columns.canceled') as string,
        width: 100,
        cellRenderer: ({ value }: { value: boolean }) => {
          return (
            <Icon
              icon='mdi:close-circle'
              style={{
                color: value ? '#FF4C51' : '#56CA00',
                fontSize: '20px'
              }}
            />
          )
        }
      },
      {
        field: 'modified_by',
        headerName: t('tables.missionExpenses.columns.modifiedBy') as string,
        minWidth: 200,
        valueFormatter: ({ value }) => {
          return value ? value.first_name + ' ' + value.last_name : ''
        }
      },

      {
        field: 'modified_at',
        headerName: t('tables.missionExpenses.columns.modifiedAt') as string,
        minWidth: 150,
        valueFormatter: ({ value }: { value: string }) => {
          return moment(value).format('LL')
        }
      },
      {
        headerName: t('common.actions.action') as string,
        field: 'actions',
        sortable: false,
        filter: false,
        width: 150,
        cellRenderer: (params: any) => (
          <TableActions
            id={params.data.id}
            basePath='/financial/mission-expenses'
            onDelete={handleDeleteClick}
            deleteAction={false}
            module='financial'
            model='missionexpenses'
          />
        )
      }
    ],
    [t]
  )

  const defaultColDef = useMemo(
    () => ({
      minWidth: 20,
      filter: false,
      sortable: true,
      resizable: true
    }),
    []
  )

  const handleDeleteClick = (id: string) => {
    console.log(id)
  }

  const handleCreateClick = () => {
    router.push('/mission-expenses/create')
  }

  const handleOnSelected = (rows: any) => {
    setSelectedRows(rows)
  }

  const handleValidateFilterChange = (event: SelectChangeEvent) => {
    const value = event.target.value
    setValidateFilter(value)

    let validateValue = false
    if (value === 'yes') validateValue = true
    if (value === 'no') validateValue = false

    setQueryParams(prev => ({
      ...prev,
      validate: validateValue,
      offset: 0
    }))
  }

  const handlePrestationFilterChange = (event: SelectChangeEvent) => {
    const value = event.target.value
    setPrestationFilter(value)

    setQueryParams(prev => ({
      ...prev,
      prestation: value === 'all' ? undefined : value,
      offset: 0
    }))
  }

  // Actions dropdown handlers
  const handleActionsClick = (event: React.MouseEvent<HTMLElement>) => {
    setActionAnchorEl(event.currentTarget)
  }

  const handleActionsClose = () => {
    setActionAnchorEl(null)
  }

  const handleCancelMissionExpenses = () => {
    const selectedIds = selectedRows?.map(row => row.id)
    cancelMissionExpenses({ mission_expense_ids: selectedIds })
    handleActionsClose()
  }

  const handleDeleteMissionExpenses = () => {
    const selectedIds = selectedRows?.map(row => row.id)
    deleteSelected({ ids: selectedIds })
    handleActionsClose()
  }

  const renderBulkActions = () => {
    return (
      <>
        {selectedRows?.length > 0 && (
          <Button
            variant='contained'
            color='primary'
            disabled={cancelingExpeses || deletingSelectedExpenses}
            startIcon={<Icon icon='mdi:dots-vertical' />}
            onClick={handleActionsClick}
            sx={{ ml: 2 }}
          >
            {t('common.actions.action')} ({selectedRows?.length})
          </Button>
        )}

        <Menu anchorEl={actionAnchorEl} open={Boolean(actionAnchorEl)} onClose={handleActionsClose}>
          <MenuItem onClick={handleCancelMissionExpenses}>
            <Icon icon='mdi:close-circle-outline' style={{ marginRight: '8px' }} />
            {t('tables.missionExpenses.actions.cancel')}
          </MenuItem>
          <MenuItem onClick={handleDeleteMissionExpenses}>
            <Icon icon='mdi:trash' style={{ marginRight: '8px' }} />
            {t('tables.missionExpenses.actions.delete')}
          </MenuItem>
        </Menu>
      </>
    )
  }
  const renderCustomFilters = () => (
    <Stack direction='row' spacing={2}>
      <FormControl size='small' sx={{ minWidth: 200 }}>
        <InputLabel id='validate-filter-label'>{t('tables.missionExpenses.filters.validity:')}</InputLabel>
        <Select
          labelId='validate-filter-label'
          value={validateFilter}
          label={t('tables.missionExpenses.filters.validity:')}
          onChange={handleValidateFilterChange}
        >
          <MenuItem value='all'>{t('tables.missionExpenses.filters.all')}</MenuItem>
          <MenuItem value='yes'>{t('tables.missionExpenses.filters.yes')}</MenuItem>
          <MenuItem value='no'>{t('tables.missionExpenses.filters.no')}</MenuItem>
        </Select>
      </FormControl>

      <FormControl size='small' sx={{ minWidth: 200 }}>
        <InputLabel id='prestation-filter-label'>{t('tables.missionExpenses.filters.type')}</InputLabel>
        <Select
          labelId='prestation-filter-label'
          value={prestationFilter}
          label={t('tables.missionExpenses.filters.type')}
          onChange={handlePrestationFilterChange}
        >
          <MenuItem value='all'>{t('tables.missionExpenses.filters.all')}</MenuItem>
          <MenuItem value='CITERNE'>{t('tables.missionExpenses.filters.citerne')}</MenuItem>
          <MenuItem value='PLATEAU'>{t('tables.missionExpenses.filters.plateau')}</MenuItem>
          <MenuItem value='BENNE'>{t('tables.missionExpenses.filters.benne')}</MenuItem>
        </Select>
      </FormControl>
    </Stack>
  )

  return (
    <>
      <BaseTable
        data={data}
        module='financial'
        model='missionexpenses'
        isLoading={isLoading}
        isPending={isPending}
        isError={isError}
        error={error}
        queryParams={queryParams}
        setQueryParams={setQueryParams}
        columnDefs={columnDefs}
        defaultColDef={defaultColDef}
        title={t('tables.missionExpenses.title') as string}
        searchPlaceholder={t('tables.missionExpenses.searchPlaceholder') as string}
        onCreateClick={handleCreateClick}
        onSelectionChanged={handleOnSelected}
        rowSelection='multiple'
        renderCustomFilters={renderCustomFilters}
        renderCustomActions={renderBulkActions}
      />
    </>
  )
}
