import React, { useMemo, useState } from 'react'
import { useFetchPermissions } from 'src/hooks/useFetchPermission'
import { BaseFetchParams } from 'src/types/models'
import { ColDef } from 'ag-grid-community'
import BaseTable from '../components/tables/BaseTable'
import { useTranslation } from 'react-i18next'

export default function PermissionTable() {
  const [queryParams, setQueryParams] = useState<Partial<BaseFetchParams>>({
    limit: 100,
    offset: 0
  })

  const { data, isLoading, isError, isPending } = useFetchPermissions(queryParams)
  const colDef = useMemo<ColDef[]>(
    () => [
      {
        headerCheckboxSelection: true,
        checkboxSelection: true,
        width: 50,
        pinned: 'left'
      },

      {
        headerName: 'Name',
        field: 'name'
      },

      {
        headerName: 'Code Name',
        field: 'codename'
      },
      {
        headerName: 'Content Type',
        field: 'content_type',

        valueFormatter: ({ value }) => {
          return `${value.app_label} | ${value.model}`
        }
      }
    ],
    []
  )
  console.log(data)

  return (
    <BaseTable
      data={data}
      columnDefs={colDef}
      isError={isError}
      model='permission'
      module='auth'
      isLoading={isLoading}
      isPending={isPending}
      defaultColDef={{
        filter: false,
        sortable: false,
        flex: 1
      }}
      setQueryParams={setQueryParams}
      title='Permission'
      queryParams={queryParams}
    />
  )
}
