import { useState } from 'react'
import { ColDef } from 'ag-grid-community'
import { useAddToOdoo, useInvoices } from '../../hooks/useInvoice'
import { Invoice, FetchInvoiceParams } from '../../types/models/invoice'
import moment from 'moment'
import { useRouter } from 'next/navigation'
import BaseTable from 'src/@core/components/tables/BaseTable'
import { useTranslation } from 'react-i18next'
import TableActions from 'src/@core/components/tables/TableActions'
import { FormControl, InputLabel, MenuItem, Select, Box, Button, Fade } from '@mui/material'
import Icon from 'src/@core/components/icon'
import toast from 'react-hot-toast'

const statusOptions = [
  { value: 'all', label: 'Tous' },
  { value: 'PAYEE', label: 'Payée' },
  { value: 'TRANSFEREE', label: 'Transférée' }
]

export default function InvoiceGrid() {
  const { t } = useTranslation()
  const [queryParams, setQueryParams] = useState<Partial<FetchInvoiceParams>>({
    limit: 100,
    offset: 0,
    search: ''
  })

  const [statusFilter, setStatusFilter] = useState<string>('all')
  const [selectedRows, setSelectedRows] = useState<Invoice[]>([])

  const { data, isLoading, isError, error, isPending } = useInvoices(queryParams)
  const router = useRouter()
  const { mutateAsync: addToOdoo, isPending: isAdding } = useAddToOdoo({
    onSuccess: () => {
      toast.success(t('tables.invoice.addToOdooSuccess'))
      setSelectedRows([])
    },
    onError: error => {
      toast.error(t('tables.invoice.addToOdooError'))
    }
  })

  const columnDefs: ColDef[] = [
    {
      headerCheckboxSelection: true,
      checkboxSelection: true,
      width: 50,
      pinned: 'left'
    },
    {
      headerName: t('tables.invoice.columns.number') as string,
      field: 'code',
      flex: 1,
      cellStyle: {
        fontWeight: 'bold'
      }
    },
    {
      headerName: t('tables.invoice.columns.client') as string,
      field: 'customer.name',
      flex: 1,
      valueFormatter: ({ value }) => {
        return value ? value : '---'
      }
    },
    {
      headerName: t('tables.invoice.columns.dueDate') as string,
      field: 'echeance',
      flex: 1,
      valueFormatter: params => (params.value ? moment(params.value).format('LL') : '')
    },
    {
      headerName: t('tables.invoice.columns.amountHT') as string,
      field: 'amount',
      flex: 1,
      valueFormatter: params => `${params.value} FCFA`
    },
    {
      headerName: t('tables.invoice.columns.amountTTC') as string,
      field: 'amount',
      flex: 1,
      valueFormatter: params => `${params.value} FCFA`
    },

    {
      headerName: t('tables.invoice.columns.modifiedBy') as string,
      field: 'modified_by',
      flex: 1,
      valueFormatter: ({ value }) => {
        return value ? value.first_name + ' ' + value.last_name : ''
      }
    },
    {
      headerName: t('tables.invoice.columns.createdAt') as string,
      field: 'created_at',
      flex: 1,
      valueFormatter: params => (params.value ? moment(params.value).format('LL') : '')
    },

    {
      headerName: t('tables.invoice.columns.modifiedAt') as string,
      field: 'modified_at',
      flex: 1,
      valueFormatter: params => (params.value ? moment(params.value).format('LL') : '')
    },
    {
      headerName: t('common.actions.action') as string,
      field: 'actions',
      sortable: false,
      filter: false,
      width: 150,
      cellRenderer: (params: any) => (
        <TableActions
          id={params.data.id}
          basePath='/financial/invoices'
          onDelete={handleDeleteClick}
          deleteAction={false}
          editAction={false}
          module='financial'
          model='invoice'
        />
      )
    }
  ]

  const handleDeleteClick = (id: string) => {
    console.log(id)
  }

  const handleStatusChange = (event: any) => {
    const value = event.target.value
    setStatusFilter(value)

    setQueryParams(prev => ({
      ...prev,
      status: value === 'all' ? undefined : value,
      offset: 0
    }))
  }

  const handleCreateClick = () => {
    router.push('/financial/invoices/create')
  }

  const onSelectionChanged = (row: any) => {
    setSelectedRows(row)
  }

  const handleAddToOdoo = async () => {
    const invoiceIds = selectedRows.map(row => row.id)
    await addToOdoo({ invoice_ids: invoiceIds })
  }

  const renderCustomFilters = () => (
    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
      <FormControl size='small' sx={{ minWidth: 120 }}>
        <InputLabel id='status-filter-label'>Status</InputLabel>
        <Select
          labelId='status-filter-label'
          id='status-filter'
          value={statusFilter}
          label='Status'
          onChange={handleStatusChange}
        >
          {statusOptions.map(option => (
            <MenuItem key={option.value} value={option.value}>
              {option.label}
            </MenuItem>
          ))}
        </Select>
      </FormControl>

      {selectedRows.length > 0 && (
        <Button
          variant='contained'
          color='primary'
          onClick={handleAddToOdoo}
          disabled={isAdding}
          startIcon={<Icon icon='mdi:odoo' />}
          size='small'
        >
          Add to Odoo ({selectedRows.length})
        </Button>
      )}
    </Box>
  )

  return (
    <>
      <BaseTable
        data={data}
        module='financial'
        model='invoice'
        isLoading={isLoading}
        isPending={isPending}
        isError={isError}
        error={error}
        queryParams={queryParams}
        defaultColDef={{
          sortable: true,
          resizable: true,
          filter: false
        }}
        setQueryParams={setQueryParams}
        columnDefs={columnDefs}
        title={t('tables.invoice.title') as string}
        searchPlaceholder={t('tables.invoice.searchPlaceholder') as string}
        onCreateClick={handleCreateClick}
        rowSelection='multiple'
        onSelectionChanged={onSelectionChanged}
        renderCustomFilters={renderCustomFilters}
      />
    </>
  )
}
