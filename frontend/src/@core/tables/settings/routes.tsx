import { Box } from '@mui/material'
import { useTranslation } from 'react-i18next'
import { ColDef } from 'ag-grid-community'
import { useDeleteSelected } from 'src/hooks/helpers/useRoutes'
import { useRoutes } from 'src/hooks/helpers/useRoutes'
import BaseTable from 'src/@core/components/tables/BaseTable'
import TableActions from 'src/@core/components/tables/TableActions'
import { useMemo, useState } from 'react'
import CustomDropdown from 'src/@core/shared/components/custom-dropdown'
import { Can } from 'src/layouts/components/acl/Can'
import toast from 'react-hot-toast'
import { useRouter } from 'next/router'
import { FetchRoutesParams } from 'src/types/models/routes'
import { Icon } from '@iconify/react'
import moment from 'moment'
import { MODELS, MODULES } from 'src/layouts/components/acl/can'

export default function RoutesTable() {
  const { t } = useTranslation()
  const [queryParams, setQueryParams] = useState<Partial<FetchRoutesParams>>({
    limit: 100,
    offset: 0
  })
  const router = useRouter()

  const { data, isLoading, isError, isPending } = useRoutes(queryParams)
  const { mutateAsync: deleteSelectedRoutes } = useDeleteSelected({
    onSuccess: () => {
      toast.success(t('tables.routes.deleteSuccess'))
    },
    onError: error => {
      toast.error(t('tables.routes.deleteError'))
      console.error(error)
    }
  })
  const [selectedRows, setSelectedRows] = useState<any[]>([])

  const handleSelectionChanged = (selectedRows: any[]) => {
    setSelectedRows(selectedRows)
  }

  const handleDeleteClick = (id: string) => {
    deleteSelectedRoutes([id])
  }

  const handleDeleteSelected = async () => {
    const selectedIds = selectedRows.map(row => row.id)
    try {
      await deleteSelectedRoutes(selectedIds)
    } catch (error) {
      // Error is handled in the mutation's onError callback
    }
  }

  const customActions = () => {
    return (
      <Can action={'delete'} model={'route'} module={'settings'}>
        <CustomDropdown
          placeholder={t('common.actions.action') as string}
          buttonVariant='contained'
          buttonColor='primary'
          options={[
            {
              label: t('tables.routes.deleteSelected'),
              icon: 'mdi:delete',
              onClick: handleDeleteSelected,
              disabled: selectedRows.length === 0
            }
          ]}
        />
      </Can>
    )
  }

  const columnDefs = useMemo<ColDef[]>(
    () => [
      {
        headerCheckboxSelection: true,
        checkboxSelection: true,
        width: 50,
        pinned: 'left'
      },
      {
        headerName: t('tables.routes.columns.name') as string,
        field: 'name',
        cellStyle: {
          fontWeight: 'bold'
        }
      },
      {
        headerName: t('tables.routes.columns.livraisonVille') as string,
        field: 'is_city',
        cellRenderer: ({ value }: { value: boolean }) => {
          return <Icon icon={value ? 'mdi:check-circle' : 'mdi:close-circle'} color={value ? '#56CA00' : '#FF4C51'} />
        }
      },
      {
        headerName: t('tables.routes.columns.volumeCarburant') as string,
        field: 'petrol_volume'
      },
      {
        headerName: t('tables.routes.columns.distance') as string,
        field: 'distance'
      },
      {
        headerName: t('tables.routes.columns.duree') as string,
        field: 'delay'
      },
      {
        headerName: t('tables.routes.columns.fraisRoute') as string,
        field: 'fees'
      },
      {
        headerName: t('tables.routes.columns.nombreBons') as string,
        field: 'nbr_ligne'
      },
      {
        headerName: t('tables.routes.columns.activee') as string,
        field: 'is_active',
        cellRenderer: ({ value }: { value: boolean }) => {
          return <Icon icon={value ? 'mdi:check-circle' : 'mdi:close-circle'} color={value ? '#56CA00' : '#FF4C51'} />
        }
      },
      {
        headerName: t('tables.routes.columns.modifiedBy') as string,
        field: 'modified_by',
        valueFormatter: ({ value }: { value: any }) => {
          return value ? value.first_name + ' ' + value.last_name : ' ---'
        }
      },
      {
        headerName: t('tables.routes.columns.modifiedAt') as string,
        field: 'modified_at',
        valueFormatter: ({ value }: { value: string }) => {
          return value ? moment(value).format('LL') : ' ---'
        }
      },
      {
        headerName: t('common.actions.action') as string,
        field: 'actions',
        width: 150,
        cellRenderer: (params: any) => {
          return (
            <TableActions
              id={params.data.id}
              basePath='settings'
              transLationKey='routes'
              viewAction={false}
              model={MODELS[MODULES.SETTINGS].ROUTE}
              module={MODULES.SETTINGS}
              onDelete={handleDeleteClick}
            />
          )
        }
      }
    ],
    [t]
  )

  const handleCreateClick = () => {
    router.push('/settings/routes/create')
  }

  return (
    <Box>
      <BaseTable
        data={data}
        module='settings'
        model='routes'
        isLoading={isLoading}
        isPending={isPending}
        isError={isError}
        error={isError}
        queryParams={queryParams}
        setQueryParams={setQueryParams}
        columnDefs={columnDefs}
        defaultColDef={{
          sortable: true,
          resizable: true,
          filter: false
        }}
        title={t('tables.routes.title') as string}
        searchPlaceholder={t('tables.routes.searchPlaceholder') as string}
        createButtonText={t('tables.routes.createButton') as string}
        onCreateClick={handleCreateClick}
        onSelectionChanged={handleSelectionChanged}
        rowSelection='multiple'
        renderCustomActions={customActions}
      />
    </Box>
  )
}
