import { useMemo, useState } from 'react'
import { useDocumentTypes } from 'src/hooks/helpers/useDocumentType'
import { BaseFetchParams } from 'src/types/models'
import { ColDef } from 'ag-grid-community'
import BaseTable from 'src/@core/components/tables/BaseTable'
import { useRouter } from 'next/router'

export default function DocumentTypeTable() {
  const [queryParams, setQueryParams] = useState<Partial<BaseFetchParams>>({
    limit: 100,
    offset: 0
  })
  const router = useRouter()
  const { data, isLoading, isError, isPending } = useDocumentTypes(queryParams)
  const colDef = useMemo<ColDef[]>(
    () => [
      {
        headerCheckboxSelection: true,
        checkboxSelection: true,
        width: 50,
        pinned: 'left'
      },
      {
        headerName: 'Name',
        field: 'name'
      },
      {
        headerName: 'Code',
        field: 'code'
      }
    ],
    []
  )

  return (
    <BaseTable
      data={data}
      module='settings'
      model='documentType'
      columnDefs={colDef}
      isError={isError}
      isLoading={isLoading}
      isPending={isPending}
      defaultColDef={{
        filter: false,
        sortable: false,
        flex: 1
      }}
      searchPlaceholder=''
      createButtonText='Add Document Type'
      onCreateClick={() => router.push('/settings/document-type/create')}
      setQueryParams={setQueryParams}
      title='Document Types'
      queryParams={queryParams}
    />
  )
}
