import React, { useState, useMemo } from 'react'
import { MissionExpenseOtherParams } from 'src/types/models/mission-expense-other'
import { ColDef } from 'ag-grid-community'
import moment from 'moment'
import { useTranslation } from 'react-i18next'
import { useRouter } from 'next/router'
import BaseTable from 'src/@core/components/tables/BaseTable'
import { useDeleteMissionExpenseOther, useMissionExpensesOthers } from 'src/hooks/useMissionExpenseOthers'
import { Box, FormControl, InputLabel, MenuItem, Select, SelectChangeEvent } from '@mui/material'
import { Icon } from '@iconify/react'
import TableActions from '../components/tables/TableActions'
import toast from 'react-hot-toast'
import { useQueryClient } from '@tanstack/react-query'

export default function MissionExpenseOtherTable() {
  const [queryParams, setQueryParams] = useState<Partial<MissionExpenseOtherParams>>({
    limit: 100,
    offset: 0
  })
  const [validateFilter, setValidateFilter] = useState<string>('all')

  const { data, isLoading, isError, error, isPending } = useMissionExpensesOthers(queryParams)
  const router = useRouter()
  const { t } = useTranslation()
  const queryClient = useQueryClient()

  const { mutateAsync: deleteMissionExpenseOther } = useDeleteMissionExpenseOther({
    onSuccess: () => {
      toast.success(t('tables.missionExpenses.deleteSuccess'))
      queryClient.invalidateQueries({ queryKey: ['mission-expenses-other'] })
    },
    onError: error => {
      toast.error(t('tables.missionExpenses.deleteError'))
    }
  })

  const columnDefs = useMemo<ColDef[]>(
    () => [
      {
        headerCheckboxSelection: true,
        checkboxSelection: true,
        width: 40,
        pinned: 'left'
      },
      {
        field: 'code',
        headerName: t('tables.missionExpenseOther.columns.code') as string,
        minWidth: 60,
        cellStyle: {
          fontWeight: 'bold'
        }
      },
      {
        field: 'validation_info.validation_count',
        headerName: t('tables.missionExpenseOther.columns.validationState') as string,
        width: 150,
        cellRenderer: ({ value }: { value: number }) => {
          return (
            <Box sx={{ display: 'flex', gap: 1 }}>
              {[...Array(3)].map((_, index) => (
                <Box key={index}>
                  {index < value ? (
                    <Icon
                      icon='mdi:check-circle'
                      style={{
                        color: '#56CA00',
                        fontSize: '20px'
                      }}
                    />
                  ) : (
                    <Icon
                      icon='mdi:circle'
                      style={{
                        color: 'gray',
                        fontSize: '20px'
                      }}
                    />
                  )}
                </Box>
              ))}
            </Box>
          )
        }
      },
      {
        field: 'validation_info.next_validator',
        headerName: t('tables.missionExpenseOther.columns.nextValidator') as string,
        minWidth: 150,
        valueFormatter: ({ value }) => {
          return value ? value.first_name + ' ' + value.last_name : '-----'
        }
      },
      {
        field: 'validade',
        headerName: t('tables.missionExpenseOther.columns.validate') as string,
        width: 100
      },
      {
        field: 'total_amount',
        headerName: t('tables.missionExpenseOther.columns.totalAmount') as string,
        width: 150
      },
      {
        field: 'modified_by',
        headerName: t('tables.missionExpenseOther.columns.modifiedBy') as string,
        minWidth: 200,
        valueFormatter: ({ value }) => {
          return value ? value.first_name + ' ' + value.last_name : ''
        }
      },
      {
        field: 'modified_at',
        headerName: t('tables.missionExpenseOther.columns.modifiedAt') as string,
        minWidth: 150,
        valueFormatter: ({ value }: { value: string }) => {
          return moment(value).format('LL')
        }
      },
      {
        headerName: t('common.actions.action') as string,
        field: 'actions',
        sortable: false,
        filter: false,
        width: 150,
        cellRenderer: (params: any) => (
          <TableActions
            id={params.data.id}
            transLationKey='missionExpenseOther'
            basePath='/financial/mission-expenses-others'
            onDelete={handleDeleteClick}
            module='financial'
            model='missionexpensesother'
          />
        )
      }
    ],
    [t]
  )

  const defaultColDef = useMemo(
    () => ({
      minWidth: 20,
      filter: false,
      sortable: true,
      resizable: true
    }),
    []
  )

  const handleCreateClick = () => {
    router.push('/financial/mission-expenses-others/create')
  }

  const handleDeleteClick = (id: string) => {
    deleteMissionExpenseOther({ id })
  }

  const handleValidateFilterChange = (event: SelectChangeEvent) => {
    const value = event.target.value
    setValidateFilter(value)

    let validateValue: any
    if (value === 'yes') validateValue = true
    if (value === 'no') validateValue = false

    setQueryParams(prev => ({
      ...prev,
      validate: validateValue,
      offset: 0
    }))
  }

  const renderCustomFilters = () => (
    <FormControl size='small' sx={{ minWidth: 200 }}>
      <InputLabel id='validate-filter-label'>{t('tables.missionExpenseOther.filters.filter')}</InputLabel>
      <Select
        labelId='validate-filter-label'
        size='small'
        value={validateFilter}
        label={t('tables.missionExpenses.filters.validity')}
        onChange={handleValidateFilterChange}
      >
        <MenuItem value='all'>{t('tables.missionExpenses.filters.all')}</MenuItem>
        <MenuItem value='yes'>{t('tables.missionExpenses.filters.yes')}</MenuItem>
        <MenuItem value='no'>{t('tables.missionExpenses.filters.no')}</MenuItem>
      </Select>
    </FormControl>
  )

  return (
    <>
      <BaseTable
        data={data}
        isLoading={isLoading}
        isPending={isPending}
        isError={isError}
        error={error}
        defaultColDef={defaultColDef}
        queryParams={queryParams}
        setQueryParams={setQueryParams}
        columnDefs={columnDefs}
        module='financial'
        model='missionexpensesother'
        createButtonText={t('tables.missionExpenseOther.createButton') as string}
        title={t('tables.missionExpenseOther.title') as string}
        searchPlaceholder={t('tables.missionExpenseOther.searchPlaceholder') as string}
        onCreateClick={handleCreateClick}
        rowSelection='single'
        renderCustomFilters={renderCustomFilters}
      />
    </>
  )
}
