import React, { useMemo, useState } from 'react'
import { FetchFuelConsumptionParams } from 'src/types/models/fuel-consumption'
import { ColDef } from 'ag-grid-community'
import BaseTable from 'src/@core/components/tables/BaseTable'
import { useTranslation } from 'react-i18next'
import moment from 'moment'
import TableActions from 'src/@core/components/tables/TableActions'
import { useRouter } from 'next/navigation'
import {
  useDeleteFuelConsumption,
  useDeleteSelectedFuelConsumption,
  useFuelConsumptions
} from 'src/hooks/useFeulConsumption'
import { toast } from 'react-hot-toast'
import CustomDropdown from '../shared/components/custom-dropdown'
import { Can } from 'src/layouts/components/acl/Can'

export default function FuelConsumptionTable() {
  const { t } = useTranslation()
  const [selectedRows, setSelectedRows] = useState<any[]>([])
  const [queryParams, setQueryParams] = useState<Partial<FetchFuelConsumptionParams>>({
    limit: 100,
    offset: 0
  })

  const { data, isLoading, isError, error, isPending } = useFuelConsumptions(queryParams)
  const router = useRouter()
  const deleteFuelConsumption = useDeleteSelectedFuelConsumption({
    onSuccess: () => {
      toast.success(t('tables.fuelConsumption.deleteSuccess'))
    },
    onError: error => {
      toast.error(t('tables.fuelConsumption.deleteError'))
      console.error(error)
    }
  })

  const columnDefs = useMemo<ColDef[]>(
    () => [
      {
        headerName: t('tables.fuelConsumption.columns.code') as string,
        field: 'code',
        cellStyle: {
          fontWeight: 'bold'
        }
      },
      {
        headerName: t('tables.fuelConsumption.columns.createdAt') as string,
        field: 'created_at',
        valueFormatter: ({ value }: { value: any }) => {
          return moment(value).format('LL')
        }
      },
      {
        headerName: t('tables.fuelConsumption.columns.createdBy') as string,
        field: 'created_by',
        valueFormatter: ({ value }: { value: any }) => {
          return value ? value.first_name + ' ' + value.last_name : ''
        },
        cellStyle: {
          fontWeight: 'bold'
        }
      },
      {
        headerName: t('tables.fuelConsumption.columns.modifiedBy') as string,
        field: 'modified_by',
        valueFormatter: ({ value }: { value: any }) => {
          return value ? value.first_name + ' ' + value.last_name : ''
        },
        cellStyle: {
          fontWeight: 'bold'
        }
      },
      {
        headerName: t('tables.fuelConsumption.columns.modifiedAt') as string,
        field: 'modified_at',
        valueFormatter: ({ value }: { value: any }) => {
          return moment(value).format('LL')
        }
      },
      {
        headerName: t('common.actions.action') as string,
        field: 'actions',
        sortable: false,
        filter: false,
        width: 150,
        cellRenderer: (params: any) => (
          <TableActions
            id={params.data.id}
            basePath='/fuel/consumption'
            module='fuel'
            onDelete={handleDeleteClick}
            model='fuelconsumption'
            transLationKey='fuelConsumption'
          />
        )
      }
    ],
    [t]
  )

  const handleDeleteSelected = () => {
    const selectedIds = selectedRows.map(row => row.id)
    try {
      deleteFuelConsumption.mutate(selectedIds)
    } catch (error) {
      // Error is handled in the mutation's onError callback
    }
  }

  function handleDeleteClick(id: string) {
    deleteFuelConsumption.mutate([id])
  }

  const customActions = () => {
    return (
      <Can action={'delete'} model={'fuelconsumption'} module={'fuel'}>
        <CustomDropdown
          placeholder={t('common.actions.action') as string}
          buttonVariant='contained'
          buttonColor='primary'
          options={[
            {
              label: t('tables.fuelConsumption.deleteSelected'),
              icon: 'mdi:delete',
              onClick: handleDeleteSelected,
              disabled: selectedRows.length === 0
            }
          ]}
        />
      </Can>
    )
  }

  return (
    <BaseTable
      data={data}
      module='fuel'
      model='fuelconsumption'
      isLoading={isLoading}
      isPending={isPending}
      isError={isError}
      error={error}
      onSelectionChanged={setSelectedRows}
      defaultColDef={{
        sortable: true,
        resizable: true,
        filter: false,
        flex: 1
      }}
      createButtonText={t('tables.fuelConsumption.createButton') as string}
      onCreateClick={() => router.push('/fuel/consumption/create')}
      queryParams={queryParams}
      setQueryParams={setQueryParams}
      renderCustomActions={customActions}
      columnDefs={columnDefs}
      rowSelection='multiple'
      title={t('tables.fuelConsumption.title') as string}
      searchPlaceholder={t('tables.fuelConsumption.searchPlaceholder') as string}
    />
  )
}
