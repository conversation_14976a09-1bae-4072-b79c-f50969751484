import React, { useState } from 'react'
import { useTranslation } from 'react-i18next'
import { ColDef } from 'ag-grid-community'
import moment from 'moment'
import { useRouter } from 'next/router'
import BaseTable from 'src/@core/components/tables/BaseTable'
import TableActions from 'src/@core/components/tables/TableActions'
import { useBulkStationCreate, useDeleteSelectedStations, useFetchStations } from 'src/hooks/useStation'
import { FetchStationsParams } from 'src/types/models/_services/stations'
import { toast } from 'react-hot-toast'
import CustomDropdown from 'src/@core/shared/components/custom-dropdown'
import { Box } from '@mui/system'
import { Icon } from '@iconify/react'
import { Button } from '@mui/material'
import ImportCsv from 'src/@core/shared/components/import-csv'
import { useQueryClient } from '@tanstack/react-query'
import LoadingComponent from 'src/@core/shared/components/loading-component'

const StationTable = () => {
  const { t } = useTranslation()
  const router = useRouter()
  const queryClient = useQueryClient()
  const [selectedRow, setSelectedRow] = useState<any[]>([])
  const [importModalOpen, setImportModalOpen] = useState(false)

  const [queryParams, setQueryParams] = useState<Partial<FetchStationsParams>>({
    limit: 100,
    offset: 0
  })
  const { data, isLoading, isError, isPending } = useFetchStations(queryParams)
  const { mutate: bulkCreateStation, isPending: pendingBulkCreate } = useBulkStationCreate({
    onSuccess: () => {
      toast.success(t('tables.station.bulkCreateSuccess'))
      queryClient.invalidateQueries({ queryKey: ['stations'] })
    },
    onError: error => {
      toast.error(t('tables.station.bulkCreateError'))
      queryClient.invalidateQueries({ queryKey: ['stations'] })

      console.error(error)
    }
  })
  const deleteStation = useDeleteSelectedStations({
    onSuccess: () => {
      toast.success(t('tables.station.deleteSuccess'))
      queryClient.invalidateQueries({ queryKey: ['stations'] })
    },
    onError: error => {
      toast.error(t('tables.station.deleteError'))
      queryClient.invalidateQueries({ queryKey: ['stations'] })
      console.error(error)
    }
  })

  const columnDefs = React.useMemo<ColDef[]>(
    () => [
      {
        headerCheckboxSelection: true,
        checkboxSelection: true,
        width: 50,
        pinned: 'left'
      },
      {
        headerName: t('tables.station.columns.name') as string,
        field: 'station_name',
        cellStyle: {
          fontWeight: 'bold'
        }
      },
      {
        headerName: t('tables.station.columns.address') as string,
        field: 'station_localisation'
      },
      {
        headerName: t('tables.station.columns.email') as string,
        field: 'email'
      },
      {
        headerName: t('tables.station.columns.active') as string,
        field: 'active'
      },
      {
        headerName: t('tables.station.columns.createdAt') as string,
        field: 'created_at',
        valueFormatter: ({ value }: { value: string }) => {
          return moment(value).format('LL')
        }
      },
      {
        headerName: t('tables.station.columns.modifiedAt') as string,
        field: 'modified_at',
        valueFormatter: ({ value }: { value: string }) => {
          return moment(value).format('LL')
        }
      },
      {
        headerName: t('tables.station.columns.modifiedBy') as string,
        field: 'modified_by',
        valueFormatter: ({ value }: { value: any }) => {
          return value ? value.first_name + ' ' + value.last_name : ''
        }
      },
      {
        headerName: t('common.actions.action') as string,
        field: 'actions',
        sortable: false,
        filter: false,
        width: 150,
        cellRenderer: (params: any) => (
          <TableActions
            id={params.data.id}
            basePath='/fuel/stations'
            viewAction={false}
            onDelete={handleDeleteClick}
            module='fuel'
            model='station'
          />
        )
      }
    ],
    [t]
  )

  const handleSelectionChanged = (selectedRows: any[]) => {
    setSelectedRow(selectedRows)
  }

  const handleDeleteSelected = async () => {
    const selectedIds = selectedRow.map(row => row.id)
    try {
      await deleteStation.mutateAsync(selectedIds)
    } catch (error) {}
  }

  //   const handleExportCSV = () => {
  //     // Implement CSV export logic here
  //     console.log('Exporting to CSV...')
  //   }

  const customActions = () => {
    return (
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
        <CustomDropdown
          placeholder={t('common.actions.action') as string}
          buttonVariant='contained'
          buttonColor='primary'
          options={[
            {
              label: t('common.actions.deleteSelected'),
              icon: 'mdi:delete',
              onClick: handleDeleteSelected,
              disabled: selectedRow.length === 0
            }
          ]}
        />

        <Button
          variant='outlined'
          startIcon={<Icon icon='mdi:file-import' />}
          sx={{ ml: 2 }}
          onClick={() => setImportModalOpen(true)}
        >
          {t('common.actions.import')}
        </Button>
      </Box>
    )
  }

  const handleDeleteClick = (id: string) => {
    deleteStation.mutateAsync([id])
  }

  if (pendingBulkCreate) return <LoadingComponent message='Importing Stations..........' />

  return (
    <>
      <BaseTable
        data={data}
        module='fuel'
        model='station'
        isLoading={isLoading}
        isPending={isPending}
        isError={isError}
        error={isError}
        queryParams={queryParams}
        rowSelection='multiple'
        onSelectionChanged={handleSelectionChanged}
        setQueryParams={setQueryParams}
        createButtonText={t('tables.station.createButton') as string}
        onCreateClick={() => router.push('/fuel/stations/create')}
        columnDefs={columnDefs}
        defaultColDef={{
          sortable: true,
          resizable: true,
          filter: false,
          flex: 1
        }}
        onExportClick={api => {
          api.exportDataAsCsv({
            fileName: `stations-${new Date().toISOString()}.csv`
          })
        }}
        title={t('tables.station.title') as string}
        searchPlaceholder={t('tables.station.searchPlaceholder') as string}
        renderCustomActions={customActions}
      />

      <ImportCsv
        isOpen={importModalOpen}
        onClose={() => setImportModalOpen(false)}
        onComplete={data => {
          const payload = data.rows.map((r: any) => r.values)
          bulkCreateStation({ payload })
          setImportModalOpen(false)
        }}
        columns={[
          {
            name: 'Station Name',
            key: 'station_name',
            required: true
          },
          {
            name: 'Station Address',
            key: 'station_localisation',
            required: true
          },
          {
            name: 'Email',
            key: 'email',
            required: true
          },
          {
            name: 'Active',
            key: 'active',
            required: true
          }
        ]}
      />
    </>
  )
}

export default StationTable
