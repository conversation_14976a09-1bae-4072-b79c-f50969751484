import React, { useMemo, useState } from 'react'
import { useFetchPermissions } from 'src/hooks/useFetchPermission'
import { BaseFetchParams } from 'src/types/models'
import { ColDef } from 'ag-grid-community'
import BaseTable from '../components/tables/BaseTable'
import { useGroups } from 'src/hooks/useGroups'
import { useRouter } from 'next/navigation'
import { useTranslation } from 'react-i18next'
import TableActions from '../components/tables/TableActions'

export default function GroupsTable() {
  const [queryParams, setQueryParams] = useState<Partial<BaseFetchParams>>({
    limit: 100,
    offset: 0
  })
  const router = useRouter()

  const { data, isLoading, isError, isPending } = useGroups(queryParams)
  const { t } = useTranslation()
  const colDef = useMemo<ColDef[]>(
    () => [
      {
        headerCheckboxSelection: true,
        checkboxSelection: true,
        width: 200,
        pinned: 'left'
      },
      {
        headerName: 'Group',
        field: 'name'
      },
      {
        headerName: t('common.actions.action') as string,
        cellRenderer: (params: any) => {
          return (
            <TableActions
              onDelete={id => {
                console.log(id)
              }}
              module=''
              model=''
              viewAction={false}
              id={params.data.name}
              basePath={'/user-management/groups'}
            />
          )
        }
      }
    ],
    [t]
  )

  return (
    <BaseTable
      data={data}
      module='auth'
      model='group'
      columnDefs={colDef}
      isError={isError}
      isLoading={isLoading}
      isPending={isPending}
      defaultColDef={{
        filter: false,
        sortable: false,
        flex: 1
      }}
      searchPlaceholder=''
      createButtonText='Add Group'
      onCreateClick={() => router.push('/user-management/groups/create')}
      setQueryParams={setQueryParams}
      title='Groups'
      queryParams={queryParams}
    />
  )
}
