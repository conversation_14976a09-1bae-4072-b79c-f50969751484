import type React from 'react'
import { useState, useMemo, useCallback, useRef } from 'react'
import { AgGridReact } from 'ag-grid-react'
import {
  AllCommunityModule,
  ModuleRegistry,
  GridApi,
  GridReadyEvent,
  ColDef,
  FilterChangedEvent,
  CellClickedEvent,
  RowClickedEvent,
  CellDoubleClickedEvent
} from 'ag-grid-community'

import {
  Box,
  TextField,
  Button,
  CircularProgress,
  Typography,
  Snackbar,
  Alert,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  type SelectChangeEvent,
  Pagination,
  useTheme,
  ToggleButtonGroup,
  Grid,
  Chip
} from '@mui/material'
import { Icon } from '@iconify/react'
import debounce from 'lodash/debounce'
import moment from 'moment'
import { useGetAllVehicles } from '../../hooks/useVehicle'
import ToggleButton from '@mui/material/ToggleButton'
import { FetchVehiclesParams, Vehicle } from '../../types/models/vehicles'
import useTableTheme from '../../hooks/useTableTheme'
import { useRouter } from 'next/navigation'
import { VehicleCard } from '../vehicle/vehicle-card'
import LoadingComponent from '../shared/components/loading-component'
import FilterModal from 'src/@core/components/filters/FilterModal'
import ImportCsv from 'src/@core/shared/components/import-csv'
import { Can } from 'src/layouts/components/acl/Can'
import { useTranslation } from 'react-i18next'

ModuleRegistry.registerModules([AllCommunityModule])
const vehicleTypes = [
  { value: 'REMORQUE', label: 'REMORQUE' },
  { value: 'CAMION', label: 'CAMION' },
  { value: 'TRACTEUR', label: 'TRACTEUR' },
  { value: 'PICKUP', label: 'PICKUP' },
  { value: 'HEAD_OFFICE', label: 'HEAD_OFFICE' }
]
const containerShape = [
  { label: 'Citerne', value: 'CITERNE' },
  { label: 'Plateaux', value: 'PLATEAU' },
  { label: 'Bennes', value: 'BENNE' },
  { label: 'Kangoo', value: 'KANGOO' },
  { label: 'Pickup', value: 'PIK-UP' },
  { label: 'Bennes et Plateaux', value: 'BENNE_PLATEAU' },
  { label: "Fourgonnette d'intervention", value: 'FOURGONNETTE_INTERVENTION' }
]
const VehicleTable = () => {
  const [gridApi, setGridApi] = useState<GridApi | null>(null)
  const [selectedRows, setSelectedRows] = useState<Vehicle[]>([])
  const router = useRouter()
  const [queryParams, setQueryParams] = useState<Partial<FetchVehiclesParams>>({
    limit: 100,
    offset: 0
  })
  const [viewMode, setViewMode] = useState<'table' | 'grid'>('table')
  const [filterModalOpen, setFilterModalOpen] = useState(false)
  const [activeFilters, setActiveFilters] = useState<Record<string, any>>({})
  const [importModalOpen, setImportModalOpen] = useState(false)

  const theme = useTheme()
  const { data, isLoading, isError, error } = useGetAllVehicles(queryParams)
  const { tableTheme } = useTableTheme()
  const { t } = useTranslation()
  const statusRenderer = (params: any) => (
    <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
      {params.value ? (
        <Icon icon='mdi:check-circle' color={theme.palette.success.main} fontSize={20} />
      ) : (
        <Icon icon='mdi:close-circle' color={theme.palette.error.main} fontSize={20} />
      )}
    </Box>
  )

  const columnDefs = useMemo<ColDef[]>(
    () => [
      {
        headerCheckboxSelection: true,
        checkboxSelection: true,
        width: 50,
        pinned: 'left'
      },
      {
        field: 'code',
        headerName: 'Code'
      },
      {
        field: 'number_plate',
        headerName: 'Number Plate'
      },
      { field: 'vehicle_type', headerName: 'Vehicle Type' },
      {
        field: 'container',
        headerName: 'Container'
      },
      {
        field: 'kilometer_old',
        headerName: 'Old Km'
      },
      {
        field: 'kilometer_new',
        headerName: 'New Km',

        valueFormatter: ({ value }: { value: number }) => value?.toLocaleString()
      },
      {
        field: 'petrol_consumption',
        headerName: 'Petrol Consumption'
      },
      {
        field: 'is_active',
        headerName: 'Active',
        cellRenderer: statusRenderer,
        width: 100
      },
      {
        field: 'created_at',
        headerName: 'Created At',

        valueFormatter: ({ value }: { value: string }) => {
          return moment(value).format('ll')
        }
      },
      {
        field: 'modified_at',
        headerName: 'Modified At',

        valueFormatter: ({ value }: { value: string }) => {
          return moment(value).format('ll')
        }
      }
    ],
    []
  )

  const defaultColDef = useMemo(
    () => ({
      flex: 1,
      minWidth: 100,
      filter: false,
      sortable: true,
      resizable: true
    }),
    []
  )

  const onGridReady = useCallback((params: GridReadyEvent) => {
    setGridApi(params.api)
  }, [])

  const handleSearch = debounce((value: string) => {
    setQueryParams({ search: value })
  }, 300)

  const handleFilterTypeChange = (event: SelectChangeEvent) => {
    const value = event.target.value as string
    setQueryParams(prev => ({ ...prev, vehicle_type: value }))
  }
  const handleFilterContainerTypeChange = (event: SelectChangeEvent) => {
    const value = event.target.value as string
    setQueryParams(prev => ({ ...prev, container: value }))
  }

  const onPageChange = (event: React.ChangeEvent<unknown>, value: number) => {
    setQueryParams(prev => ({ ...prev, offset: (value - 1) * prev.limit! }))
  }

  const onSelectionChanged = useCallback(() => {
    if (gridApi) {
      const selectedNodes = gridApi.getSelectedNodes()
      const selectedData = selectedNodes.map(node => node.data)
      setSelectedRows(selectedData)
    }
  }, [gridApi])

  const handleExport = useCallback(() => {
    if (gridApi) {
      gridApi.exportDataAsCsv({
        fileName: `vehicles-${new Date().toISOString()}.csv`
      })
    }
  }, [gridApi])

  const handleViewModeChange = (event: React.MouseEvent<HTMLElement>, newMode: 'table' | 'grid') => {
    if (newMode !== null) {
      setViewMode(newMode)
    }
  }

  const handleRowClick = (params: RowClickedEvent) => {
    router.push(`/vehicles/${params.data.id}/detail`)
  }

  const handleCreateVehicle = () => {
    router.push('/vehicles/create')
  }

  const availableFilters = [
    {
      field: 'vehicle_type',
      label: 'Vehicle Type',
      type: 'select' as const,
      options: vehicleTypes.map(v_ => ({
        value: v_.value,
        label: v_.label
      }))
    },
    {
      field: 'container',
      label: 'Container Shape',
      type: 'select' as const,
      options: containerShape.map(c_ => ({
        value: c_.value,
        label: c_.label
      }))
    },
    {
      field: 'is_active',
      label: 'Active',
      type: 'select' as const,
      options: [
        { value: 'true', label: 'Active' },
        { value: 'false', label: 'Inactive' }
      ]
    },
    {
      field: 'search',
      label: 'Search',
      type: 'text' as const
    }
  ]

  const handleFilterApply = (filters: Record<string, any>) => {
    setQueryParams(prev => ({
      ...prev,
      vehicle_type: filters.vehicle_type === 'all' ? '' : filters.vehicle_type,
      container: filters.container === 'all' ? '' : filters.container,
      is_active: filters.is_active === 'all' ? undefined : filters.is_active === 'true',
      search: filters.search || ''
    }))
    setActiveFilters(filters)
  }

  return (
    <Box sx={{ p: 3 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 3, gap: 2 }}>
        <Box sx={{ display: 'flex', gap: 2 }}>
          <Button variant='outlined' startIcon={<Icon icon='mdi:filter' />} onClick={() => setFilterModalOpen(true)}>
            Filters
            {Object.keys(activeFilters).length > 0 && (
              <Chip size='small' label={Object.keys(activeFilters).length} color='primary' sx={{ ml: 1 }} />
            )}
          </Button>

          {/* Show active filters in horizontal layout */}
          {Object.keys(activeFilters).length > 0 && (
            <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap', alignItems: 'center' }}>
              {Object.entries(activeFilters).map(([field, value]) => {
                if (value && value !== 'all') {
                  const filterDef = availableFilters.find(f => f.field === field)
                  const label = filterDef?.options?.find(opt => opt.value === value)?.label || value

                  return (
                    <Chip
                      key={field}
                      label={`${filterDef?.label || field}: ${label}`}
                      onDelete={() => {
                        const newFilters = { ...activeFilters }
                        delete newFilters[field]
                        handleFilterApply(newFilters)
                      }}
                      size='small'
                      color='primary'
                      variant='outlined'
                    />
                  )
                }

                return null
              })}
            </Box>
          )}
        </Box>

        <Box sx={{ display: 'flex', gap: 2 }}>
          <ToggleButtonGroup value={viewMode} exclusive onChange={handleViewModeChange} size='medium'>
            <ToggleButton value='table'>
              <Icon icon='mdi:table' />
            </ToggleButton>
            <ToggleButton value='grid'>
              <Icon icon='mdi:grid' />
            </ToggleButton>
          </ToggleButtonGroup>

          <Button
            variant='contained'
            startIcon={<Icon icon='mdi:download' />}
            onClick={handleExport}
            disabled={!data?.results?.length}
          >
            {t('common.actions.exportCsv')}
          </Button>

          <Button
            variant='contained'
            startIcon={<Icon icon='mdi:download' />}
            onClick={() => setImportModalOpen(true)}

            // disabled={!data?.results?.length}
          >
            {t('common.actions.import')}
          </Button>
          <Can action={'create'} module={'vehicle'} model={'vehicle'}>
            <Button
              variant='contained'
              startIcon={<Icon icon='mdi:plus' />}
              color='success'
              onClick={handleCreateVehicle}
            >
              Add Vehicle
            </Button>
          </Can>
        </Box>
      </Box>

      <FilterModal
        open={filterModalOpen}
        onClose={() => setFilterModalOpen(false)}
        onApply={handleFilterApply}
        initialFilters={activeFilters}
        availableFilters={availableFilters}
      />

      {/* change  view mode */}
      {viewMode === 'table' ? (
        <Box sx={{ height: 600, width: '100%', position: 'relative' }}>
          {isLoading && (
            <Box
              sx={{
                position: 'absolute',
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                backgroundColor: 'rgba(255, 255, 255, 0.7)',
                zIndex: 1
              }}
            >
              <CircularProgress />
            </Box>
          )}
          <div style={{ height: '100%', width: '100%' }}>
            <AgGridReact
              rowData={data?.results}
              columnDefs={columnDefs}
              defaultColDef={defaultColDef}
              onGridReady={onGridReady}
              pagination={true}
              onRowClicked={handleRowClick}
              paginationPageSize={queryParams.limit}
              loading={isLoading}
              suppressPaginationPanel={true}
              rowSelection='multiple'
              onSelectionChanged={onSelectionChanged}
              enableCellTextSelection={true}
              theme={tableTheme}
              quickFilterText={queryParams.search}
            />
          </div>
        </Box>
      ) : (
        <>
          {isLoading ? (
            <LoadingComponent />
          ) : (
            <Grid container spacing={3}>
              {data?.results.map((vehicle: Vehicle) => (
                <Grid item xs={12} sm={6} md={4} lg={3} key={vehicle.id}>
                  <VehicleCard vehicle={vehicle} />
                </Grid>
              ))}
            </Grid>
          )}
        </>
      )}

      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mt: 2 }}>
        <Typography variant='body2'>{selectedRows.length} rows selected</Typography>
        <Pagination
          count={Math.ceil((data?.count || 0) / queryParams.limit!)}
          page={Math.floor(queryParams.offset! / queryParams.limit!) + 1}
          onChange={onPageChange}
          color='primary'
        />
      </Box>
      <ImportCsv
        isOpen={importModalOpen}
        onClose={() => setImportModalOpen(false)}
        onComplete={data => {
          console.log(data)
        }}
        columns={[
          {
            name: 'Code',
            key: 'code',
            required: true
          },
          {
            name: 'Number Plate',
            key: 'number_plate',
            required: true
          },
          {
            name: 'Vehicle Type',
            key: 'vehicle_type',
            required: true
          },
          {
            name: 'Container',
            key: 'container',
            required: true
          },
          {
            name: 'Kilometer Old',
            key: 'kilometer_old',
            required: true
          },
          {
            name: 'Kilometer New',
            key: 'kilometer_new',
            required: true
          },
          {
            name: 'Petrol Consumption',
            key: 'petrol_consumption',
            required: true
          }
        ]}
      />

      <Snackbar open={isError} autoHideDuration={6000}>
        <Alert severity='error' sx={{ width: '100%' }}>
          {error instanceof Error ? error.message : 'An error occurred while fetching data'}
        </Alert>
      </Snackbar>
    </Box>
  )
}

export default VehicleTable
