import React, { useMemo, useState } from 'react'
import { BaseFetchParams } from 'src/types/models'
import { ColDef } from 'ag-grid-community'
import { BaseTable } from 'src/@core/components/tables/BaseTable'
import { useFuelConsumptionPartitions } from 'src/hooks/useFuelConsumptionPartition'
import { useDeleteSelectedFuelConsumption } from 'src/hooks/useFeulConsumption'
import toast from 'react-hot-toast'
import CustomDropdown from '../shared/components/custom-dropdown'
import { Can } from 'src/layouts/components/acl/Can'
import { useTranslation } from 'react-i18next'

export default function FuelConsumptionOperationTable() {
  const { t } = useTranslation()
  const [queryParams, setQueryParams] = useState<Partial<BaseFetchParams>>({
    limit: 100,
    offset: 0
  })

  const [selectedRows, setSelectedRows] = useState<any[]>([])

  const { data, isLoading, isError, error, isPending } = useFuelConsumptionPartitions(queryParams)
  const deleteSelectedFuelConsumption = useDeleteSelectedFuelConsumption({
    onSuccess: () => {
      toast.success('Success in deleting fuel consumption')
    },
    onError: error => {
      toast.error(error.message)
    }
  })

  const handleDeleteSelected = () => {
    const selectedIds = selectedRows.map(row => row.id)
    try {
      deleteSelectedFuelConsumption.mutate(selectedIds)
    } catch (error) {
      // Error is handled in the mutation's onError callback
    }
  }

  const customActions = () => {
    return (
      <Can action={'delete'} model={'fuelconsumption'} module={'fuel'}>
        <CustomDropdown
          placeholder={t('common.actions.action') as string}
          buttonVariant='contained'
          buttonColor='primary'
          options={[
            {
              label: t('tables.fuelConsumption.deleteSelected'),
              icon: 'mdi:delete',
              onClick: handleDeleteSelected,
              disabled: selectedRows.length === 0
            }
          ]}
        />
      </Can>
    )
  }

  const columnDefs = useMemo<ColDef[]>(
    () => [
      {
        headerName: 'Code',
        field: 'code'
      },
      {
        headerName: 'Label',
        field: 'label'
      },
      {
        headerName: 'Volume',
        field: 'petrol_volume'
      }
    ],
    []
  )

  return (
    <BaseTable
      data={data}
      module='fuel'
      model='fuelconsumptionpartition'
      columnDefs={columnDefs}
      isError={isError}
      isLoading={isLoading}
      isPending={isPending}
      error={error}
      rowSelection='multiple'
      onSelectionChanged={setSelectedRows}
      defaultColDef={{
        filter: false,
        sortable: false,
        flex: 1
      }}
      setQueryParams={setQueryParams}
      title='Opération'
      queryParams={queryParams}
      renderCustomActions={customActions}
    />
  )
}
