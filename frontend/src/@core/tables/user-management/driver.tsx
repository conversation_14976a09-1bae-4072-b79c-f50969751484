import { useTranslation } from 'react-i18next'
import { useState, useMemo } from 'react'
import { useRouter } from 'next/router'
import TableActions from 'src/@core/components/tables/TableActions'
import { ColDef } from 'ag-grid-community'
import BaseTable from 'src/@core/components/tables/BaseTable'
import { MODELS, MODULES } from 'src/layouts/components/acl/can'
import moment from 'moment'
import toast from 'react-hot-toast'
import { FetchDriverParams } from 'src/types/models/drivers'
import { useDeleteSelectedDrivers, useDrivers } from 'src/hooks/user-management/useDrivers'
import { Button } from '@mui/material'
import { useQueryClient } from '@tanstack/react-query'
import { Icon } from '@iconify/react'
import CustomDropdown from 'src/@core/shared/components/custom-dropdown'
import { Can } from 'src/layouts/components/acl/Can'

export default function DriverTable() {
  const [queryParams, setQueryParams] = useState<Partial<FetchDriverParams>>({
    limit: 100,
    offset: 0
  })
  const queryClient = useQueryClient()
  const [selectedRows, setSelectedRows] = useState<any[]>([])

  const router = useRouter()
  const { t } = useTranslation()
  const { data, isLoading, isError, isPending } = useDrivers(queryParams)
  const { mutateAsync: deleteDrivers, isPending: deletingDrivers } = useDeleteSelectedDrivers({
    onSuccess: () => {
      toast.success(t('tables.driver.deleteSuccess'))
      queryClient.invalidateQueries({ queryKey: ['drivers'] })
    },
    onError: error => {
      toast.error(t('tables.driver.deleteError'))
      console.error(error)
    }
  })

  const handleSelectionChanged = (selectedRows: any[]) => {
    setSelectedRows(selectedRows)
  }

  const handleDeleteSelected = async () => {
    const selectedIds = selectedRows.map(row => row.id)
    try {
      await deleteDrivers(selectedIds)
    } catch (error) {
      // Error is handled in the mutation's onError callback
    }
  }
  const customActions = () => {
    return (
      <Can action={'delete'} model={MODELS[MODULES.APP_AUTHENTICATION].DRIVER} module={MODULES.APP_AUTHENTICATION}>
        <CustomDropdown
          placeholder={t('common.actions.action') as string}
          buttonVariant='contained'
          buttonColor='primary'
          options={[
            {
              label: t('tables.driver.deleteSelected'),
              icon: 'mdi:delete',
              onClick: handleDeleteSelected,
              disabled: selectedRows.length === 0
            }
          ]}
        />
      </Can>
    )
  }

  const columns = useMemo<ColDef[]>(
    () => [
      {
        headerCheckboxSelection: true,
        checkboxSelection: true,
        width: 50,
        pinned: 'left'
      },
      {
        field: 'matricule',
        headerName: 'Matricule',
        width: 100
      },

      {
        field: 'first_name',
        headerName: t('tables.driver.columns.firstName') as string,
        width: 200
      },
      {
        field: 'last_name',
        headerName: t('tables.driver.columns.lastName') as string
      },

      {
        field: 'fonction',
        headerName: t('tables.driver.columns.fonction') as string
      },
      {
        field: 'dated_embauche_societe',
        headerName: t('tables.driver.columns.datedEmbaucheSociete') as string,
        valueFormatter: params => {
          return params.value ? moment(params.value).format('LL') : '---'
        }
      },
      {
        field: 'age',
        headerName: t('tables.driver.columns.age') as string,
        width: 100,
        valueGetter: params => {
          return params.data.date_de_naissance ? moment().diff(moment(params.data.date_de_naissance), 'years') : '---'
        }
      },
      {
        field: 'dated_embauche_societe',
        headerName: t('tables.driver.columns.datedEmbaucheSociete') as string,
        valueFormatter: params => {
          return params.value ? moment(params.value).format('LL') : '---'
        }
      },
      {
        field: 'sexe',
        width: 100,

        headerName: t('tables.driver.columns.sexe') as string
      },

      {
        field: 'address',
        headerName: t('tables.driver.columns.address') as string,
        cellRenderer: (params: any) => {
          return params.value ? params.value : '---'
        }
      },
      {
        field: 'phone_number',
        headerName: t('tables.driver.columns.phoneNumber') as string,
        cellRenderer: (params: any) => {
          return params.value ? `+237 ${params.value}` : '---'
        }
      },
      {
        field: 'created_at',
        headerName: t('tables.driver.columns.createdAt') as string,
        valueFormatter: params => {
          return params.value ? moment(params.value).format('LL') : '---'
        }
      },
      {
        field: 'modified_by',
        headerName: t('tables.driver.columns.modifiedBy') as string,
        valueFormatter: params => {
          return params.value ? `${params.value.first_name} ${params.value.last_name}` : '---'
        }
      },
      {
        field: 'modified_at',
        headerName: t('tables.driver.columns.modifiedAt') as string,
        valueFormatter: params => {
          return params.value ? moment(params.value).format('LL') : '---'
        }
      },
      {
        field: 'actions',
        headerName: t('common.actions.action') as string,

        pinned: 'right',
        cellRenderer: (params: any) => (
          <TableActions
            id={params.data.id}
            basePath='/user-management/drivers'
            onDelete={handleDeleteClick}
            viewAction={false}
            transLationKey='driver'
            module={MODULES.APP_AUTHENTICATION}
            model={MODELS[MODULES.APP_AUTHENTICATION].DRIVER}
          />
        )
      }
    ],
    [t]
  )

  const handleDeleteClick = async (id: string) => {
    try {
      await deleteDrivers([id])
    } catch (error) {
      // Error is handled in the mutation's onError callback
    }
  }

  const handleCreateClick = () => {
    router.push('/user-management/drivers/create')
  }

  return (
    <BaseTable
      data={data}
      module={MODULES.APP_AUTHENTICATION}
      model={MODELS[MODULES.APP_AUTHENTICATION].DRIVER}
      isLoading={isLoading}
      isPending={isPending}
      isError={isError}
      error={isError}
      queryParams={queryParams}
      rowSelection='multiple'
      setQueryParams={setQueryParams}
      createButtonText={t('tables.driver.createButton') as string}
      columnDefs={columns}
      defaultColDef={{
        resizable: true,
        minWidth: 20,

        tooltipComponent: 'customTooltip'
      }}
      title={t('tables.driver.title') as string}
      searchPlaceholder={t('tables.driver.searchPlaceholder') as string}
      onCreateClick={handleCreateClick}
      onSelectionChanged={handleSelectionChanged}
      renderCustomActions={customActions}
    />
  )
}
