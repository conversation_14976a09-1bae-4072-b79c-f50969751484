import { useTranslation } from 'react-i18next'
import { useState, useMemo } from 'react'
import { useRouter } from 'next/router'
import TableActions from 'src/@core/components/tables/TableActions'
import { ColDef } from 'ag-grid-community'
import BaseTable from 'src/@core/components/tables/BaseTable'
import { MODELS, MODULES } from 'src/layouts/components/acl/can'
import moment from 'moment'
import toast from 'react-hot-toast'
import { Button } from '@mui/material'
import { useQueryClient } from '@tanstack/react-query'
import { useCustomers, useDeleteSelectedCustomers } from 'src/hooks/useCustomer'
import { CustomerParams } from 'src/types/models/customer'
import { Icon } from '@iconify/react'
import CustomDropdown from 'src/@core/shared/components/custom-dropdown'
import { Can } from 'src/layouts/components/acl/Can'

export default function CustomersTable() {
  const [queryParams, setQueryParams] = useState<Partial<CustomerParams>>({
    limit: 100,
    offset: 0
  })
  const queryClient = useQueryClient()
  const [selectedRows, setSelectedRows] = useState<any[]>([])

  const router = useRouter()
  const { t } = useTranslation()
  const { data, isLoading, isError, isPending } = useCustomers(queryParams)
  const { mutateAsync: deleteCustomers, isPending: deletingCustomers } = useDeleteSelectedCustomers({
    onSuccess: () => {
      toast.success(t('tables.customer.deleteSuccess'))
      queryClient.invalidateQueries({ queryKey: ['customers'] })
    },
    onError: error => {
      toast.error(t('tables.customer.deleteError'))
      console.error(error)
    }
  })

  const handleSelectionChanged = (selectedRows: any[]) => {
    setSelectedRows(selectedRows)
  }

  const handleDeleteSelected = async () => {
    const selectedIds = selectedRows.map(row => row.id)
    try {
      await deleteCustomers(selectedIds)
    } catch (error) {
      // Error is handled in the mutation's onError callback
    }
  }

  const customActions = () => {
    return (
      <Can action={'delete'} model={MODELS[MODULES.APP_AUTHENTICATION].CUSTOMER} module={MODULES.APP_AUTHENTICATION}>
        <CustomDropdown
          placeholder={t('common.actions.action') as string}
          buttonVariant='contained'
          buttonColor='primary'
          options={[
            {
              label: t('tables.customer.deleteSelected'),
              icon: 'mdi:delete',
              onClick: handleDeleteSelected,
              disabled: selectedRows.length === 0
            }
          ]}
        />
      </Can>
    )
  }

  const columns = useMemo<ColDef[]>(
    () => [
      {
        headerCheckboxSelection: true,
        checkboxSelection: true,
        width: 50,
        pinned: 'left'
      },
      {
        field: 'sap_uid',
        headerName: t('tables.customer.columns.externalId') as string
      },
      {
        field: 'name',
        headerName: t('tables.customer.columns.name') as string
      },
      {
        field: 'address',
        headerName: t('tables.customer.columns.address') as string
      },
      {
        field: 'phone1',
        headerName: t('tables.customer.columns.phone') as string
      },
      {
        field: 'mailaddres',
        headerName: t('tables.customer.columns.email') as string,
        valueFormatter: params => {
          return params.value ? params.value : '---'
        }
      },

      {
        field: 'modified_by',
        headerName: t('tables.customer.columns.modifiedBy') as string,
        valueFormatter: params => {
          return params.value ? `${params.value.first_name} ${params.value.last_name}` : '---'
        }
      },
      {
        field: 'modified_at',
        headerName: t('tables.customer.columns.modifiedAt') as string,
        valueFormatter: params => {
          return params.value ? moment(params.value).format('LL') : '---'
        }
      },
      {
        field: 'actions',
        headerName: t('common.actions.action') as string,
        pinned: 'right',
        cellRenderer: (params: any) => (
          <TableActions
            id={params.data.id}
            basePath='/user-management/customers'
            onDelete={handleDeleteClick}
            viewAction={false}
            transLationKey='customer'
            module={MODULES.APP_AUTHENTICATION}
            model={MODELS[MODULES.APP_AUTHENTICATION].CUSTOMER}
          />
        )
      }
    ],
    [t]
  )

  const handleDeleteClick = async (id: string) => {
    try {
      await deleteCustomers([id])
    } catch (error) {
      // Error is handled in the mutation's onError callback
    }
  }

  const handleCreateClick = () => {
    router.push('/user-management/customers/create')
  }

  return (
    <BaseTable
      data={data}
      module={MODULES.APP_AUTHENTICATION}
      model={MODELS[MODULES.APP_AUTHENTICATION].CUSTOMER}
      isLoading={isLoading}
      isPending={isPending}
      isError={isError}
      error={isError}
      queryParams={queryParams}
      rowSelection='multiple'
      setQueryParams={setQueryParams}
      createButtonText={t('tables.customer.createButton') as string}
      columnDefs={columns}
      defaultColDef={{
        resizable: true,
        minWidth: 20,

        tooltipComponent: 'customTooltip'
      }}
      title={t('tables.customer.title') as string}
      searchPlaceholder={t('tables.customer.searchPlaceholder') as string}
      onCreateClick={handleCreateClick}
      onSelectionChanged={handleSelectionChanged}
      renderCustomActions={customActions}
    />
  )
}
