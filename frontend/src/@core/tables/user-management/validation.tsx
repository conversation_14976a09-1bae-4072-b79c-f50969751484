import { useTranslation } from 'react-i18next'
import { useState, useMemo } from 'react'
import { useRouter } from 'next/router'
import TableActions from 'src/@core/components/tables/TableActions'
import { ColDef } from 'ag-grid-community'
import BaseTable from 'src/@core/components/tables/BaseTable'
import { MODELS, MODULES } from 'src/layouts/components/acl/can'
import moment from 'moment'
import toast from 'react-hot-toast'
import { Button } from '@mui/material'
import { Icon } from '@iconify/react'
import { useQueryClient } from '@tanstack/react-query'
import { BaseFetchParams } from 'src/types/models'
import { useDeleteValidation, useValidation } from 'src/hooks/user-management/useValidation'
import CustomDropdown from 'src/@core/shared/components/custom-dropdown'
import { Can } from 'src/layouts/components/acl/Can'

export default function ValidationTable() {
  const [queryParams, setQueryParams] = useState<Partial<BaseFetchParams>>({
    limit: 100,
    offset: 0
  })
  const queryClient = useQueryClient()
  const [selectedRows, setSelectedRows] = useState<any[]>([])

  const router = useRouter()
  const { t } = useTranslation()
  const { data, isLoading, isError, isPending } = useValidation(queryParams)
  const { mutateAsync: deleteValidations, isPending: deletingValidations } = useDeleteValidation({
    onSuccess: () => {
      toast.success(t('tables.validation.deleteSuccess'))
      queryClient.invalidateQueries({ queryKey: ['validations'] })
    },
    onError: () => {
      toast.error(t('tables.validation.deleteError'))
    }
  })

  const handleSelectionChanged = (selectedRows: any[]) => {
    setSelectedRows(selectedRows)
  }

  const handleDeleteSelected = async () => {
    const selectedIds = selectedRows.map(row => row.id)
    try {
      await deleteValidations(selectedIds)
    } catch (error) {
      // Error is handled in the mutation's onError callback
    }
  }

  const customActions = () => {
    return (
      <Can action={'delete'} model={MODELS[MODULES.APP_AUTHENTICATION].VALIDATIONS} module={MODULES.APP_AUTHENTICATION}>
        <CustomDropdown
          placeholder={t('common.actions.action') as string}
          buttonVariant='contained'
          buttonColor='primary'
          options={[
            {
              label: t('tables.users.deleteSelected'),
              icon: 'mdi:delete',
              onClick: handleDeleteSelected,
              disabled: selectedRows.length === 0
            }
          ]}
        />
      </Can>
    )
  }

  const columns = useMemo<ColDef[]>(
    () => [
      {
        headerCheckboxSelection: true,
        checkboxSelection: true,
        width: 50,
        pinned: 'left'
      },
      {
        field: 'data_type',
        headerName: t('tables.validation.columns.dataType') as string,
        width: 200,
        cellStyle: {
          fontWeight: 'bold'
        }
      },
      {
        field: 'level',
        headerName: t('tables.validation.columns.validationLevels') as string,
        width: 200
      },
      {
        field: 'modified_by',
        headerName: t('tables.validation.columns.modifiedBy') as string,
        valueFormatter: params => {
          return params.value ? `${params.value.first_name} ${params.value.last_name}` : '---'
        }
      },
      {
        field: 'modified_at',
        headerName: t('tables.validation.columns.modifiedAt') as string,
        valueFormatter: params => {
          return params.value ? moment(params.value).format('LL') : '---'
        }
      },
      {
        field: 'actions',
        headerName: t('common.actions.action') as string,
        cellRenderer: (params: any) => (
          <TableActions
            id={params.data.id}
            basePath='/user-management/validations'
            onDelete={handleDeleteClick}
            viewAction={false}
            transLationKey='validation'
            module={MODULES.APP_AUTHENTICATION}
            model={MODELS[MODULES.APP_AUTHENTICATION].VALIDATIONS}
          />
        )
      }
    ],
    [t]
  )

  const handleDeleteClick = async (id: string) => {
    try {
      await deleteValidations([id])
    } catch (error) {
      // Error is handled in the mutation's onError callback
    }
  }

  const handleCreateClick = () => {
    router.push('/user-management/validations/create')
  }

  return (
    <BaseTable
      data={data}
      module={MODULES.APP_AUTHENTICATION}
      model={MODELS[MODULES.APP_AUTHENTICATION].VALIDATIONS}
      isLoading={isLoading}
      isPending={isPending}
      isError={isError}
      error={isError}
      queryParams={queryParams}
      rowSelection='multiple'
      setQueryParams={setQueryParams}
      createButtonText={t('tables.validation.createButton') as string}
      columnDefs={columns}
      defaultColDef={{
        resizable: true,
        minWidth: 20,
        tooltipComponent: 'customTooltip'
      }}
      title={t('tables.validation.title') as string}
      searchPlaceholder={t('tables.validation.searchPlaceholder') as string}
      onCreateClick={handleCreateClick}
      onSelectionChanged={handleSelectionChanged}
      renderCustomActions={customActions}
    />
  )
}
