import { useState, useMemo } from 'react'
import { useTranslation } from 'react-i18next'
import { ColDef } from 'ag-grid-community'
import { FetchAgencyParam } from 'src/types/models/agencies'
import { useAgencies, useDeleteAgency } from 'src/hooks/useAgency'
import BaseTable from 'src/@core/components/tables/BaseTable'
import { MODELS, MODULES } from 'src/layouts/components/acl/can'
import TableActions from 'src/@core/components/tables/TableActions'
import moment from 'moment'
import { Icon } from '@iconify/react'
import { useRouter } from 'next/router'
import { useCustomers } from 'src/hooks/useCustomer'
import { Box, Button, FormControl, InputLabel, MenuItem, Select } from '@mui/material'
import { useQueryClient } from '@tanstack/react-query'
import toast from 'react-hot-toast'
import CustomDropdown from 'src/@core/shared/components/custom-dropdown'

export default function AgenciesTable() {
  const queryClient = useQueryClient()
  const [queryParams, setQueryParams] = useState<Partial<FetchAgencyParam>>({
    limit: 100,
    offset: 0
  })

  const { data, isLoading, isError, isPending } = useAgencies(queryParams)
  const { mutateAsync: deleteAgencies, isPending: deletingAgencies } = useDeleteAgency({
    onSuccess: () => {
      toast.success(t('tables.agencies.deleteSuccess'))
      queryClient.invalidateQueries({ queryKey: ['agencies'] })
    },
    onError: error => {
      toast.error(t('tables.agencies.deleteError'))
      console.error(error)
    }
  })
  const [selectedRows, setSelectedRows] = useState<any[]>([])
  const { t } = useTranslation()
  const router = useRouter()

  const columns = useMemo<ColDef[]>(
    () => [
      {
        headerCheckboxSelection: true,
        checkboxSelection: true,
        width: 50,
        pinned: 'left'
      },
      {
        headerName: t('tables.agencies.columns.client') as string,
        field: 'company.name'
      },
      {
        headerName: t('tables.agencies.columns.name') as string,
        field: 'name'
      },
      {
        headerName: t('tables.agencies.columns.address') as string,
        field: 'address'
      },
      {
        headerName: t('tables.agencies.columns.active') as string,
        field: 'is_active',
        cellRenderer: (params: any) =>
          params.value ? (
            <Icon icon='mdi:check-circle' color='#56CA00' />
          ) : (
            <Icon icon='mdi:close-circle' color='#FF4C51' />
          )
      },
      {
        headerName: t('tables.agencies.columns.modifiedBy') as string,
        field: 'modified_by',
        valueFormatter: ({ value }) => (value ? `${value.first_name} ${value.last_name}` : '---')
      },
      {
        headerName: t('tables.agencies.columns.modifiedAt') as string,
        field: 'modified_at',
        valueFormatter: ({ value }) => (value ? moment(value).format('LL') : '---')
      },
      {
        headerName: t('common.actions.action') as string,
        field: 'actions',
        pinned: 'right',
        cellRenderer: (params: any) => (
          <TableActions
            id={params.data.id}
            basePath='/user-management/agencies'
            onDelete={handleDeleteClick}
            viewAction={false}
            transLationKey='agencies'
            module={MODULES.APP_AUTHENTICATION}
            model={MODELS[MODULES.APP_AUTHENTICATION].AGENCY}
          />
        )
      }
    ],
    [t]
  )

  const handleDeleteSelected = async () => {
    const selectedIds = selectedRows.map(row => row.id)
    try {
      await deleteAgencies(selectedIds)
    } catch (error) {
      // Error is handled in the mutation's onError callback
    }
  }

  const handleDeleteClick = async (id: string) => {
    try {
      await deleteAgencies([id])
    } catch (error) {
      // Error is handled in the mutation's onError callback
    }
  }

  const [selectedClient, setSelectedClient] = useState<string | null>(null)
  const { data: clients } = useCustomers({ limit: 100, offset: 0 })

  const customActions = () => {
    return (
      <CustomDropdown
        placeholder={t('common.actions.action') as string}
        buttonVariant='contained'
        buttonColor='primary'
        options={[
          {
            label: t('tables.users.deleteSelected'),
            icon: 'mdi:delete',
            onClick: handleDeleteSelected,
            disabled: selectedRows.length === 0
          }
        ]}
      />
    )
  }

  const handleClientFilter = (clientId: string) => {
    setSelectedClient(clientId)
    setQueryParams(prev => ({
      ...prev,
      company: clientId
    }))
  }

  const RenderCustomActions = () => {
    return (
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
        <FormControl>
          <InputLabel id='client-filter-label'>{t('tables.agencies.client')}</InputLabel>
          <Select
            fullWidth
            labelId='client-filter-label'
            size='small'
            value={selectedClient || ''}
            label={t('tables.agencies.client')}
            onChange={e => handleClientFilter(e.target.value as string)}
            sx={{ minWidth: 200 }}
          >
            <MenuItem value=''>All Clients</MenuItem>
            {clients?.results.map(client => (
              <MenuItem key={client.id} value={client.id}>
                {client.name}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
      </Box>
    )
  }

  const handleSelectionChanged = (selectedRows: any[]) => {
    setSelectedRows(selectedRows)
  }

  return (
    <BaseTable
      data={data}
      module={MODULES.APP_AUTHENTICATION}
      model={MODELS[MODULES.APP_AUTHENTICATION].AGENCY}
      isLoading={isLoading}
      isPending={isPending}
      isError={isError}
      error={isError}
      queryParams={queryParams}
      setQueryParams={setQueryParams}
      createButtonText={t('tables.agencies.createButton') as string}
      onCreateClick={() => router.push('/user-management/agencies/create')}
      columnDefs={columns}
      renderCustomActions={customActions}
      onSelectionChanged={handleSelectionChanged}
      customFilters={<RenderCustomActions />}
      rowSelection='multiple'
      defaultColDef={{
        resizable: true,
        minWidth: 100,
        flex: 1
      }}
      title={t('tables.agencies.title') as string}
      searchPlaceholder={t('tables.agencies.searchPlaceholder') as string}
    />
  )
}
