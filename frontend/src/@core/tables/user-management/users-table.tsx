import { useTranslation } from 'react-i18next'
import { useState, useMemo } from 'react'
import { useRouter } from 'next/router'
import TableActions from 'src/@core/components/tables/TableActions'
import { ColDef } from 'ag-grid-community'
import { MODELS, MODULES } from 'src/layouts/components/acl/can'
import moment from 'moment'
import toast from 'react-hot-toast'
import { useDeleteSelectedUsers, useUsers } from 'src/hooks/user-management/useUsers'
import { Button } from '@mui/material'
import { useQueryClient } from '@tanstack/react-query'
import { Icon } from '@iconify/react'
import { BaseFetchParams } from 'src/types/models'
import BaseTable from 'src/@core/components/tables/BaseTable'
import CustomDropdown from 'src/@core/shared/components/custom-dropdown'
import { Can } from 'src/layouts/components/acl/Can'

export default function UsersTable() {
  const queryClient = useQueryClient()
  const { t } = useTranslation()
  const [queryParams, setQueryParams] = useState<Partial<BaseFetchParams>>({ limit: 100, offset: 0 })
  const { data, isLoading, isError, error, isPending } = useUsers(queryParams)
  const { mutateAsync: deleteSelectedUsers } = useDeleteSelectedUsers({
    onSuccess: () => {
      toast.success(t('tables.users.deleteSuccess'))
      queryClient.invalidateQueries({ queryKey: ['users'] })
    },
    onError: error => {
      toast.error(t('tables.users.deleteError'))
      console.error(error)
    }
  })
  const [selectedRow, setSelectedRows] = useState<any[]>([])
  const router = useRouter()

  const handleSelectionChanged = (selectedRows: any[]) => {
    setSelectedRows(selectedRows)
  }

  const handleDeleteSelected = async () => {
    const selectedIds = selectedRow.map(row => row.id)
    try {
      await deleteSelectedUsers(selectedIds)
    } catch (error) {
      // Error is handled in the mutation's onError callback
    }
  }

  const customActions = () => {
    return (
      <Can action={'delete'} model={MODELS[MODULES.APP_AUTHENTICATION].USERS} module={MODULES.APP_AUTHENTICATION}>
        <CustomDropdown
          placeholder={t('common.actions.action') as string}
          buttonVariant='contained'
          buttonColor='primary'
          options={[
            {
              label: t('tables.users.deleteSelected'),
              icon: 'mdi:delete',
              onClick: handleDeleteSelected,
              disabled: selectedRow.length === 0
            },
            {
              label: t('tables.users.activateSelected'),
              icon: 'mdi:check-circle',
              onClick: () => handleActivateDeactivateSelected(true),
              disabled: selectedRow.length === 0
            },
            {
              label: t('tables.users.deactivateSelected'),
              icon: 'mdi:close-circle',
              onClick: () => handleActivateDeactivateSelected(false),
              disabled: selectedRow.length === 0
            }
          ]}
        />
      </Can>
    )
  }

  const handleActivateDeactivateSelected = async (activate: boolean) => {
    const selectedIds = selectedRow.map(row => row.id)
    try {
      toast.success(activate ? t('tables.users.activateSuccess') : t('tables.users.deactivateSuccess'))
      queryClient.invalidateQueries({ queryKey: ['users'] })
    } catch (error) {
      toast.error(activate ? t('tables.users.activateError') : t('tables.users.deactivateError'))
      console.error(error)
    }
  }

  const columns = useMemo<ColDef[]>(
    () => [
      {
        headerCheckboxSelection: true,
        checkboxSelection: true,
        width: 50,
        pinned: 'left'
      },

      {
        headerName: t('tables.users.columns.username') as string,
        field: 'username'
      },
      {
        headerName: t('tables.users.columns.picture') as string,
        field: 'picture',
        cellRenderer: (params: any) => {
          return params.value ? (
            <img src={params.value} alt='User' style={{ width: '50px', height: '50px', borderRadius: '50%' }} />
          ) : (
            '___'
          )
        }
      },
      {
        headerName: t('tables.users.columns.firstName') as string,
        field: 'first_name'
      },
      {
        headerName: t('tables.users.columns.lastName') as string,
        field: 'last_name'
      },
      {
        headerName: t('tables.users.columns.email') as string,
        field: 'email'
      },
      {
        headerName: t('tables.users.columns.supervisor') as string,
        field: 'supervisor',
        valueFormatter: ({ value }) => (value ? `${value.first_name} ${value.last_name}` : '---')
      },
      {
        headerName: t('tables.users.columns.isSuperuser') as string,
        field: 'is_superuser',
        valueFormatter: ({ value }) => (value ? 'Yes' : 'No')
      },
      {
        headerName: t('tables.users.columns.language') as string,
        field: 'language',
        valueFormatter: ({ value }) => (value ? (value == 'fr' ? 'French' : ' English') : '---')
      },
      {
        headerName: t('tables.users.columns.isActive') as string,
        field: 'is_active',
        valueFormatter: ({ value }) => (value ? 'Yes' : 'No')
      },
      {
        headerName: t('common.actions.action') as string,
        cellRenderer: (params: any) => {
          return (
            <TableActions
              onDelete={handleDelete}
              module={MODULES.APP_AUTHENTICATION}
              model={MODELS[MODULES.APP_AUTHENTICATION].USERS}
              id={params.data.id}
              basePath={'/user-management/users'}
              viewAction={false}
            />
          )
        }
      }
    ],
    [t]
  )

  const handleDelete = async (id: string) => {
    try {
      await deleteSelectedUsers([id])
    } catch (error) {
      // Error is handled in the mutation's onError callback
    }
  }

  return (
    <BaseTable
      data={data}
      isLoading={isLoading}
      isPending={isPending}
      isError={isError}
      error={isError}
      queryParams={queryParams}
      rowSelection='multiple'
      setQueryParams={setQueryParams}
      createButtonText={t('tables.users.createButton') as string}
      columnDefs={columns}
      defaultColDef={{
        resizable: true,
        minWidth: 20,

        tooltipComponent: 'customTooltip'
      }}
      title={t('tables.users.title') as string}
      searchPlaceholder={t('tables.users.searchPlaceholder') as string}
      onCreateClick={() => router.push('/user-management/users/create')}
      onSelectionChanged={handleSelectionChanged}
      renderCustomActions={customActions}
      module={MODULES.APP_AUTHENTICATION}
      model={MODELS[MODULES.APP_AUTHENTICATION].USERS}
    />
  )
}
