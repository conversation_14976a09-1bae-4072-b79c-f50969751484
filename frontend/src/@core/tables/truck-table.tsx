import { useState } from 'react'
import { useGetAllTrucks } from 'src/hooks/useTruck'
import { TruckParam } from 'src/types/models/truck'

export default function TruckTable() {
  const [queryParams, setQueryParams] = useState<TruckParam>({
    limit: 100,
    offset: 0
  })

  const { data, isLoading, isPending, isError, error } = useGetAllTrucks(queryParams)

  return <div>{JSON.stringify(data, null, 3)}</div>
}
