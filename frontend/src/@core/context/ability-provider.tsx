import React, { createContext, useContext, useEffect, useState } from 'react'
import { Ability } from '@casl/ability'
import { defineAbilityFor } from 'src/configs/abilityConfig'
import { useSelector } from 'react-redux'
import { selectUser } from 'src/store/auth'
import { User } from '../types/auth'

interface AbilityContextType {
  ability: Ability
  rebuildAbility: (user: User | null) => void
}

const AbilityContext = createContext<AbilityContextType | undefined>(undefined)

export function AbilityProvider({ children }: { children: React.ReactNode }) {
  const user = useSelector(selectUser)
  const [ability, setAbility] = useState(() => defineAbilityFor(null))

  const rebuildAbility = (user: User | null) => {
    const newAbility = defineAbilityFor(user?.user_permissions || null)
    setAbility(newAbility)
  }

  useEffect(() => {
    rebuildAbility(user)
  }, [user])

  return <AbilityContext.Provider value={{ ability, rebuildAbility }}>{children}</AbilityContext.Provider>
}

export function useAbility() {
  const context = useContext(AbilityContext)
  if (!context) {
    throw new Error('useAbility must be used within AbilityProvider')
  }

  return context
}

export function usePermissions() {
  const { ability } = useAbility()
  const user = useSelector(selectUser)

  const hasPermission = (module: string, model: string, action: string) => {
    if (!user?.user_permissions) return false
    const modulePermissions = user.user_permissions[module]
    if (!modulePermissions) return false
    const modelPermissions = modulePermissions.find(m => m.model === model)
    if (!modelPermissions) return false

    return modelPermissions.permission.includes(action)
  }

  const can = (action: string, subject: string) => {
    return ability.can(action, subject)
  }

  const cannot = (action: string, subject: string) => {
    return ability.cannot(action, subject)
  }

  return { hasPermission, can, cannot }
}
