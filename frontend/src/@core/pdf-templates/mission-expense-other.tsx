import Image from 'next/image'
import {
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Typography,
  Box,
  Container,
  styled,
  Checkbox,
  FormControlLabel,
  Card,
  CardContent,
  Button
} from '@mui/material'
import BackButton from '../components/back-button'
import { MissionExpensesOthers } from 'src/types/models/mission-expense-other'
import moment from 'moment'
import { Icon } from '@iconify/react'

const StyledTableCell = styled(TableCell)(({ theme }) => ({
  border: '1px solid #091F5C',
  padding: '8px',
  fontSize: '11px',
  textAlign: 'start',
  color: '#091F5C',
  '@media print': {
    color: '#091F5C !important',
    borderColor: '#091F5C !important'
  },
  '&.header': {
    backgroundColor: '#eee',
    fontWeight: 'bold',
    '@media print': {
      backgroundColor: '#eee !important',
      WebkitPrintColorAdjust: 'exact',
      printColorAdjust: 'exact'
    }
  }
}))

const StyledFormSection = styled(Box)({
  marginBottom: '1rem',
  '& .label': {
    color: '#091F5C',
    fontWeight: 'bold',
    fontSize: '11px',
    marginRight: '8px'
  },
  '& .value': {
    fontSize: '11px'
  }
})

const SignatureBox = styled(Box)(({ theme }) => ({
  textAlign: 'center',
  flex: 1,
  padding: '8px',
  border: '1px solid #091F5C',
  display: 'flex',
  flexDirection: 'column',
  justifyContent: 'space-between',
  minHeight: '120px',
  '@media print': {
    border: '1px solid #091F5C !important',
    color: '#091F5C !important'
  }
}))

const priorities = [
  {
    name: 'Normal',
    value: 'NORMAL'
  },
  {
    name: 'Urgent',
    value: 'URGENT'
  },
  {
    name: 'Important',
    value: 'IMPORTANT'
  },
  {
    name: 'Courant',
    value: 'COURANT'
  }
]

const validationTitle = [
  '(1) (2) VISA N+1',
  '(1) VISA DIRECTEUR GENERAL ADJOIN',
  'VISA DIRECTEUR ADMI. FIN & CPTBLE(Autorisation Financière)'
]

export default function MissionExpenseOthersReport({ data }: { data: MissionExpensesOthers }) {
  return (
    <Container sx={{ py: 2 }}>
      <Box sx={{ '@media print': { display: 'none' } }}>
        <Box sx={{ mb: 4 }}>
          <BackButton />
        </Box>
        <Card sx={{ mb: 4 }}>
          <CardContent sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Box>
              <Typography variant='h6' sx={{ mb: 1 }}>
                Mission Expense Report
              </Typography>
              <Box sx={{ display: 'flex', gap: 4 }}>
                <Typography sx={{ color: 'text.secondary' }}>
                  Document #: <strong>{data?.code}</strong>
                </Typography>
                <Typography sx={{ color: 'text.secondary' }}>
                  Date: <strong>{moment(data?.created_at).format('DD/MM/YYYY')}</strong>
                </Typography>
              </Box>
            </Box>
            <Button variant='contained' startIcon={<Icon icon='tabler:printer' />} onClick={() => window.print()}>
              Print
            </Button>
          </CardContent>
        </Card>
      </Box>

      <Box sx={{ '@media screen': { bgcolor: 'grey.50', p: 4, borderRadius: 1 } }}>
        <Paper sx={{ p: 6, borderRadius: 0, border: '1px solid #091F5C', '@media print': { boxShadow: 'none' } }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 4, alignItems: 'flex-start' }}>
            <Image
              src='/placeholder.svg?height=60&width=120'
              alt='PSL Logo'
              width={120}
              height={60}
              style={{ objectFit: 'contain' }}
            />
            <Box sx={{ flex: 1, textAlign: 'center', mx: 4 }}>
              <Typography
                variant='h6'
                sx={{
                  color: '#091F5C',
                  fontWeight: 'bold',
                  fontSize: '1.1rem'
                }}
              >
                __DEMANDE D&apos;ACHAT / PRESTATIONS DIVERSES__
              </Typography>
            </Box>
            <Box sx={{ textAlign: 'right' }}>
              <Typography variant='body1' sx={{ color: '#091F5C', fontWeight: 'bold' }}>
                DATE : {moment(data?.created_at).format('DD/MM/YYYY')}
              </Typography>
              <Typography variant='body1' sx={{ color: '#091F5C', fontWeight: 'bold' }}>
                N° : {data?.code}
              </Typography>
            </Box>
          </Box>

          {/* Requester Information */}
          <Box sx={{ mb: 3, fontStyle: 'oblique' }}>
            <Typography
              variant='subtitle2'
              sx={{
                color: '#091F5C',
                fontWeight: 'bold',
                borderBottom: '1px solid #091F5C',
                mb: 1
              }}
            >
              IDENTITE DU DEMANDEUR
            </Typography>
            <StyledFormSection>
              <Box sx={{ display: 'flex', mb: 1, bgcolor: '#F8F9FB', p: 1 }}>
                <Typography className='label'>SERVICE OU DEPARTEMENT :</Typography>
                <Typography className='value'>LOGISTIQUE</Typography>
              </Box>
              <Box sx={{ display: 'flex', mb: 1, bgcolor: '#F8F9FB', p: 1 }}>
                <Typography className='label'>NOMS & PRENOMS DU DEMANDEUR :</Typography>
                <Typography className='value'>
                  {data?.created_by.first_name + ' ' + data?.created_by.last_name}
                </Typography>
              </Box>
              <Box sx={{ display: 'flex', mb: 1, bgcolor: '#F8F9FB', p: 1 }}>
                <Typography className='label'>FONCTION DU DEMANDEUR :</Typography>
                <Typography className='value'>LOGISTICIEN</Typography>
              </Box>
            </StyledFormSection>
          </Box>

          {/* Payment Method */}
          <TableContainer sx={{ mb: 3 }}>
            <Table sx={{ border: '1px solid #091F5C' }}>
              <TableHead sx={{ border: '1px solid #091F5C' }}>
                <TableRow sx={{ backgroundColor: '#eee' }}>
                  <TableCell align='center'>
                    <Typography variant='body2' style={{ fontWeight: 'bold', color: 'black' }}>
                      ESPECES (CAISSE)
                    </Typography>
                  </TableCell>

                  <TableCell align='center'>
                    <Typography variant='body2' style={{ fontWeight: 'bold', color: 'black' }}>
                      BON DE COMMANDE
                    </Typography>
                  </TableCell>

                  <TableCell align='center'>
                    <Typography variant='body2' style={{ fontWeight: 'bold', color: 'black' }}>
                      CHEQUE
                    </Typography>
                  </TableCell>
                </TableRow>
              </TableHead>

              <TableBody>
                <TableRow>
                  <TableCell align='center' sx={{ border: '1px solid #091F5C' }}>
                    <span style={{ fontWeight: 'bold', fontSize: '2rem' }}>☑</span>
                  </TableCell>
                  <TableCell align='center' sx={{ border: '1px solid #091F5C' }}>
                    <span style={{ fontWeight: 'bold', fontSize: '2rem' }}>{'☐'}</span>
                  </TableCell>
                  <TableCell align='center' sx={{ border: '1px solid #091F5C' }}>
                    <span style={{ fontWeight: 'bold', fontSize: '2rem' }}>{'☐'}</span>
                  </TableCell>
                </TableRow>
              </TableBody>
            </Table>
          </TableContainer>

          {/* Observation */}
          <Box sx={{ mb: 5 }}>
            <Typography
              variant='subtitle2'
              sx={{
                color: '#091F5C',
                fontWeight: 'bold',
                borderBottom: '1px solid #091F5C',
                mb: 1
              }}
            >
              OBSERVATION
            </Typography>
            <Box sx={{ height: '40px', border: '1px solid #091F5C' }} />
          </Box>

          {/* Priority Management */}
          <Box sx={{ mb: 5, display: 'flex', alignItems: 'center', width: '100%', gap: 10 }}>
            <Typography variant='body2' sx={{ color: '#091F5C', flexShrink: 0, fontWeight: 'bold', mr: 2 }}>
              GESTION DES PRIORITES_
            </Typography>
            <div style={{ display: 'flex', gap: 10, width: '100%', justifyContent: 'space-between' }}>
              {priorities.map((p, index) => (
                <div key={p.name} style={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <span style={{ fontWeight: 'bold', fontSize: '2rem' }}>{data?.priority === p.name ? '☑' : '☐'}</span>
                  <span style={{ fontStyle: 'italic' }}>{p.value}</span>
                </div>
              ))}
            </div>
          </Box>

          {/* Main Table */}
          <TableContainer sx={{ mb: 3 }}>
            <Table>
              <TableHead>
                <TableRow>
                  <StyledTableCell className='header'>DESIGNATION OU LIBELLE ARTICLE / REF.</StyledTableCell>
                  <StyledTableCell className='header'>OPERATIONS</StyledTableCell>
                  <StyledTableCell className='header' align='center'>
                    QUANTITÉ
                  </StyledTableCell>
                  <StyledTableCell className='header' align='center'>
                    PU
                  </StyledTableCell>
                  <StyledTableCell className='header' align='center'>
                    MONTANT
                  </StyledTableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {data?.mission_other_lines.map((item, index) => (
                  <TableRow key={item.id}>
                    <StyledTableCell>{item.description}</StyledTableCell>
                    <StyledTableCell sx={{ whiteSpace: 'pre-line' }}>
                      {item.operations.map((op: any) => op.order_code + ' - BE ' + op.be_number + '\n')}
                    </StyledTableCell>
                    <StyledTableCell align='center'>{item.qty}</StyledTableCell>
                    <StyledTableCell align='right'>{item.fees}</StyledTableCell>
                    <StyledTableCell align='right'>{item.amount}</StyledTableCell>
                  </TableRow>
                ))}
                <TableRow>
                  <StyledTableCell colSpan={4} sx={{ bgcolor: '#eee' }}>
                    <Typography variant='body2' sx={{ fontWeight: 'bold' }}>
                      TOTAL
                    </Typography>
                  </StyledTableCell>
                  <StyledTableCell
                    align='right'
                    sx={{ fontWeight: 'bold', bgcolor: '#eee', color: '#000', fontSize: '.9rem' }}
                  >
                    {data?.total_amount} FCFA
                  </StyledTableCell>
                </TableRow>
              </TableBody>
            </Table>
          </TableContainer>

          {/* Amount in Words */}
          <Box sx={{ mb: 3 }}>
            <Typography variant='body2' sx={{ color: '#091F5C', fontWeight: 'bold' }}>
              Montant en lettres:
            </Typography>

            <Typography variant='body2' sx={{ color: '#091F5C' }}>
              {/* {data.amountInWords} */}
            </Typography>
            <hr style={{ height: '4px', border: 'none', backgroundColor: '#091F5C' }} />
          </Box>

          {/* Validation Notes */}
          <Box sx={{ mb: 3, borderBottom: '1px solid #091F5C', paddingBottom: '10px', textAlign: 'right' }}>
            <Typography variant='body2' display='block' sx={{ color: '#091F5C' }}>
              (1) Validation Obligatoire pour Accord Opérations Logistiques & Transport (Départ.Exploitation)
            </Typography>
            <Typography variant='body2' display='block' sx={{ color: '#091F5C' }}>
              (2) Validation Obligatoire pour Accord Opérations Maintenance (Départ.Techniques)
            </Typography>
          </Box>

          <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 4 }}>
            {[...Array(4)].map((_, index) => {
              if (index === 0) {
                return (
                  <Box key={index} sx={{ flex: 1 }}>
                    <SignatureBox>
                      <Box>
                        <Typography variant='body1' sx={{ color: '#091F5C' }}>
                          {data?.created_by.first_name + ' ' + data?.created_by.last_name}
                        </Typography>
                        <Typography variant='body2' sx={{ color: '#091F5C', fontWeight: 'bold' }}>
                          VALIDEE
                        </Typography>
                        <Typography variant='body1' display='block'>
                          Le {moment(data?.created_at).format('DD/MM/YYYY')}
                        </Typography>
                        <Typography variant='body1' display='block'>
                          à {moment(data?.created_at).format('HH:mm')}
                        </Typography>
                      </Box>
                    </SignatureBox>
                    <Box sx={{ textAlign: 'center' }}>
                      <Typography variant='overline' sx={{ color: '#091F5C', fontWeight: 'bold' }}>
                        VISA DEMANDEUR (SE)
                      </Typography>
                    </Box>
                  </Box>
                )
              }

              // For other boxes, show validation status
              const sig = data?.validation[index - 1] // Offset by 1 since first box is creator

              return (
                <Box key={index} sx={{ flex: 1 }}>
                  <SignatureBox>
                    <Box>
                      {sig ? (
                        <>
                          <Typography variant='body1' sx={{ color: '#091F5C' }}>
                            {sig.user.first_name + ' ' + sig.user.last_name}
                          </Typography>
                          <Typography variant='body2' sx={{ color: '#091F5C', fontWeight: 'bold' }}>
                            {sig.validation}
                          </Typography>
                          <Typography variant='body1' display='block'>
                            Le {moment(sig.created_at).format('DD/MM/YYYY')}
                          </Typography>
                          <Typography variant='body1' display='block'>
                            à {moment(sig.created_at).format('HH:mm')}
                          </Typography>
                        </>
                      ) : (
                        <>
                          <Typography variant='body1' sx={{ color: '#091F5C' }}>
                            Non validé
                          </Typography>
                          <Typography variant='body2' sx={{ color: '#091F5C', fontWeight: 'bold' }}>
                            EN ATTENTE
                          </Typography>
                        </>
                      )}
                    </Box>
                  </SignatureBox>
                  <Box sx={{ textAlign: 'center' }}>
                    <Typography
                      variant='caption'
                      sx={{ color: '#091F5C', fontWeight: 'bold', textTransform: 'capitalize' }}
                    >
                      {validationTitle[index - 1]}
                    </Typography>
                  </Box>
                </Box>
              )
            })}
          </Box>

          {/* Footer */}
          <Box sx={{ textAlign: 'center', mt: 4, borderTop: '1px solid #eee', paddingTop: '8px' }}>
            <Typography variant='caption' display='block' sx={{ color: '#666' }}>
              PETRO SERVICES ET LOGISTIQUE SARL | RC N° : RC/DLA/2011/B/07 N/UI : M011100034744H
            </Typography>
            <Typography variant='caption' display='block' sx={{ color: '#666' }}>
              B.P : 2963 Douala / Cameroun Tel : 233478083
            </Typography>
          </Box>
        </Paper>
      </Box>
    </Container>
  )
}
