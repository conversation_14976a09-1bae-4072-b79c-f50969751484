import Image from 'next/image'
import {
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Typography,
  Box,
  Container,
  styled,
  Button
} from '@mui/material'
import moment from 'moment'
import { MissionExpense, Missionexpenseline } from 'src/types/models/mission-expense'
import { Icon } from '@iconify/react'
import BackButton from '../components/back-button'

// Styled components for consistent styling
const StyledTableCell = styled(TableCell)(({ theme }) => ({
  border: '1px solid #091F5C',
  padding: '4px 8px',
  fontSize: '11px',
  color: '#091F5C',
  textAlign: 'center',
  backgroundColor: '#F8F9FB',
  '&.header': {
    backgroundColor: '#E8EDF7',
    fontWeight: 'bold',
    whiteSpace: 'pre-line',
    lineHeight: 1.2
  }
}))

const StyledTableRow = styled(TableRow)(() => ({
  '& td': {
    backgroundColor: '#F8F9FB'
  }
}))

const TableFooter = ({ data }: { data?: MissionExpense }) => (
  <>
    <TableRow>
      <StyledTableCell
        sx={{
          textAlign: 'left',
          verticalAlign: 'top',
          backgroundColor: '#F8F9FB',
          p: 1
        }}
      >
        <Typography variant='caption' display='block'>
          {`${data?.created_by.first_name} ${data?.created_by.last_name}`}
        </Typography>
        <Typography variant='caption' display='block'>
          Validée
        </Typography>
        <Typography variant='caption' display='block'>
          Le {}
        </Typography>
        <Typography variant='caption' display='block'>
          à 10:27
        </Typography>
      </StyledTableCell>
      <StyledTableCell sx={{ backgroundColor: '#F8F9FB' }}>Non Visé</StyledTableCell>
      <StyledTableCell sx={{ backgroundColor: '#F8F9FB' }}>Non Visé</StyledTableCell>
      <StyledTableCell sx={{ backgroundColor: '#F8F9FB' }}>Non Visé</StyledTableCell>
      <StyledTableCell
        sx={{
          backgroundColor: '#E8EDF7',
          fontWeight: 'bold',
          verticalAlign: 'middle'
        }}
        rowSpan={2}
      >
        TOTAL
      </StyledTableCell>
      <StyledTableCell
        sx={{
          backgroundColor: '#E8EDF7',
          fontWeight: 'bold'
        }}
      >
        FRAIS DE MISSION
      </StyledTableCell>
      <StyledTableCell
        sx={{
          backgroundColor: '#E8EDF7',
          fontWeight: 'bold'
        }}
      >
        CONSO. CARB (L)
      </StyledTableCell>
      <StyledTableCell
        sx={{
          backgroundColor: '#E8EDF7',
          fontWeight: 'bold'
        }}
      >
        MONTANT CARB
      </StyledTableCell>
      <StyledTableCell
        colSpan={3}
        sx={{
          textAlign: 'left',
          backgroundColor: '#F8F9FB',
          p: 1
        }}
        rowSpan={2}
      >
        <Typography variant='caption' display='block' sx={{ fontSize: '9px', mb: 0.5 }}>
          * Ne pas oublier de mentionner les Consommations, l&apos;Exactitude des Frais de Mission/route est de rigueur.
        </Typography>
        <Typography variant='caption' display='block' sx={{ fontSize: '9px' }}>
          * Détailler entièrement les opérations exécutées dans sa globalité (N°BON, QTE Transportée...)
        </Typography>
      </StyledTableCell>
    </TableRow>
    <TableRow>
      <StyledTableCell colSpan={4}></StyledTableCell>
      {/* No need to include TOTAL cell here since it spans 2 rows */}
      <StyledTableCell sx={{ backgroundColor: '#F8F9FB' }}>396 000,00</StyledTableCell>
      <StyledTableCell sx={{ backgroundColor: '#F8F9FB' }}>3 560</StyledTableCell>
      <StyledTableCell sx={{ backgroundColor: '#F8F9FB' }}>2 847 660,00</StyledTableCell>
    </TableRow>
  </>
)

const AuthorizationSection = () => (
  <Box sx={{ mt: 2, mb: 4 }}>
    <Box sx={{ textAlign: 'center', mb: 1 }}>
      <Typography
        component='div'
        sx={{
          color: '#4CAF50',
          fontSize: '11px',
          fontWeight: 500,
          mb: 2
        }}
      >
        AUTORISATION
      </Typography>
    </Box>
    <Box
      sx={{
        display: 'flex',
        justifyContent: 'space-between',
        px: 2,
        mb: 1
      }}
    >
      <Typography variant='caption'>LOGISTICIEN</Typography>
      <Typography variant='caption'>RESP. EXPLOITATION</Typography>
      <Typography variant='caption'>DIR. GENERAL ADJOINT</Typography>
    </Box>
    <Box sx={{ textAlign: 'center' }}>
      <Typography
        variant='caption'
        sx={{
          color: '#4CAF50',
          fontSize: '11px'
        }}
      >
        FINANCIERE (DAFC)
      </Typography>
    </Box>
  </Box>
)

const CompanyFooter = () => (
  <Box
    sx={{
      textAlign: 'center',
      mt: 3,
      '& > *': {
        lineHeight: 1.3,
        color: '#666',
        fontSize: '11px'
      }
    }}
  >
    <Typography variant='caption' display='block'>
      RC N° : RC/DLA/2011/B/07 N/UI : M011100034744H
    </Typography>
    <Typography variant='caption' display='block'>
      B.P : 2963 Douala / Cameroun
    </Typography>
    <Typography variant='caption' display='block'>
      Tel : *********
    </Typography>
  </Box>
)

const MissionExpenseReport = ({ data }: { data?: MissionExpense }) => {
  const handlePrint = () => {
    window.print()
  }

  return (
    <Container sx={{ p: 10 }}>
      {/* Print Button */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2, '@media print': { display: 'none' } }}>
        <BackButton />
        <Button
          variant='contained'
          startIcon={<Icon icon={''} />}
          onClick={handlePrint}
          sx={{
            bgcolor: '#091F5C',
            '&:hover': {
              bgcolor: '#0c2a7a'
            }
          }}
        >
          Imprimer
        </Button>
      </Box>

      <Paper
        elevation={0}
        sx={{
          p: 20,
          bgcolor: 'white',

          borderRadius: 0,
          '@media print': {
            boxShadow: 'none',
            margin: 0,
            padding: 0,
            width: '100%'
          }
        }}
      >
        {/* Logo */}
        <Box sx={{ mb: 2 }}>
          <Image
            src='/placeholder.svg?height=60&width=120'
            alt='PSL Logo'
            width={120}
            height={60}
            style={{ objectFit: 'contain' }}
          />
        </Box>

        {/* Title Section */}
        <Box sx={{ mb: 2 }}>
          <Box
            sx={{
              bgcolor: '#E8EDF7',
              py: 0.5,
              textAlign: 'center',
              mb: 0.5
            }}
          >
            <Typography
              variant='h6'
              sx={{
                fontWeight: 600,
                color: '#091F5C',
                fontSize: '1.1rem'
              }}
            >
              DEMANDE DE FRAIS DE MISSIONS/CONSOMMATIONS
            </Typography>
          </Box>
          <Typography
            variant='h6'
            align='center'
            sx={{
              fontWeight: 600,
              color: '#091F5C',
              fontSize: '1rem'
            }}
          >
            __PRESTATIONS CAMIONS BENNE__
          </Typography>
        </Box>

        {/* Client and Reference Info */}
        <Box
          sx={{
            mb: 2,
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'flex-start'
          }}
        >
          <Typography sx={{ color: '#091F5C', fontWeight: 'bold' }} variant='body2'>
            CLIENT : {data?.client}
          </Typography>
          <Box
            sx={{
              display: 'flex',
              gap: 1,
              alignItems: 'center',
              bgcolor: '#E8EDF7',
              p: 1,
              borderRadius: 1
            }}
          >
            <Typography variant='body2' sx={{ color: '#091F5C' }}>
              NUMERO DE L&apos;OPERATION :
            </Typography>
            <Typography variant='body2' sx={{ color: '#091F5C' }}>
              N° CONTRAT BENNE: {data?.mission_expense_lines[0].be_number} - CSE / {moment().format('YYY')} /
            </Typography>
            <Typography style={{ fontWeight: 'bold' }} variant='body2' color='error'>
              {data?.code}
            </Typography>
          </Box>
        </Box>

        {/* Main Table */}
        <TableContainer sx={{ mb: 2, border: '1px solid #091F5C' }}>
          <Table size='small' sx={{ minWidth: 800, borderCollapse: 'collapse' }}>
            <TableHead>
              <TableRow>
                <StyledTableCell className='header'>DATE</StyledTableCell>
                <StyledTableCell className='header'>CHAUFFEUR</StyledTableCell>
                <StyledTableCell className='header'>IMMATRICULATION</StyledTableCell>
                <StyledTableCell className='header'>{'LIEU DE LIVRAISON\n/ CLIENT'}</StyledTableCell>
                <StyledTableCell className='header'>{'N°BC/N°\nCONT.'}</StyledTableCell>
                <StyledTableCell className='header'>{'FRAIS DE MISSION\n(FCFA)'}</StyledTableCell>
                <StyledTableCell className='header'>{'CONSO. CARB\n(LITRES)'}</StyledTableCell>
                <StyledTableCell className='header'>{'MNT CONSO.\nCARB (FCFA)'}</StyledTableCell>
                <StyledTableCell className='header'>QTE</StyledTableCell>
                <StyledTableCell className='header'>PRODUIT</StyledTableCell>
                <StyledTableCell className='header'>OBS</StyledTableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {data?.mission_expense_lines.map((line, index) => (
                <StyledTableRow key={index}>
                  <StyledTableCell>{moment(line.created_at).format('l')}</StyledTableCell>
                  <StyledTableCell>{`${line.driver.first_name} ${line.driver.last_name}`}</StyledTableCell>
                  <StyledTableCell>{line.vehicle}</StyledTableCell>
                  <StyledTableCell>{line.destination.name}</StyledTableCell>
                  <StyledTableCell>{line.be_number}</StyledTableCell>
                  <StyledTableCell>{line.fees}</StyledTableCell>
                  <StyledTableCell>{line.petrol_volum}</StyledTableCell>
                  <StyledTableCell>{line.petrol_amount}</StyledTableCell>
                  <StyledTableCell>{line.product_qty}</StyledTableCell>
                  <StyledTableCell>{line.product.label}</StyledTableCell>
                  <StyledTableCell>{line.obs || ''}</StyledTableCell>
                </StyledTableRow>
              ))}
              <TableFooter data={data} />
            </TableBody>
          </Table>
        </TableContainer>

        <AuthorizationSection />
        <CompanyFooter />
      </Paper>
    </Container>
  )
}

export default MissionExpenseReport
