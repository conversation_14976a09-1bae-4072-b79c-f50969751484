import { MutateOptions, useMutation, useQuery } from '@tanstack/react-query'
import { createAgency, deleteAgency, fetchAgencies, getAgencyById, updateAgency } from 'src/services/agencies'
import { AgencyRequestPayload, FetchAgencyParam } from 'src/types/models/agencies'

export const useAgencies = (params: Partial<FetchAgencyParam>) => {
  return useQuery({
    queryKey: ['agencies', params],
    queryFn: () => fetchAgencies(params)
  })
}

export const useAgencyById = (id: string) => {
  return useQuery({
    queryKey: ['agency', id],
    queryFn: () => getAgencyById(id),
    enabled: !!id
  })
}

export const useCreateAgency = (options?: MutateOptions<any, any, AgencyRequestPayload>) => {
  return useMutation({
    mutationFn: (data: AgencyRequestPayload) => createAgency(data),
    ...options
  })
}

export const useUpdateAgency = (options?: MutateOptions<any, any, { id: string; data: AgencyRequestPayload }>) => {
  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: AgencyRequestPayload }) => updateAgency(id, data),
    ...options
  })
}

export const useDeleteAgency = (options?: MutateOptions<any, any, string[]>) => {
  return useMutation({
    mutationFn: (ids: string[]) => deleteAgency(ids),
    ...options
  })
}
