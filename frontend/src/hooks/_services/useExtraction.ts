import { useQuery } from '@tanstack/react-query'
import { fetchExtractions, getExtractionById } from 'src/services/_service/extractions'
import { Extraction, ExtractionResponse } from 'src/types/models/_services/extraction'

export const useExtraction = (params: Partial<any>) => {
  return useQuery<ExtractionResponse>({
    queryKey: ['extractions', params],
    queryFn: () => fetchExtractions(params)
  })
}

export const useExtractionById = (id: string) => {
  return useQuery<Extraction>({
    queryKey: ['extraction', id],
    queryFn: () => getExtractionById(id)
  })
}
