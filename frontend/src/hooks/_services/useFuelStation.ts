import { useInfiniteQuery, useQuery, useQueryClient } from '@tanstack/react-query'
import { debounce } from 'lodash'
import { fetchStations } from 'src/services/_service/stations'
import { FetchStationsParams } from 'src/types/models/_services/stations'
import { useCallback, useState } from 'react'

// Regular hook for table usage
export const useFuelStations = (params: Partial<FetchStationsParams>) => {
  return useQuery({
    queryKey: ['fuel-stations', params],
    queryFn: () => fetchStations(params)
  })
}

// Infinite scroll hook
// export const useInfiniteFuelStations = (initialParams?: Partial<FetchStationsParams>) => {
//   const [search, setSearch] = useState('')
//   const queryClient = useQueryClient()

//   const defaultParams: FetchStationsParams = {
//     limit: 100, // This was already 100, keeping it for consistency
//     offset: 0,
//     ...initialParams
//   }

//   const query = useInfiniteQuery({
//     queryKey: ['infinite-fuel-stations', { ...defaultParams, search }],
//     queryFn: ({ pageParam = 0 }) =>
//       fetchStations({
//         ...defaultParams,
//         offset: pageParam * defaultParams.limit,
//         search
//       }),
//     getNextPageParam: lastPage => {
//       if (!lastPage.next) return

//       return Math.ceil(lastPage.count / defaultParams.limit)
//     },
//     initialPageParam: 0
//   })

//   const debouncedSearch = useCallback(
//     (value: string) => {
//       debounce((searchValue: string) => {
//         setSearch(searchValue)
//         queryClient.resetQueries({ queryKey: ['infinite-fuel-stations'] })
//       }, 300)(value)
//     },
//     [queryClient]
//   )

//   return {
//     ...query,
//     search,
//     setSearch: debouncedSearch
//   }
// }
