import { MutateOptions, useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import {
  deleteImmobilization,
  deleteSelectedImmobilizations,
  fetchImmobilizations,
  getImmobilizationById
} from 'src/services/_service/immobilization'
import { FetchImmobilizationParams } from 'src/types/models/_services/imobilization'

export const useImmobilizations = (params: Partial<FetchImmobilizationParams>) => {
  return useQuery({
    queryKey: ['immobilizations', params],
    queryFn: () => fetchImmobilizations(params)
  })
}

export const useImmobilizationById = (id: string) => {
  return useQuery({
    queryKey: ['immobilization', id],
    queryFn: () => getImmobilizationById(id)
  })
}

export const useDeleteImmobilization = (options?: MutateOptions<any, any, any>) => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (id: string) => deleteImmobilization(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['immobilizations'] })
    },
    ...options
  })
}

export const useDeleteSelectedImmobilizations = (options?: MutateOptions<any, any, string[]>) => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (ids: string[]) => deleteSelectedImmobilizations(ids),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['immobilizations'] })
    },
    ...options
  })
}
