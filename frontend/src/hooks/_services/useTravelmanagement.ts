import { MutateOptions, useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import {
  deleteTravel,
  fetchTravels,
  getTravelById,
  startTravel,
  endTravel,
  loadTravel,
  unloadTravel,
  reportBlocking,
  endBlocking,
  cancelTravel,
  setOnWay,
  updateTravel,
  createTravel,
  getTravelsStats,
  getTravelsDashboardStats,
  deleteSelectedTravels
} from 'src/services/_service/travel'
import {
  TravelParams,
  TravelStartPayload,
  TravelEndPayload,
  TravelLoadPayload,
  TravelUnloadPayload,
  TravelBlockingPayload,
  TravelCancelPayload,
  TravelBasePayload
} from 'src/types/models/_services/travel'

export const useTravels = (params: Partial<TravelParams>) => {
  return useQuery({
    queryKey: ['travels', params],
    queryFn: () => fetchTravels(params)
  })
}

export const useTravelsStats = () => {
  return useQuery({
    queryKey: ['travels-stats'],
    queryFn: () => getTravelsStats()
  })
}

export const useTravelById = (id: string) => {
  return useQuery({
    queryKey: ['travel', id],
    queryFn: () => getTravelById(id),
    enabled: !!id
  })
}

export const useStartTravel = (id: string, options?: MutateOptions<any, Error, TravelStartPayload>) => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (payload: TravelStartPayload) => startTravel(id, payload),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['travels'] })
      queryClient.invalidateQueries({ queryKey: ['travel', id] })
    },
    ...options
  })
}

export const useEndTravel = (id: string, options?: MutateOptions<any, Error, TravelEndPayload>) => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (payload: TravelEndPayload) => endTravel(id, payload),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['travels'] })
      queryClient.invalidateQueries({ queryKey: ['travel', id] })
    },
    ...options
  })
}

export const useLoadTravel = (id: string, options?: MutateOptions<any, Error, TravelLoadPayload>) => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (payload: TravelLoadPayload) => loadTravel(id, payload),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['travels'] })
      queryClient.invalidateQueries({ queryKey: ['travel', id] })
    },
    ...options
  })
}

export const useUnloadTravel = (id: string, options?: MutateOptions<any, Error, TravelUnloadPayload>) => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (payload: TravelUnloadPayload) => unloadTravel(id, payload),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['travels'] })
      queryClient.invalidateQueries({ queryKey: ['travel', id] })
    },
    ...options
  })
}

export const useReportBlocking = (id: string, options?: MutateOptions<any, Error, TravelBlockingPayload>) => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (payload: TravelBlockingPayload) => reportBlocking(id, payload),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['travels'] })
      queryClient.invalidateQueries({ queryKey: ['travel', id] })
    },
    ...options
  })
}

export const useEndBlocking = (id: string, options?: MutateOptions<any, Error, TravelBlockingPayload>) => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (payload: TravelBlockingPayload) => endBlocking(id, payload),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['travels'] })
      queryClient.invalidateQueries({ queryKey: ['travel', id] })
    },
    ...options
  })
}

export const useCancelTravel = (id: string, options?: MutateOptions<any, Error, TravelCancelPayload>) => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (payload: TravelCancelPayload) => cancelTravel(id, payload),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['travels'] })
      queryClient.invalidateQueries({ queryKey: ['travel', id] })
    },
    ...options
  })
}

export const useSetOnWay = (id: string, options?: MutateOptions<any, Error, TravelBasePayload>) => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (payload: TravelBasePayload) => setOnWay(id, payload),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['travels'] })
      queryClient.invalidateQueries({ queryKey: ['travel', id] })
    },
    ...options
  })
}

export const useUpdateTravel = (id: string, options?: MutateOptions<any, Error, TravelBasePayload>) => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (payload: TravelBasePayload) => updateTravel(id, payload),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['travels'] })
      queryClient.invalidateQueries({ queryKey: ['travel', id] })
    },
    ...options
  })
}

export const useDeleteTravel = (option?: MutateOptions<any, any, any>) => {
  return useMutation({
    mutationFn: (id: string) => deleteTravel(id),
    ...option
  })
}

export const useCreateTravel = (option?: MutateOptions<any, Error, TravelBasePayload>) => {
  return useMutation({
    mutationFn: (payload: TravelBasePayload) => createTravel(payload),
    ...option
  })
}

export const useTravelsDashboardStats = () => {
  return useQuery({
    queryKey: ['travels-dashboard-stats'],
    queryFn: () => getTravelsDashboardStats()
  })
}

export const useDeleteSelectedTravels = (option?: MutateOptions<any, any, any>) => {
  return useMutation({
    mutationFn: (ids: string[]) => deleteSelectedTravels(ids),
    ...option
  })
}
