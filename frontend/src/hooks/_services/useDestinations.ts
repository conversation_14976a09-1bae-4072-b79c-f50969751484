import { MutateOptions, useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import {
  createDestination,
  deleteDestination,
  deleteSelectedDestinations,
  fetchDestinations,
  getDestinationById,
  updateDestination
} from 'src/services/_service/destinations'
import { DestinationParams, DestinationRequestPayload } from 'src/types/models/_services/destinations'

export const useDestinations = (params: Partial<DestinationParams>) => {
  return useQuery({
    queryKey: ['destinations', params],
    queryFn: () => fetchDestinations(params)
  })
}

export const useDestinationById = (id: string) => {
  return useQuery({
    queryKey: ['destination', id],
    queryFn: () => getDestinationById(id),
    enabled: !!id
  })
}

export const useCreateDestination = (options?: MutateOptions<any, any, DestinationRequestPayload>) => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (payload: DestinationRequestPayload) => createDestination(payload),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['destinations'] })
    },
    ...options
  })
}

export const useUpdateDestination = (
  id: string,
  options?: MutateOptions<any, any, Partial<DestinationRequestPayload>>
) => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (payload: Partial<DestinationRequestPayload>) => updateDestination(id, payload),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['destinations'] })
      queryClient.invalidateQueries({ queryKey: ['destination', id] })
    },
    ...options
  })
}

export const useDeleteDestination = (options?: MutateOptions<any, any, string>) => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (id: string) => deleteDestination(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['destinations'] })
    },
    ...options
  })
}

export const useDeleteSelectedDestinations = (options?: MutateOptions<any, any, string[]>) => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (ids: string[]) => deleteSelectedDestinations(ids),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['destinations'] })
    },
    ...options
  })
}
