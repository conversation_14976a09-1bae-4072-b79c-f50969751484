import { MutateOptions, useInfiniteQuery, useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { debounce } from 'lodash'
import {
  createInvoice,
  createInvoiceItem,
  deleteInvoiceItem,
  fetchInvoiceItems,
  getInvoiceItemById
} from 'src/services/_service/invoice-items'
import {
  CreateInvoicePayload,
  FetchInvoiceItemsParams,
  InvoiceItemRequestPayload,
  InvoiceItemResponse
} from 'src/types/models/_services/invoice-items'
import { useCallback, useState } from 'react'

// Regular hook for table usage
export const useInvoiceItems = (params: Partial<FetchInvoiceItemsParams>) => {
  return useQuery<InvoiceItemResponse>({
    queryKey: ['invoice-items', params],
    queryFn: () => fetchInvoiceItems(params)
  })
}

// Infinite scroll hook
export const useInfiniteInvoiceItems = (initialParams?: Partial<FetchInvoiceItemsParams>) => {
  const [search, setSearch] = useState('')
  const queryClient = useQueryClient()

  const defaultParams: FetchInvoiceItemsParams = {
    limit: 100, // Changed from 10 to 100
    offset: 0,
    ...initialParams
  }

  const query = useInfiniteQuery({
    queryKey: ['infinite-invoice-items', { ...defaultParams, search }],
    queryFn: ({ pageParam = 0 }) =>
      fetchInvoiceItems({
        ...defaultParams,
        offset: pageParam,
        search
      }),
    getNextPageParam: (lastPage, allPages) => {
      const currentOffset = allPages.length * defaultParams.limit

      return lastPage.next ? currentOffset : undefined
    },
    initialPageParam: 0
  })

  const debouncedSearch = useCallback(
    (value: string) => {
      debounce((searchValue: string) => {
        setSearch(searchValue)
        queryClient.resetQueries({ queryKey: ['infinite-invoice-items'] })
      }, 300)(value)
    },
    [queryClient]
  )

  return {
    ...query,
    search,
    setSearch: debouncedSearch
  }
}

export const useInvoiceItemById = (id: string) => {
  return useQuery({
    queryKey: ['invoice-item', id],
    queryFn: () => getInvoiceItemById(id),
    enabled: !!id
  })
}

export const useDeleteInvoiceItem = (options?: MutateOptions<any, any, any>) => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ id }: { id: string }) => deleteInvoiceItem(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['invoice-items'] })
    },
    ...options
  })
}

export const useCreateInvoiceItem = (options?: MutateOptions<any, any, InvoiceItemRequestPayload>) => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (payload: InvoiceItemRequestPayload) => createInvoiceItem(payload),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['invoice-items'] })
    },
    ...options
  })
}

export const useCreateInvoice = (options?: MutateOptions<any, any, any>) => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (payload: CreateInvoicePayload) => createInvoice(payload),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['invoice-items'] })
    },
    ...options
  })
}
