import { MutateOptions, useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { api } from 'src/lib/api'
import {
  fetchOrders,
  getOrderById,
  createOrder,
  updateOrder,
  createOrderLines,
  deleteOrder,
  validateOrder,
  rejectOrder,
  requestValidation,
  addMissingTrips,
  getOrdersStats,
  ordersDashboardStats
} from 'src/services/_service/orders'
import {
  OrderFormValues,
  OrderOperationLine,
  OrderParams,
  OrderRequestPayload
} from 'src/types/models/_services/orders'

export const useOrders = (params: Partial<OrderParams>) => {
  return useQuery({
    queryKey: ['orders', params],
    queryFn: () => fetchOrders(params)
  })
}

export const useOrderById = (id: string) => {
  return useQuery({
    queryKey: ['order', id],
    queryFn: () => getOrderById(id),
    enabled: !!id
  })
}

export const useOrderStats = () => {
  return useQuery({
    queryKey: ['order-stats'],
    queryFn: () => getOrdersStats()
  })
}

export const useCreateOrder = (options?: MutateOptions<any, any, OrderFormValues>) => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async (payload: OrderFormValues) => {
      const res = await createOrder(payload)

      return res
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['orders'] })
    },
    ...options
  })
}

export const useGenerateOrderInvoice = (options?: MutateOptions<any, any, any>) => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async ({ for_order }: { for_order: string }) => {
      const res = await api.post('/mission-expenses/generate/', { for_order })

      return res
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['orders'] })
    },
    ...options
  })
}

export const useStartMissingTrip = (options?: MutateOptions<any, any, any>) => {
  return useMutation({
    mutationFn: ({ order_ids }: { order_ids: string[] }) => addMissingTrips(order_ids),
    ...options
  })
}

export const useUpdateOrder = (options?: MutateOptions<any, any, any>) => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async ({ id, data }: { id: string; data: OrderFormValues }) => {
      const res = await updateOrder(id, data)

      return res
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['orders'] })
    },

    ...options
  })
}

export const useUpdateValidationStatus = (options?: MutateOptions<any, any, any>) => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async ({ id, data }: { id: string; data: Partial<OrderFormValues> }) => {
      if (data.validation_status) {
        if (data.validation_status === 'REJETEE') {
          return await rejectOrder(id, data.rejection_reason!)
        } else if (data.validation_status === 'VALIDEE') {
          return await validateOrder(id)
        }
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['orders'] })
    },

    ...options
  })
}

export const useDeleteOrder = (options?: MutateOptions<any, any, any>) => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ id }: { id: string }) => deleteOrder(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['orders'] })
    },
    ...options
  })
}

export const useRequestValidation = (options?: MutateOptions<any, any, any>) => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ id }: { id: string }) => requestValidation(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['orders'] })
    },
    ...options
  })
}

export const useValidateOrder = (options?: MutateOptions<any, any, any>) => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ id }: { id: string }) => validateOrder(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['orders'] })
    },
    ...options
  })
}

export const useRejectOrder = (options?: MutateOptions<any, any, any>) => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ id, reason }: { id: string; reason: string }) => rejectOrder(id, reason),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['orders'] })
    },
    ...options
  })
}

export const useUpdateOrderLine = (options?: MutateOptions<any, any, any>) => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async ({ id, data }: { id: string; data: Partial<OrderOperationLine> }) => {
      const response = await api.patch(`/invoice-items/${id}/`, data)

      return response.data
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['orders'] })
      queryClient.invalidateQueries({ queryKey: ['order'] })
    },
    ...options
  })
}

export const useOrdersDashboardStats = () => {
  return useQuery({
    queryKey: ['orders-dashboard-stats'],
    queryFn: () => ordersDashboardStats()
  })
}
