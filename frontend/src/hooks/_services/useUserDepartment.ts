import { MutateOptions, useInfiniteQuery, useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { useCallback, useState } from 'react'
import { debounce } from 'lodash'
import {
  createUserDepartment,
  deleteSelectedUserDepartments,
  fetchUserDepartments,
  getUserDepartmentById,
  updateUserDepartment
} from 'src/services/user-department'
import { FetchUserDepartmentParams, UserDepartmentRequestPayload } from 'src/types/models/user-department'

export const useInfiniteUserDepartments = (initialParams?: Partial<FetchUserDepartmentParams>) => {
  const [search, setSearch] = useState('')
  const queryClient = useQueryClient()

  const defaultParams: FetchUserDepartmentParams = {
    limit: 100,
    offset: 0,
    ...initialParams
  }

  const query = useInfiniteQuery({
    queryKey: ['infinite-user-departments', { ...defaultParams, search }],
    queryFn: ({ pageParam = 0 }) =>
      fetchUserDepartments({
        ...defaultParams,
        offset: pageParam
      }),
    getNextPageParam: (lastPage, allPages) => {
      const currentOffset = allPages.length * defaultParams.limit

      return lastPage.next ? currentOffset : undefined
    },
    initialPageParam: 0
  })

  const debouncedSearch = useCallback(
    (value: string) => {
      debounce((searchValue: string) => {
        setSearch(searchValue)
        queryClient.resetQueries({ queryKey: ['infinite-user-departments'] })
      }, 300)(value)
    },
    [queryClient]
  )

  return {
    ...query,
    search,
    setSearch: debouncedSearch
  }
}

export const useUserDepartments = (params: Partial<FetchUserDepartmentParams>) => {
  return useQuery({
    queryKey: ['user-departments', params],
    queryFn: () => fetchUserDepartments(params)
  })
}

export const useUserDepartmentById = (id: string) => {
  return useQuery({
    queryKey: ['user-department', id],
    queryFn: () => getUserDepartmentById(id),
    enabled: !!id
  })
}

export const useDeleteSelectedUserDepartments = (options?: MutateOptions<any, any, string[]>) => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (ids: string[]) => deleteSelectedUserDepartments(ids),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['user-departments'] })
    },
    ...options
  })
}

export const useCreateUserDepartment = (options?: MutateOptions<any, any, UserDepartmentRequestPayload>) => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (payload: UserDepartmentRequestPayload) => createUserDepartment(payload),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['user-departments'] })
    },
    ...options
  })
}

export const useUpdateUserDepartment = (
  id: string,
  options?: MutateOptions<any, any, UserDepartmentRequestPayload>
) => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (payload: UserDepartmentRequestPayload) => updateUserDepartment(id, payload),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['user-departments'] })
      queryClient.invalidateQueries({ queryKey: ['user-department', id] })
    },
    ...options
  })
}
