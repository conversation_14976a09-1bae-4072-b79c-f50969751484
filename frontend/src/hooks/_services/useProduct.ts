import { MutateOptions, useMutation, useQuery } from '@tanstack/react-query'
import { createProduct, fetchProducts, getProductById, updateProduct } from 'src/services/_service/products'
import { ProductParams, ProuductRequestPayload } from 'src/types/models/_services/products'

export const useProducts = (params: Partial<ProductParams>) => {
  return useQuery({
    queryKey: ['products', params],
    queryFn: () => fetchProducts(params)
  })
}

export const useProductById = (id: string) => {
  return useQuery({
    queryKey: ['product', id],
    queryFn: () => getProductById(id),
    enabled: !!id
  })
}

export const useCreateProduct = (option: MutateOptions<any, any, ProuductRequestPayload>) => {
  return useMutation({
    mutationFn: (payload: ProuductRequestPayload) => {
      console.log(payload)

      return createProduct(payload)
    },
    ...option
  })
}

export const useEditProduct = (id: string, option: MutateOptions<any, any, Partial<ProuductRequestPayload>>) => {
  return useMutation({
    mutationFn: (payload: Partial<ProuductRequestPayload>) => updateProduct(id, payload),
    ...option
  })
}
