import { MutateOptions, useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import {
  createCountry,
  deleteCountry,
  fetchCountries,
  getCountryById,
  updateCountry
} from 'src/services/_service/countries'
import { CountryParams, CountryRequestPayload } from 'src/types/models/_services/countries'

export const useCountries = (params: Partial<CountryParams>) => {
  return useQuery({
    queryKey: ['countries', params],
    queryFn: () => fetchCountries(params)
  })
}

export const useCountryById = (id: string) => {
  return useQuery({
    queryKey: ['country', id],
    queryFn: () => getCountryById(id),
    enabled: !!id
  })
}

export const useCreateCountry = (options?: MutateOptions<any, any, CountryRequestPayload>) => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (payload: CountryRequestPayload) => createCountry(payload),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['countries'] })
    },
    ...options
  })
}

export const useUpdateCountry = (id: string, options?: MutateOptions<any, any, Partial<CountryRequestPayload>>) => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (payload: Partial<CountryRequestPayload>) => updateCountry(id, payload),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['countries'] })
      queryClient.invalidateQueries({ queryKey: ['country', id] })
    },
    ...options
  })
}

export const useDeleteCountry = (options?: MutateOptions<any, any, string>) => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (id: string) => deleteCountry(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['countries'] })
    },
    ...options
  })
}
