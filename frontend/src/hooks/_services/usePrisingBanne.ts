import { MutateOptions, useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import {
  createPricingBenne,
  deletePricingBenne,
  fetchPricingBenne,
  getPricingBenneById,
  updatePricingBenne
} from 'src/services/_service/pricing-banne'
import { FetchPricingBenneParams, PricingBenne } from 'src/types/models/_services/price-banne'

export const usePricingBenne = (params: Partial<FetchPricingBenneParams>) => {
  return useQuery({
    queryKey: ['pricing-benne', params],
    queryFn: () => fetchPricingBenne(params)
  })
}

export const usePricingBenneById = (id: string) => {
  return useQuery<PricingBenne>({
    queryKey: ['pricing-benne', id],
    queryFn: () => getPricingBenneById(id)
  })
}

export const useCreatePricingBenne = (options?: MutateOptions<any, any, any>) => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (payload: any) => createPricingBenne(payload),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['pricing-benne'] })
    },
    ...options
  })
}

export const useUpdatePricingBenne = (id: string, options?: MutateOptions<any, any, any>) => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (payload: any) => updatePricingBenne(id, payload),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['pricing-benne'] })
      queryClient.invalidateQueries({ queryKey: ['pricing-benne', id] })
    },
    ...options
  })
}

export const useDeletePricingBenne = (options?: MutateOptions<any, any, string>) => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (id: string) => deletePricingBenne(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['pricing-benne'] })
    },
    ...options
  })
}
