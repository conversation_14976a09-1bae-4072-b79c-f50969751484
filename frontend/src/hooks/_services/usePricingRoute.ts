import { MutateOptions, useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import {
  createPricingRoute,
  deletePricingRoute,
  fetchPricingRoute,
  getPricingRouteById,
  updatePricingRoute
} from 'src/services/_service/pricing-route'
import { FetchPricingRouteParams, PricingRoute } from 'src/types/models/_services/pricing-route'

export const usePricingRoute = (params: Partial<FetchPricingRouteParams>) => {
  return useQuery({
    queryKey: ['pricing-route', params],
    queryFn: () => fetchPricingRoute(params)
  })
}

export const usePricingRouteById = (id: string) => {
  return useQuery<PricingRoute>({
    queryKey: ['pricing-route', id],
    queryFn: () => getPricingRouteById(id),
    enabled: !!id
  })
}

export const useCreatePricingRoute = (options?: MutateOptions<any, any, any>) => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (payload: any) => createPricingRoute(payload),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['pricing-route'] })
    },
    ...options
  })
}

export const useUpdatePricingRoute = (id: string, options?: MutateOptions<any, any, any>) => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (payload: any) => updatePricingRoute(id, payload),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['pricing-route'] })
      queryClient.invalidateQueries({ queryKey: ['pricing-route', id] })
    },
    ...options
  })
}

export const useDeletePricingRoute = (options?: MutateOptions<any, any, string>) => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (id: string) => deletePricingRoute(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['pricing-route'] })
    },
    ...options
  })
}
