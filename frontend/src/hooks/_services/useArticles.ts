import { MutateOptions, useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import {
  fetchArticles,
  getArticleById,
  createArticle,
  updateArticle,
  deleteArticle,
  deleteSelectedArticles
} from 'src/services/_service/article'
import { FetchArticlesParams, Article, ArticleFormData } from 'src/types/models/_services/article'

export const useArticles = (params: Partial<FetchArticlesParams>) => {
  return useQuery({
    queryKey: ['articles', params],
    queryFn: () => fetchArticles(params)
  })
}

export const useArticleById = (id: string) => {
  return useQuery<Article>({
    queryKey: ['article', id],
    queryFn: () => getArticleById(id),
    enabled: !!id
  })
}

export const useCreateArticle = (options?: MutateOptions<any, any, ArticleFormData>) => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (payload: ArticleFormData) => createArticle(payload),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['articles'] })
    },
    ...options
  })
}

export const useUpdateArticle = (options?: MutateOptions<any, any, any>) => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: ArticleFormData }) => updateArticle(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['articles'] })
    },
    ...options
  })
}

export const useDeleteArticle = (options?: MutateOptions<any, any, string>) => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (id: string) => deleteArticle(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['articles'] })
    },
    ...options
  })
}

export const useDeleteSelectedArticles = (options?: MutateOptions<any, any, string[]>) => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (ids: string[]) => deleteSelectedArticles(ids),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['articles'] })
    },
    ...options
  })
}
