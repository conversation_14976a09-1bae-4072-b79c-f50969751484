import { MutationOptions, useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { addToOdoo, createInvoice, fetchInvoices, getInvoiceById, getInvoiceStats } from 'src/services/invoices'
import { CreateInvoiceData, FetchInvoiceParams, Invoice } from 'src/types/models/invoice'

export const useInvoices = (params: Partial<FetchInvoiceParams>) => {
  return useQuery({
    queryKey: ['invoices', params],
    queryFn: () => fetchInvoices(params)
  })
}

export const useInvoiceById = (id: string) => {
  return useQuery<Invoice, Error>({
    queryKey: ['invoice', id],
    queryFn: () => getInvoiceById(id),
    enabled: !!id
  })
}

export const useInvoiceStats = () => {
  return useQuery({
    queryKey: ['invoice-stats'],
    queryFn: () => getInvoiceStats()
  })
}

export const useCreateInvoice = (options?: MutationOptions<any, Error, CreateInvoiceData>) => {
  return useMutation({
    mutationFn: async (data: CreateInvoiceData) => await createInvoice(data),
    ...options
  })
}

export const useAddToOdoo = (options?: MutationOptions<any, Error, { invoice_ids: string[] }>) => {
  return useMutation({
    mutationFn: async (data: { invoice_ids: string[] }) => await addToOdoo(data),
    ...options
  })
}
