import { useQuery } from '@tanstack/react-query'
import { fetchTrucks, getTruckById } from 'src/services/truck'
import { TruckParam } from 'src/types/models/truck'

export const useGetAllTrucks = (params: TruckParam) => {
  return useQuery({
    queryKey: ['trucks', params],
    queryFn: () => fetchTrucks(params)
  })
}

export const useGetTruckById = (id: string) => {
  return useQuery({
    queryKey: ['truck', id],
    queryFn: () => getTruckById(id),
    enabled: !!id
  })
}
