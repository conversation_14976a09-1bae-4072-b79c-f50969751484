import { MutateOptions, useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import {
  createNatureOfArticle,
  deleteNatureOfArticle,
  deleteSelectedNatureOfArticles,
  fetchNatureOfArticles,
  getNatureOfArticleById,
  updateNatureOfArticle
} from '../../services/_service/nature-of-articles'
import {
  FetchNatureOfArticlesParams,
  NatureOfArticleRequestPayload
} from '../../types/models/_services/nature-of-articles'

export const useNatureOfArticles = (params: Partial<FetchNatureOfArticlesParams>) => {
  return useQuery({
    queryKey: ['nature-of-articles', params],
    queryFn: () => fetchNatureOfArticles(params)
  })
}

export const useNatureOfArticleById = (id: string) => {
  return useQuery({
    queryKey: ['nature-of-article', id],
    queryFn: () => getNatureOfArticleById(id),
    enabled: !!id
  })
}

export const useCreateNatureOfArticle = (options?: MutateOptions<any, any, NatureOfArticleRequestPayload>) => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (payload: NatureOfArticleRequestPayload) => createNatureOfArticle(payload),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['nature-of-articles'] })
    },
    ...options
  })
}

export const useUpdateNatureOfArticle = (
  id: string,
  options?: MutateOptions<any, any, Partial<NatureOfArticleRequestPayload>>
) => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (payload: Partial<NatureOfArticleRequestPayload>) => updateNatureOfArticle(id, payload),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['nature-of-articles'] })
      queryClient.invalidateQueries({ queryKey: ['nature-of-article', id] })
    },
    ...options
  })
}

export const useDeleteNatureOfArticle = (options?: MutateOptions<any, any, string>) => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (id: string) => deleteNatureOfArticle(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['nature-of-articles'] })
    },
    ...options
  })
}

export const useDeleteSelectedNatureOfArticles = (options?: MutateOptions<any, any, string[]>) => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (ids: string[]) => deleteSelectedNatureOfArticles(ids),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['nature-of-articles'] })
    },
    ...options
  })
}
