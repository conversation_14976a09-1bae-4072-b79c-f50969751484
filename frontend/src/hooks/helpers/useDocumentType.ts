import { MutateOptions, useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import {
  createDocumentType,
  deleteDocumentType,
  deleteSelectedDocumentType,
  fetchDocumentType,
  fetchDocumentTypeById,
  updateDocumentType
} from '../../services/helpers/document-type'
import { BaseFetchParams } from 'src/types/models'

export const useDocumentTypes = (params: Partial<BaseFetchParams>) => {
  return useQuery({
    queryFn: () => fetchDocumentType(),
    queryKey: ['documentType']
  })
}

export const useDocumentTypeById = (id: string) => {
  return useQuery({
    queryKey: ['documentType', id],
    queryFn: () => fetchDocumentTypeById(id),
    enabled: !!id
  })
}

export const useCreateDocumentType = (options?: MutateOptions<any, any, any>) => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (data: any) => createDocumentType(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['documentType'] })
    },
    ...options
  })
}

export const useUpdateDocumentType = (id: string, options?: MutateOptions<any, any, any>) => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (data: any) => updateDocumentType(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['documentType'] })
      queryClient.invalidateQueries({ queryKey: ['documentType', id] })
    },
    ...options
  })
}

export const useDeleteDocumentType = (options?: MutateOptions<any, any, string>) => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (id: string) => deleteDocumentType(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['documentType'] })
    },
    ...options
  })
}

export const useDeleteSelectedDocumentType = (options?: MutateOptions<any, any, string[]>) => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (ids: string[]) => deleteSelectedDocumentType(ids),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['documentType'] })
    },
    ...options
  })
}
