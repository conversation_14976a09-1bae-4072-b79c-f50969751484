import { MutateOptions, useMutation, useQuery } from '@tanstack/react-query'
import { api } from '../../lib/api'
import { createRoute, deleteRoute, deleteSelectedRoutes, updateRoute } from 'src/services/helpers/routes'

interface Route {
  id: string
  name: string
  origin: string
  destination: string
  distance?: number
  estimated_time?: number
}

interface FetchRoutesParams {
  limit: number
  offset: number
  search?: string
}

interface RoutesResponse {
  count: number
  results: Route[]
}

const fetchRoutes = async (params: Partial<FetchRoutesParams>) => {
  const response = await api.get<RoutesResponse>('/trajet/', { params })

  return response.data
}

const getRouteById = async (id: string) => {
  const response = await api.get<Route>(`/trajet/${id}/`)

  return response.data
}

export const useRoutes = (params: Partial<FetchRoutesParams>) => {
  return useQuery({
    queryKey: ['routes', params],
    queryFn: () => fetchRoutes(params)
  })
}

export const useRouteById = (id: string) => {
  return useQuery({
    queryKey: ['route', id],
    queryFn: () => getRouteById(id)
  })
}

export const useCreateRoute = (options?: MutateOptions<any, any, any>) => {
  return useMutation({
    mutationFn: (data: any) => createRoute(data),
    ...options
  })
}

export const useUpdateRoute = (id: string, options?: MutateOptions<any, any, any>) => {
  return useMutation({
    mutationFn: (data: any) => updateRoute(id, data),
    ...options
  })
}

export const useDeleteRoute = (options?: MutateOptions<any, any, any>) => {
  return useMutation({
    mutationFn: (id: string) => deleteRoute(id),
    ...options
  })
}

export const useDeleteSelected = (options?: MutateOptions<any, any, any>) => {
  return useMutation({
    mutationFn: (ids: string[]) => deleteSelectedRoutes(ids),
    ...options
  })
}
