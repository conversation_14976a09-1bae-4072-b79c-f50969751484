import { MutateOptions, useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import {
  createGroupArticle,
  deleteGroupArticle,
  fetchGroupArticles,
  getGroupArticleById,
  updateGroupArticle
} from '../../services/_service/group-articles'
import { FetchGroupArticlesParams, GroupArticleRequestPayload } from '../../types/models/_services/group-article'

export const useGroupArticles = (params: Partial<FetchGroupArticlesParams>) => {
  return useQuery({
    queryKey: ['groupArticles', params],
    queryFn: () => fetchGroupArticles(params)
  })
}

export const useGroupArticleById = (id: string) => {
  return useQuery({
    queryKey: ['groupArticle', id],
    queryFn: () => getGroupArticleById(id),
    enabled: !!id
  })
}

export const useCreateGroupArticle = (options?: MutateOptions<any, any, GroupArticleRequestPayload>) => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (payload: GroupArticleRequestPayload) => createGroupArticle(payload),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['groupArticles'] })
    },
    ...options
  })
}

export const useUpdateGroupArticle = (
  id: string,
  options?: MutateOptions<any, any, Partial<GroupArticleRequestPayload>>
) => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (payload: Partial<GroupArticleRequestPayload>) => updateGroupArticle(id, payload),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['groupArticles'] })
      queryClient.invalidateQueries({ queryKey: ['groupArticle', id] })
    },
    ...options
  })
}

export const useDeleteGroupArticle = (options?: MutateOptions<any, any, string>) => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (id: string) => deleteGroupArticle(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['groupArticles'] })
    },
    ...options
  })
}
