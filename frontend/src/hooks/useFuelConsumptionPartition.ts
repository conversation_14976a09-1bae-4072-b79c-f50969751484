import { MutateOptions, useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import {
  createFuelConsumptionPartition,
  deleteFuelConsumptionPartition,
  fetchFuelConsumptionPartitions,
  getFuelConsumptionPartitionById,
  updateFuelConsumptionPartition
} from 'src/services/fuel-consumption-partition'
import { FetchFuelConsumptionPartitionParams } from 'src/types/models/fuel-consumption-partition'

export const useFuelConsumptionPartitions = (params: Partial<FetchFuelConsumptionPartitionParams>) => {
  return useQuery({
    queryKey: ['fuel-consumption-partitions', params],
    queryFn: () => fetchFuelConsumptionPartitions(params)
  })
}

export const useCreateFuelConsumptionPartition = (options?: MutateOptions<any, Error, any>) => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (payload: any) => createFuelConsumptionPartition(payload),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ['fuel-consumption-partitions']
      })
    },
    ...options
  })
}

export const useUpdateFuelConsumptionPartition = (options?: MutateOptions<any, Error, any>) => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: any }) => updateFuelConsumptionPartition(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['fuel-consumption-partitions'] })
    },
    ...options
  })
}

export const useDeleteFuelConsumptionPartition = (options?: MutateOptions<any, Error, any>) => {
  return useMutation({
    mutationFn: ({ id }: { id: string }) => deleteFuelConsumptionPartition(id),

    ...options
  })
}
