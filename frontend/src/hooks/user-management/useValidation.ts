import { MutateOptions, useMutation, useQuery } from '@tanstack/react-query'
import {
  createValidation,
  deleteSelectedValidations,
  fetchValidation,
  getValidationById,
  updateValidation
} from 'src/services/users/validation'
import { BaseFetchParams } from 'src/types/models'

export const useValidation = (params: Partial<BaseFetchParams>) => {
  return useQuery({
    queryKey: ['validations', params],
    queryFn: () => fetchValidation(params)
  })
}

export const useValidationById = (id: string) => {
  return useQuery({
    queryKey: ['validation', id],
    queryFn: () => getValidationById(id),
    enabled: !!id
  })
}

export const useCreateDataValidation = (options: MutateOptions<any, any, any>) => {
  return useMutation({
    mutationFn: (data: any) => createValidation(data),
    ...options
  })
}

export const useUpdateDataValidation = (options: MutateOptions<any, any, any>) => {
  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: any }) => updateValidation(id, data),
    ...options
  })
}

export const useDeleteValidation = (options: MutateOptions<any, any, any>) => {
  return useMutation({
    mutationFn: (id: string[]) => deleteSelectedValidations(id),
    ...options
  })
}
