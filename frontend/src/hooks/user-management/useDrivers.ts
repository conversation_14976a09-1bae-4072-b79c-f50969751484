import { MutateOptions, useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import {
  deleteDriver,
  fetchDriver,
  getDriverById,
  updateDriver,
  createDriver,
  deleteSelectedDrivers
} from 'src/services/drivers'
import { Driver, DriverFormData, FetchDriverParams } from 'src/types/models/drivers'

export const useDrivers = (params: Partial<FetchDriverParams>) => {
  return useQuery({
    queryKey: ['drivers', params],
    queryFn: () => fetchDriver(params)
  })
}

export const useDriverById = (id: string) => {
  return useQuery<Driver>({
    queryKey: ['driver', id],
    queryFn: () => getDriverById(id),
    enabled: !!id
  })
}

export const useCreateDriver = (options?: MutateOptions<any, Error, DriverFormData>) => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (data: DriverFormData) => createDriver(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['drivers'] })
    },
    ...options
  })
}

export const useUpdateDriver = (options?: MutateOptions<any, Error, { id: string; data: Partial<DriverFormData> }>) => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: Partial<DriverFormData> }) => updateDriver(data, id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['drivers'] })
    },
    ...options
  })
}

export const useDeleteDriver = (options?: MutateOptions<any, any, string>) => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (id: string) => deleteDriver(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['drivers'] })
    },
    ...options
  })
}

export const useDeleteSelectedDrivers = (options?: MutateOptions<any, any, string[]>) => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (ids: string[]) => deleteSelectedDrivers(ids),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['drivers'] })
    },
    ...options
  })
}
