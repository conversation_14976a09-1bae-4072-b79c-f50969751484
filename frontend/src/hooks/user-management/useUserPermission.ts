import { useQuery } from '@tanstack/react-query'
import { fetchPermissions } from 'src/services/permission'
import { BaseFetchParams } from 'src/types/models'

import { useState, useCallback } from 'react'
import { useInfiniteQuery, useQueryClient } from '@tanstack/react-query'
import { debounce } from 'lodash'

export const useInfiniteUserPermissions = (initialParams?: Partial<BaseFetchParams>) => {
  const [search, setSearch] = useState('')
  const queryClient = useQueryClient()

  const defaultParams: BaseFetchParams = {
    limit: 100,
    offset: 0,
    ...initialParams
  }

  const query = useInfiniteQuery({
    queryKey: ['infinite-user-permissions', { ...defaultParams, search }],
    queryFn: ({ pageParam = 0 }) =>
      fetchPermissions({
        ...defaultParams,
        offset: pageParam
      }),
    getNextPageParam: (lastPage, allPages) => {
      const currentOffset = allPages.length * defaultParams.limit

      return lastPage.next ? currentOffset : undefined
    },
    initialPageParam: 0
  })

  const debouncedSearch = useCallback(
    (value: string) => {
      debounce((searchValue: string) => {
        setSearch(searchValue)
        queryClient.resetQueries({ queryKey: ['infinite-user-permissions'] })
      }, 300)(value)
    },
    [queryClient]
  )

  return {
    ...query,
    search,
    setSearch: debouncedSearch
  }
}

export const useUserPermissions = (params: Partial<BaseFetchParams>) => {
  return useQuery({
    queryKey: ['user-permissions', params],
    queryFn: () => fetchPermissions(params)
  })
}
