import { MutateOptions, useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import {
  activateSelected,
  changeAdminPassword,
  changePassword,
  createUser,
  deactivateSelected,
  deleteSelectedUsers,
  deleteUser,
  fetchUsers,
  getUserById,
  updateProfileImage,
  updateUser,
  userSupervisors
} from 'src/services/users/users'
import { BaseFetchParams } from 'src/types/models'
import { PasswordPayload, UserRequestPayload } from 'src/types/models/user-management/users'

export const useUsers = (params: Partial<BaseFetchParams>) => {
  return useQuery({
    queryKey: ['users', params],
    queryFn: () => fetchUsers(params)
  })
}

export const useUserSupervisors = (params: Partial<BaseFetchParams>) => {
  return useQuery({
    queryKey: ['user-supervisors', params],
    queryFn: () => userSupervisors(params)
  })
}

export const useUserById = (id: string) => {
  return useQuery({
    queryKey: ['user', id],
    queryFn: () => getUserById(id),
    enabled: !!id
  })
}

export const useCreateUser = (options?: MutateOptions<any, any, UserRequestPayload>) => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (data: UserRequestPayload) => createUser(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['users'] })
    },
    ...options
  })
}

export const useAdminChangePassword = (options?: MutateOptions<any, any, { id: string; data: PasswordPayload }>) => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: PasswordPayload }) => changeAdminPassword(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['users'] })
    },
    ...options
  })
}

export const useChangePassword = (options?: MutateOptions<any, any, { id: string; data: PasswordPayload }>) => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: PasswordPayload }) => changePassword(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['users'] })
    },
    ...options
  })
}

export const useUpdateUser = (options?: MutateOptions<any, any, { id: string; data: Partial<UserRequestPayload> }>) => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: Partial<UserRequestPayload> }) => updateUser(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['users'] })
    },
    ...options
  })
}

export const useDeleteUser = (options?: MutateOptions<any, any, string>) => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (id: string) => deleteUser(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['users'] })
    },
    ...options
  })
}

export const useDeleteSelectedUsers = (options?: MutateOptions<any, any, string[]>) => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (ids: string[]) => deleteSelectedUsers(ids),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['users'] })
    },
    ...options
  })
}

export const useActivateSelectedUsers = (options?: MutateOptions<any, any, string[]>) => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (ids: string[]) => activateSelected(ids),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['users'] })
    },
    ...options
  })
}

export const useDeactivateSelectedUsers = (options?: MutateOptions<any, any, string[]>) => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (ids: string[]) => deactivateSelected(ids),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['users'] })
    },
    ...options
  })
}

export const useUpdateProfileImage = (options?: MutateOptions<any, any, { image: any; id: string }>) => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: data => updateProfileImage(data.image, data.id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['users'] })
    },
    ...options
  })
}
