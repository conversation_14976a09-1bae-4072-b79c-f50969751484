import { useQuery } from '@tanstack/react-query'
import { fetchPermissions } from 'src/services/permission'
import { BaseFetchParams } from 'src/types/models'
import { PermissionData } from 'src/types/models/permission'

export const useFetchPermissions = (params: Partial<BaseFetchParams>) => {
  return useQuery<PermissionData>({
    queryKey: ['permissions', params],
    queryFn: () => fetchPermissions(params)
  })
}
