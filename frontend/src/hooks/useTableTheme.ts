import React, { useMemo } from 'react'
import { themeQuartz } from 'ag-grid-community'
import { useTheme } from '@mui/material'
import { hexToRGBA } from 'src/@core/utils/hex-to-rgba'

export default function useTableTheme() {
  const theme = useTheme()
  const isDarkMode = theme.palette.mode === 'dark'

  const tableTheme = useMemo(
    () =>
      themeQuartz.withParams({
        backgroundColor: theme.palette.background.paper,
        foregroundColor: theme.palette.text.primary,

        headerBackgroundColor: theme.palette.primary.main,
        headerTextColor: theme.palette.common.white,
        headerFontWeight: 600,
        headerColumnResizeHandleColor: theme.palette.divider,
        oddRowBackgroundColor: isDarkMode ? '#2F3349' : '#F8F7FA',
        rowHoverColor: isDarkMode
          ? hexToRGBA(theme.palette.common.white, 0.08)
          : hexToRGBA(theme.palette.common.black, 0.04),
        selectedRowBackgroundColor: isDarkMode
          ? hexToRGBA(theme.palette.primary.main, 0.16)
          : hexToRGBA(theme.palette.primary.main, 0.08),

        // Border colors
        borderColor: isDarkMode ? 'rgba(208, 212, 241, 0.16)' : 'rgba(47, 43, 61, 0.16)',

        // Input colors (for filters)
        inputBackgroundColor: theme.palette.background.paper,
        inputTextColor: theme.palette.text.primary,

        // Checkbox colors

        // Typography
        fontFamily: theme.typography.fontFamily,
        fontSize: `${theme.typography.body2.fontSize}`,

        // Menu colors
        menuBackgroundColor: theme.palette.background.paper,
        menuTextColor: theme.palette.text.primary,

        // Cell colors
        cellTextColor: theme.palette.text.primary

        // Disabled state
      }),
    [theme, isDarkMode]
  )

  return { tableTheme }
}
