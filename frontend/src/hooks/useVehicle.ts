import { MutateOptions, useQuery } from '@tanstack/react-query'
import { fetchVehicles, getVehicleById, deleteVehicle, updateVehicle, createVehicle } from 'src/services/vehicles'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { FetchVehiclesParams, VehicleRequest } from 'src/types/models/vehicles'

export const useGetAllVehicles = (params: Partial<FetchVehiclesParams>) => {
  return useQuery({
    queryKey: ['vehicles', params],
    queryFn: () => fetchVehicles(params)
  })
}

export const useCreateVehicle = (options?: MutateOptions<any, Error, VehicleRequest>) => {
  return useMutation<any, Error, VehicleRequest>({
    mutationFn: (data: VehicleRequest) => createVehicle(data),
    ...options
  })
}

export const useGetVehicleById = (id: string) => {
  return useQuery({
    queryKey: ['vehicle', id],
    queryFn: () => getVehicleById(id),
    enabled: !!id
  })
}

export const useDeleteVehicle = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (id: string) => deleteVehicle(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['vehicles'] })
    }
  })
}

export const useUpdateVehicle = (options?: MutateOptions<any, Error, { data: VehicleRequest; id?: string }>) => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ id, data }: { id?: string; data: VehicleRequest }) => updateVehicle(data, id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['vehicles'] })
    }
  })
}
