import { MutateOptions, useInfiniteQuery, useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { useCallback, useState } from 'react'
import { debounce } from 'lodash'
import { createGroup, fetchGroups, getGroupById, updateGroup } from 'src/services/groups'
import { BaseFetchParams } from 'src/types/models'

interface CreateGroupData {
  name: string
  permissions: number[]
}

export const useInfiniteGroups = (initialParams?: Partial<BaseFetchParams>) => {
  const [search, setSearch] = useState('')
  const queryClient = useQueryClient()

  const defaultParams: BaseFetchParams = {
    limit: 100,
    offset: 0,
    ...initialParams
  }

  const query = useInfiniteQuery({
    queryKey: ['infinite-groups', { ...defaultParams, search }],
    queryFn: ({ pageParam = 0 }) =>
      fetchGroups({
        ...defaultParams,
        offset: pageParam
      }),
    getNextPageParam: (lastPage, allPages) => {
      const currentOffset = allPages.length * defaultParams.limit

      return lastPage.next ? currentOffset : undefined
    },
    initialPageParam: 0
  })

  const debouncedSearch = useCallback(
    (value: string) => {
      debounce((searchValue: string) => {
        setSearch(searchValue)
        queryClient.resetQueries({ queryKey: ['infinite-groups'] })
      }, 300)(value)
    },
    [queryClient]
  )

  return {
    ...query,
    search,
    setSearch: debouncedSearch
  }
}

export const useGroups = (params: Partial<BaseFetchParams>) => {
  return useQuery({
    queryKey: ['groups', params],
    queryFn: () => fetchGroups(params)
  })
}

export const useCreateGroup = (options: MutateOptions<any, any, any>) => {
  return useMutation({
    mutationFn: (data: CreateGroupData) => createGroup(data),
    ...options
  })
}

export const useGroupById = (id: string) => {
  return useQuery({
    queryFn: () => getGroupById(id),
    queryKey: ['group', id]
  })
}

export const useUpdateGroup = (id: string, options: MutateOptions<any, any, any>) => {
  return useMutation({
    mutationFn: (data: CreateGroupData) => updateGroup(id, data),
    ...options
  })
}

export const useDeleteGroup = (id: string, options: MutateOptions<any, any, any>) => {
  return useMutation({
    mutationFn: (data: CreateGroupData) => updateGroup(id, data),
    ...options
  })
}
