import { useQueryClient } from '@tanstack/react-query'
import { useSelector } from 'react-redux'
import { selectUser } from 'src/store/auth'
import { useOrderById } from './_services/useOrders'

export const useCheckAuthorValidation = (orderId: string) => {
  const user = useSelector(selectUser)
  const { data: orderData } = useOrderById(orderId)

  return {
    author: orderData?.created_by.id === user?.id ? true : false,
    isValidator: user?.is_validator ? true : false
  }
}
