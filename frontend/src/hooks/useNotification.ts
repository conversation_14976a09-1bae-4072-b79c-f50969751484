import { useQuery, useMutation, useQueryClient, MutateOptions } from '@tanstack/react-query'
import {
  fetchNotifications,
  getNotificationById,
  deleteNotification,
  deleteSelectedNotifications
} from 'src/services/notifications'
import { FetchNotificationParams } from 'src/types/models/notification'

export const useNotifications = (params: Partial<FetchNotificationParams>) => {
  return useQuery({
    queryKey: ['notifications', params],
    queryFn: () => fetchNotifications(params)
  })
}

export const useNotification = (id: string) => {
  return useQuery({
    queryKey: ['notification', id],
    queryFn: () => getNotificationById(id),
    enabled: !!id
  })
}

export const useDeleteNotification = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (id: string) => deleteNotification(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['notifications'] })
    }
  })
}

export const useDeleteSelectedNotifications = (options?: MutateOptions<any, any, string[]>) => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (ids: string[]) => deleteSelectedNotifications(ids),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['notifications'] })
    },
    ...options
  })
}
