import { FetchStationsParams, StationRequestPayload } from 'src/types/models/_services/stations'
import { MutateOptions, useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import {
  deleteStation,
  fetchStations,
  getStationById,
  updateStation,
  createStation,
  deleteSelectedStations
} from 'src/services/station-service'
import { bulkStationCreate } from 'src/services/_service/stations'

export const useFetchStations = (params: Partial<FetchStationsParams>) => {
  return useQuery({
    queryKey: ['stations', params],
    queryFn: () => fetchStations(params)
  })
}

export const useStationById = (id: string) => {
  return useQuery({
    queryKey: ['station', id],
    queryFn: () => getStationById(id),
    enabled: !!id
  })
}

export const useCreateStation = (options?: MutateOptions<any, Error, StationRequestPayload>) => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (data: StationRequestPayload) => createStation(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['stations'] })
    },
    ...options
  })
}

export const useUpdateStation = (
  options?: MutateOptions<any, Error, { id: string; data: Partial<StationRequestPayload> }>
) => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: Partial<StationRequestPayload> }) => updateStation(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['stations'] })
    },
    ...options
  })
}

export const useDeleteStation = (options?: MutateOptions<any, Error, string>) => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (id: string) => deleteStation(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['stations'] })
    },
    ...options
  })
}

export const useDeleteSelectedStations = (options?: MutateOptions<any, Error, string[]>) => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (ids: string[]) => deleteSelectedStations(ids),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['stations'] })
    },
    ...options
  })
}

export const useBulkStationCreate = (options?: MutateOptions<any, Error, { payload: StationRequestPayload[] }>) => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ payload }) => bulkStationCreate(payload),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['stations'] })
    },
    ...options
  })
}
