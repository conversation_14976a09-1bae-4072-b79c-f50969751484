import { useTheme } from '@mui/material/styles'

interface ThemeImageProps {
  lightImage: string
  darkImage: string
  fallback?: string
}

const useThemeImage = ({ lightImage, darkImage, fallback = '/images/logo_light.png' }: ThemeImageProps) => {
  const theme = useTheme()

  const getImageSource = () => {
    if (theme.palette.mode === 'dark') {
      return darkImage || fallback
    }

    return lightImage || fallback
  }

  return { imageSource: getImageSource() }
}

export default useThemeImage
