import { MissionExpense, MissionExpenseParams } from './../types/models/mission-expense'
import { MutateOptions, useMutation, useQuery } from '@tanstack/react-query'
import {
  cancelMissionExpeses,
  deleteSelectedMissionExpense,
  fetchMissionExpenses,
  getMissionExpenseById,
  getMissionExpensesDashboardStats,
  getMissionExpensesStats
} from 'src/services/mission-expense'

export const useMissionExpenses = (params: Partial<MissionExpenseParams>) => {
  return useQuery({
    queryKey: ['mission-expenses', params],
    queryFn: () => fetchMissionExpenses(params)
  })
}

export const useMissionExpensesStats = () => {
  return useQuery({
    queryKey: ['mission-expenses-stats'],
    queryFn: () => getMissionExpensesStats()
  })
}

export const useMissionExpenseById = (id: string) => {
  return useQuery<MissionExpense>({
    queryKey: ['mission-expense', id],
    queryFn: () => getMissionExpenseById(id),
    enabled: !!id
  })
}

export const useCancelMissionExpenses = (options: MutateOptions<any, any, any>) => {
  return useMutation({
    mutationFn: ({ mission_expense_ids }: { mission_expense_ids: string[] }) =>
      cancelMissionExpeses(mission_expense_ids),
    ...options
  })
}

export const useDeleteSelectedMissionExpenses = (options: MutateOptions<any, any, any>) => {
  return useMutation({
    mutationFn: ({ ids }: { ids: string[] }) => deleteSelectedMissionExpense(ids),
    ...options
  })
}

export const useMissionExpensesDashboardStats = () => {
  return useQuery({
    queryKey: ['mission-expenses-dashboard-stats'],
    queryFn: () => getMissionExpensesDashboardStats()
  })
}
