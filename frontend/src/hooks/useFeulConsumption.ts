import { MutateOptions, useQuery } from '@tanstack/react-query'
import {
  deleteFuelConsumption,
  deleteSelectedFuelConsumption,
  fetchFuelConsumptions,
  getFuelConsumptionById
} from 'src/services/fuel-consumption'
import { FetchFuelConsumptionParams, FuelConsumption } from 'src/types/models/fuel-consumption'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { createFuelConsumption, updateFuelConsumption } from 'src/services/fuel-consumption'
import { useCreateFuelConsumptionPartition, useUpdateFuelConsumptionPartition } from './useFuelConsumptionPartition'
import { createFuelConsumptionPartition } from 'src/services/fuel-consumption-partition'
import { ConsumptionFormData } from 'src/types/models/fuel-consumption-partition'

export const useFuelConsumptions = (params: Partial<FetchFuelConsumptionParams>) => {
  return useQuery({
    queryKey: ['fuel-consumptions', params],
    queryFn: () => fetchFuelConsumptions(params)
  })
}

export const useFuelConsumptionById = (id: string) => {
  return useQuery<FuelConsumption>({
    queryKey: ['fuel-consumption', id],
    enabled: !!id,
    queryFn: () => getFuelConsumptionById(id)
  })
}

export const useCreateFuelConsumption = (options?: MutateOptions<any, Error, { data: ConsumptionFormData }>) => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async ({ data }) => {
      const response = await createFuelConsumption({
        type_conso: data.type_conso,
        validation: data.validation,
        operation: data.operation,
        repartitions: data.distributions
      })

      return response
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['fuel-consumptions'] })
    },
    ...options
  })
}

export const useUpdateFuelConsumption = (
  options?: MutateOptions<any, Error, { id: string; data: ConsumptionFormData }>
) => {
  const queryClient = useQueryClient()
  const createPartition = useCreateFuelConsumptionPartition()
  const updatePartition = useUpdateFuelConsumptionPartition()

  return useMutation({
    mutationFn: async ({ data, id }) => {
      const response = await updateFuelConsumption(id, {
        type_conso: data.type_conso,
        validation: data.validation,
        operation: data.operation,
        repartitions: data.distributions
      })

      return response
    },

    ...options
  })
}

export const useDeleteFuelConsumption = (options?: MutateOptions<any, Error, { id: string }>) => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ id }: { id: string }) => deleteFuelConsumption(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['fuel-consumptions'] })
    }
  })
}

export const useDeleteSelectedFuelConsumption = (options?: MutateOptions<any, Error, string[]>) => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (ids: string[]) => deleteSelectedFuelConsumption(ids),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['fuel-consumptions'] })
    },
    ...options
  })
}
