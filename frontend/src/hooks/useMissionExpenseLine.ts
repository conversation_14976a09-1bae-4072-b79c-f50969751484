import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import {
  createMissionExpenseLine,
  deleteMissionExpenseLine,
  fetchMissionExpenseLines,
  getMissionExpenseLineById,
  updateMissionExpenseLine
} from 'src/services/me-others-line'
import { MissionExpenseLineFormData, QueryParams } from 'src/types/models/me-lines-other'

export const useMissionExpenseLines = (params?: Partial<QueryParams>) => {
  return useQuery({
    queryKey: ['mission-expense-lines', params],
    queryFn: () => fetchMissionExpenseLines(params)
  })
}

export const useMissionExpenseLineById = (id: string) => {
  return useQuery({
    queryKey: ['mission-expense-line', id],
    queryFn: () => getMissionExpenseLineById(id),
    enabled: !!id
  })
}

export const useCreateMissionExpenseLine = (
  options: { onSuccess?: () => void; onError?: (error: any) => void } = {}
) => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (data: MissionExpenseLineFormData) => createMissionExpenseLine(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['mission-expense-lines'] })
      options.onSuccess?.()
    },
    onError: options.onError
  })
}

export const useUpdateMissionExpenseLine = (
  options: { onSuccess?: () => void; onError?: (error: any) => void } = {}
) => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: Partial<MissionExpenseLineFormData> }) =>
      updateMissionExpenseLine(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['mission-expense-lines'] })
      options.onSuccess?.()
    },
    onError: options.onError
  })
}

export const useDeleteMissionExpenseLine = (
  options: { onSuccess?: () => void; onError?: (error: any) => void } = {}
) => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (id: string) => deleteMissionExpenseLine(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['mission-expense-lines'] })
      options.onSuccess?.()
    },
    onError: options.onError
  })
}
