import { MutateOptions, useMutation, useQuery } from '@tanstack/react-query'
import {
  createMissionExpenseLine,
  deleteMissionExpenseLine,
  updateMissionExpenseLine
} from 'src/services/me-others-line'
import {
  fetchMissionExpensesOthers,
  getMissionExpenseOtherById,
  createMissionExpenseOther,
  validateMissionExpensesOthers,
  rejectMissionExpensesOthers,
  updateMissionExpenseOther,
  deleteMissionExpenseOtherLine,
  deleteMissionExpenseOther
} from 'src/services/mission-expense-others'
import { MissionExpenseOtherFormData, MissionExpenseOtherParams } from 'src/types/models/mission-expense-other'
import moment from 'moment'

export const useMissionExpensesOthers = (params: Partial<MissionExpenseOtherParams>) => {
  return useQuery({
    queryKey: ['mission-expenses-others', params],
    queryFn: () => fetchMissionExpensesOthers(params)
  })
}

export const useMissionExpenseOtherById = (id: string) => {
  return useQuery({
    queryKey: ['mission-expense-other', id],
    queryFn: () => getMissionExpenseOtherById(id),
    enabled: !!id
  })
}

export const useCreateMissionExpenseOther = (options?: MutateOptions<any, any, MissionExpenseOtherFormData>) => {
  return useMutation({
    mutationFn: async (data: MissionExpenseOtherFormData) => {
      const res = await createMissionExpenseOther(data)

      if (data.validation) {
        if (data.validation === 'REJETEE') {
          await rejectMissionExpensesOthers(res.id, data.validationReason!)
        } else if (data.validation === 'VALIDEE') {
          await validateMissionExpensesOthers(res.id)
        }
      }

      return res
    },
    ...options
  })
}

export const useDeleteMissionExpenseOther = (options?: MutateOptions<any, any, any>) => {
  return useMutation({
    mutationFn: ({ id }: { id: string }) => deleteMissionExpenseOther(id),
    ...options
  })
}

export const useDeleteMissionExpenseOtherLine = (options?: MutateOptions<any, any, any>) => {
  return useMutation({
    mutationFn: ({ id }: { id: string }) => deleteMissionExpenseOtherLine(id),
    ...options
  })
}

export const useUpdateMissionExpenseOther = (options?: MutateOptions<any, any, any>) => {
  return useMutation({
    mutationFn: async ({ id, data }: { id: string; data: MissionExpenseOtherFormData }) => {
      const res = await updateMissionExpenseOther(id, data)

      if (data.validation) {
        if (data.validation === 'REJETEE') {
          await rejectMissionExpensesOthers(res.id, data.validationReason!)
        } else if (data.validation === 'VALIDEE') {
          await validateMissionExpensesOthers(res.id)
        }
      }

      return res
    },
    ...options
  })
}
