import { MutateOptions, useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import {
  createCustomer,
  deleteCustomer,
  deleteSelectedCustomers,
  fetchCustomers,
  getCustomerById,
  updateCustomer
} from 'src/services/customer'
import { Customer, CustomerParams, CustomerRequestPayload } from 'src/types/models/customer'

export const useCustomers = (params: Partial<CustomerParams>) => {
  return useQuery({
    queryKey: ['customers', params],
    queryFn: () => fetchCustomers(params)
  })
}

export const useCustomerById = (id: string) => {
  return useQuery<Customer>({
    queryKey: ['customer', id],
    queryFn: () => getCustomerById(id),
    enabled: !!id
  })
}

export const useCreateCustomer = (options?: MutateOptions<Customer, Error, CustomerRequestPayload>) => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (data: CustomerRequestPayload) => createCustomer(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['customers'] })
    },
    ...options
  })
}

export const useDeleteSelectedCustomers = (options?: MutateOptions<any, any, string[]>) => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (ids: string[]) => deleteSelectedCustomers(ids),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['customers'] })
    },
    ...options
  })
}

export const useUpdateCustomer = (
  options?: MutateOptions<Customer, Error, { id: string; data: Partial<CustomerRequestPayload> }>
) => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: Partial<CustomerRequestPayload> }) => updateCustomer(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['customers'] })
    },
    ...options
  })
}

export const useDeleteCustomer = (options?: MutateOptions<void, Error, string>) => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (id: string) => deleteCustomer(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['customers'] })
    },
    ...options
  })
}
