import moment from 'moment'

export const getNotificationTypeLabel = (type: string) => {
  switch (type) {
    case 'NEW':
      return 'Nouveau'
    case 'UPDATE':
      return 'Mise à jour'
    default:
      return type
  }
}

export function formateTableData(date?: string) {
  return moment(date).format('LL')
}

export function formateTime(timeString?: string) {
  return moment(timeString).format('hh:mm A')
}

export function encodeBase64(str1: string, str2: string) {
  return btoa(`${str1}:${str2}`)
}
