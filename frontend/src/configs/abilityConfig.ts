import { AbilityBuilder, createMongoAbility } from '@casl/ability'
import { UserPermissions } from 'src/@core/types/auth'

export type Actions = 'create' | 'read' | 'update' | 'delete' | 'manage' | 'validate' | 'alert' | 'edit' | 'stop'
export type Subjects = string

export function defineAbilityFor(permissions: UserPermissions | null) {
  const { can, cannot, build } = new AbilityBuilder(createMongoAbility)

  if (!permissions) {
    cannot('manage', 'all')

    return build()
  }

  // Iterate through all permission categories
  Object.entries(permissions).forEach(([module, modelPermissions]) => {
    modelPermissions.forEach(({ model, permission }) => {
      // Add explicit permissions
      permission.forEach(action => {
        can(action, `${module}:${model}`)
      })

      // If user has update permission, automatically grant read permission
      if (permission.includes('update') && !permission.includes('read')) {
        can('read', `${module}:${model}`)
      }
    })
  })

  return build()
}
