html,
body {
  min-height: 100%;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

#__next {
  height: 100%;
}

.react-hot-toast-container {
  z-index: 1000 !important;
}

code {
  font-family: 'Inter', sans-serif, -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu',
    'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue';
  padding: 0.1rem 0.4rem;
  font-size: 90%;
  color: #d400ff;
  border-radius: 0.1335rem;
}

code:not([class*='language-']):before,
code:not([class*='language-']):after {
  content: '`';
}

code[class*='language-'] {
  padding: 0;
}


@media print {

  /* Hide all elements except the printable content */
  body * {
    visibility: hidden;
  }

  /* Show only the paper content */
  .MuiPaper-root * {
    visibility: visible;
  }

  /* Remove any margins and padding */
  @page {
    padding: 0;
    size: auto;
    margin: 0;
    orientation: portrait;
  }

  /* Ensure white background */
  body {
    background-color: white;
    -webkit-print-color-adjust: exact;
    print-color-adjust: exact;
  }

  /* Hide buttons and non-printable elements */
  button,
  .no-print {
    display: none;
  }

  /* Remove box shadows when printing */
  .MuiPaper-root {
    box-shadow: none;
  }
}

/* Add styles for PDF generation */
.pdf-container {
  width: 100%;
  overflow-x: hidden;
}

body {
  -webkit-print-color-adjust: exact;
  print-color-adjust: exact;
}

#printable-area {
  width: 100%;
  height: 100%;
  page-break-after: always;
}





.gjs-block {
  width: auto;
  height: auto;
  min-height: 40px;
  padding: 10px;
  margin-bottom: 10px;
  text-align: center;
  cursor: grab;
  transition: all 0.2s ease;
}

.gjs-block:hover {
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.16), 0 3px 6px rgba(0, 0, 0, 0.23);
}

.gjs-block-category {
  font-size: 14px;
  font-weight: bold;
  margin: 10px 0;
  padding: 5px;
  background-color: #f5f5f5;
  border-radius: 3px;
}

/* Custom editor styles */
.gjs-editor {
  background-color: #f5f5f5;
}

.gjs-cv-canvas {
  background-color: #fff;
  border: 1px solid #ddd;
}

.gjs-pn-panel {
  padding: 5px;
}

.gjs-pn-buttons {
  justify-content: center;
}

.gjs-pn-btn {
  margin: 0 2px;
  padding: 5px;
  font-size: 16px;
}

.gjs-pn-btn.gjs-pn-active {
  background-color: #ddd;
}

.gjs-layer {
  padding: 5px;
}

.gjs-layer-title {
  font-size: 13px;
}

.gjs-layer-count {
  font-size: 11px;
}

.gjs-sm-sector {
  margin-bottom: 10px;
}

.gjs-sm-title {
  font-size: 13px;
  font-weight: bold;
  padding: 5px;
  background-color: #f5f5f5;
}

.gjs-sm-property {
  padding: 5px;
}

.gjs-sm-label {
  font-size: 12px;
}