/* eslint-disable @typescript-eslint/no-var-requires */
const path = require('path')

/** @type {import('next').NextConfig} */

// Remove this if you're not using Fullcalendar features

module.exports = {
  trailingSlash: true,

  transpilePackages: ['csv-import-react', '@mui/material', '@mui/system', '@mui/icons-material', 'react-datepicker'],
  reactStrictMode: false,
  webpack: (config, { isServer }) => {
    config.resolve.alias = {
      ...config.resolve.alias,
      apexcharts: path.resolve(__dirname, './node_modules/apexcharts-clevision')
    }
    config.resolve.alias['csv-import-react'] = require.resolve('csv-import-react')

    if (!isServer) {
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
        path: false
      }
    }

    config.resolve.extensionAlias = {
      '.js': ['.js', '.jsx', '.ts', '.tsx'],
      '.mjs': ['.mjs', '.mjsx', '.mts', '.mtsx']
    }

    // config.module.rules.push({
    //   test: /node_modules\/csv-import-react\/build\/index\.js$/
    // })

    return config
  }
}
